// 必须在首行添加，已保障 jsapi 支持 Iframe 逻辑正常处理
import './supportIframe';
import '@ali/dingtalk-jsapi/entry/union';

import { getJSAPI, isDingTalk } from '@ali/we-jsapi';
import { env as NEnv, compareVersion as NCompareVersion, on as NOn, off as NOff } from '@ali/dingtalk-jsapi/core';
import NSetNavLeft from '@ali/dingtalk-jsapi/api/biz/navigation/setLeft';
import { getENV as NGetENV } from '@ali/dingtalk-jsapi/lib/env';
import NHideBar from '@ali/dingtalk-jsapi/api/biz/navigation/hideBar';
import { getChannel as NGetChannel } from '@ali/dingtalk-jsapi/plugin/uniEvent';
import NOpenLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import NScan from '@ali/dingtalk-jsapi/api/biz/util/scan';
import NGoBack from '@ali/dingtalk-jsapi/api/biz/navigation/back';
import NDdlwp from '@ali/dingtalk-jsapi/api/internal/request/lwp';
import NInvokeWorkbench from '@ali/dingtalk-jsapi/api/biz/util/invokeWorkbench';
import { default as nativeOpen } from '@ali/dingtalk-jsapi/api/biz/util/open';
import NRotateView from '@ali/dingtalk-jsapi/api/device/screen/rotateView';
import { ddSdk as NDdSdk } from '@ali/dingtalk-jsapi/lib/ddSdk';
import NToast from '@ali/dingtalk-jsapi/api/device/notification/toast';
import NResetView from '@ali/dingtalk-jsapi/api/device/screen/resetView';
import NFetchImageData from '@ali/dingtalk-jsapi/api/biz/util/fetchImageData';
import NGetData from '@ali/dingtalk-jsapi/api/internal/clipboardData/getData';
import NClose from '@ali/dingtalk-jsapi/api/biz/navigation/close';
import NQuit from '@ali/dingtalk-jsapi/api/biz/navigation/quit';
import NExecuteSql from '@ali/dingtalk-jsapi/api/internal/sqlite/executeSql';
import NOpenSqlite from '@ali/dingtalk-jsapi/api/internal/sqlite/open';
import NUt from '@ali/dingtalk-jsapi/api/biz/util/ut';
import NSetTitle from '@ali/dingtalk-jsapi/api/biz/navigation/setTitle';
import NPreviewImage from '@ali/dingtalk-jsapi/api/biz/util/previewImage';
import DGeolocation from '@ali/dingtalk-jsapi/api/device/geolocation/get';
import { default as NVipAlarm } from '@ali/dingtalk-jsapi/api/biz/util/vip';
import { default as downloadFileNative } from '@ali/dingtalk-jsapi/api/biz/util/downloadFile';
import DChooseConversation from '@ali/dingtalk-jsapi/api/biz/chat/chooseConversation';
import NSaveImage from '@ali/dingtalk-jsapi/api/biz/util/saveImage';
import NGetCurrentUserInfo from '@ali/dingtalk-jsapi/api/internal/user/getCurrentUserInfo';
import NGetPhoneInfo from '@ali/dingtalk-jsapi/api/device/base/getPhoneInfo';
import NGetCurrentOrgId from '@ali/dingtalk-jsapi/api/internal/util/getCurrentOrgId';
import NCreateDing from '@ali/dingtalk-jsapi/api/biz/ding/create';
import NWatermarkCamera from '@ali/dingtalk-jsapi/api/biz/util/watermarkCamera';
import DisableWebViewBounce from '@ali/dingtalk-jsapi/api/ui/webViewBounce/disable';

export const on = getJSAPI('on', NOn);
export const off = getJSAPI('off', NOff);
export const setNavLeft = getJSAPI('setNavLeft', NSetNavLeft);
export const getENV = getJSAPI('getENV', NGetENV);
export const hideBar = getJSAPI('hideBar', NHideBar);
export const getChannel = getJSAPI('getChannel', NGetChannel);
export const compareVersion = getJSAPI('compareVersion', NCompareVersion);
export const goBack = getJSAPI('goBack', NGoBack);
export const openLink = getJSAPI('openLink', NOpenLink);
export const scan = getJSAPI('scan', NScan);
export const open = getJSAPI('open', nativeOpen);
export const ddlwp = getJSAPI('ddlwp', NDdlwp);
export const lwp = getJSAPI('lwp', NDdlwp);
export const invokeWorkbench = getJSAPI('invokeWorkbench', NInvokeWorkbench);
export const env = getJSAPI('env', NEnv);
export const rotateView = getJSAPI('rotateView', NRotateView);
export const ddSdk = getJSAPI('ddSdk', NDdSdk).getExportSdk();
export const toast = getJSAPI('toast', NToast);
export const resetView = getJSAPI('resetView', NResetView);
export const fetchImageData = getJSAPI('fetchImageData', NFetchImageData);
export const getClipboardData = getJSAPI('getClipboardData', NGetData);
export const close = getJSAPI('close', NClose);
export const quit = getJSAPI('quit', NQuit);
export const sqlite = getJSAPI('sqlite', { open: NOpenSqlite, executeSql: NExecuteSql });
export const ut = getJSAPI('ut', NUt);
export const setTitle = getJSAPI('setTitle', NSetTitle);
export const previewImage = getJSAPI('previewImage', NPreviewImage);
export const vipAlarm = getJSAPI('vipAlarm', NVipAlarm);
export const downloadFile = getJSAPI('downloadFile', downloadFileNative);
export const getGeolocation = getJSAPI('getGeolocation', DGeolocation);
export const chooseConversation = getJSAPI('chooseConversation', DChooseConversation);
export const saveImage = getJSAPI('saveImage', NSaveImage);
export const createDing = getJSAPI('createDing', NCreateDing);
export const watermarkCamera = getJSAPI('watermarkCamera', NWatermarkCamera);
export const disableWebViewBounce = getJSAPI('disableWebViewBounce', DisableWebViewBounce);

// #region 当前仅提供给移动端文字 SDK 使用
const getCurrentUserInfo = getJSAPI('getCurrentUserInfo', NGetCurrentUserInfo);
const getPhoneInfo = getJSAPI('getPhoneInfo', NGetPhoneInfo);
const getCurrentOrgId = getJSAPI('getCurrentOrgId', NGetCurrentOrgId);
const openSqlite = getJSAPI('openSqlite', NOpenSqlite);
const executeSqlite = getJSAPI('executeSqlite', NExecuteSql);
// #endregion

interface AccelerometerEvent {
  x?: number;
  y?: number;
  z?: number;
}

export const onAccelerometerChange = (callback: (event: Required<AccelerometerEvent>) => void) => {
  ddSdk._invoke('internal.accelerometer.onAccelerometerChange', {
    onSuccess: (event: AccelerometerEvent) => {
      if (!event || event.x == null) {
        return;
      }
      callback(event as Required<AccelerometerEvent>);
    },
  });
};

export const offAccelerometerChange = () => {
  ddSdk._invoke('internal.accelerometer.offAccelerometerChange');
};

// 目前在表单填写页+移动端场景下，注入 JSAPI 给文字 SDK 消费
export const initJSAPI = () => {
  /* 当前窗口是否在顶层（不在iframe里面） */
  const isThisTopWindow = window === window.top;

  if (isThisTopWindow && isDingTalk()) {
    // 非拼写错误，历史问题，保持兼容
    // @ts-ignore 忽略全局变量声明，避免 tsconfig 配置扩散
    window.dd_jspi = {
      on,
      off,
      setNavLeft,
      getENV,
      hideBar,
      getChannel,
      compareVersion,
      goBack,
      openLink,
      scan,
      ddlwp,
      lwp,
      invokeWorkbench,
      env,
      rotateView,
      // 文字 sdk 需要原值
      ddSdk: NDdSdk,
      toast,
      resetView,
      fetchImageData,
      getClipboardData,
      close,
      quit,
      ut,
      setTitle,
      previewImage,
      vipAlarm,
      downloadFile,
      getGeolocation,
      chooseConversation,
      saveImage,
      getCurrentUserInfo,
      isDingTalk,
      getPhoneInfo,
      getCurrentOrgId,
      openSqlite,
      executeSqlite,
      createDing,
      watermarkCamera,
    };
  }
};

export { isDingTalk } from '@ali/we-jsapi';
export { _invoke } from '@ali/dingtalk-jsapi/core';
export * as envV2 from '@ali/dingtalk-jsapi/plugin/environmentV2';
