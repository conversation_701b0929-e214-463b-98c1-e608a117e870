{"name": "@ali/notable-jsapi-adaptor", "version": "0.36.0", "main": "src/index.ts", "sideEffects": ["src/initDomain.ts", "src/supportIframe.ts"], "publishConfig": {"registry": "https://registry.anpm.alibaba-inc.com"}, "author": "alidocs panda", "scripts": {"reinstall": "rm -rf node_modules pnpm-lock.yaml && pnpm i", "clean": "rm -rf pkg build", "build": "we build-component", "test": "we jest", "typechecking": "we typechecking", "pub": "npm run build && tnpm publish pkg", "coverage": "we jest --coverage --passWithNoTests", "lint": "we eslint --quiet"}, "dependencies": {"@ali/we-jsapi": "0.1.13", "@ali/dingtalk-jsapi": "2.15.5", "@ali/notable-common": "0.36.0"}, "devDependencies": {}}