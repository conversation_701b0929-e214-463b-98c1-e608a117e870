{"name": "@ali/notable-model", "version": "0.36.0", "main": "src/index.ts", "sideEffects": ["./src/locale/*.ts", "./locale/*.js"], "publishConfig": {"registry": "https://registry.anpm.alibaba-inc.com"}, "author": "alidocs panda", "scripts": {"reinstall": "rm -rf node_modules pnpm-lock.yaml && pnpm i", "clean": "rm -rf pkg build", "start": "we start-storybook -p 9000", "build-storybook": "we build-storybook", "build": "we build-component && rollup -c ./rollup.config.js && cp -r assets pkg/assets", "test": "we jest", "deploy": "we deploy-pages -d build/ -b demo -m 'deloy. to #xxx'", "site": "pnpm build-storybook && pnpm deploy", "typechecking": "we typechecking", "pub": "pnpm build && tnpm publish pkg", "coverage": "we jest --coverage --passWithNoTests", "lint": "we eslint --quiet", "update-i18n": "update-i18n ./localesKeys.js ./src/i18nResources -w 'zh_CN|en_US|zh_TW|zh_HK|ja_JP' --moreLangs ko_KR,tr_TR,pt_BR,th_TH,id_ID,ms_MY -l ts -n notable-sdk"}, "dependencies": {"@ali/notable-common": "0.36.0", "@ali/notable-i18n": "0.36.0", "@ali/zongheng-common": "2.22.250312-beta.6251617", "@ali/zongheng-formula": "2.22.250312-beta.6251617", "@ali/zongheng-formula-functions": "2.22.250312-beta.6251617", "@types/lodash-es": "4.17.7", "dayjs": "1.10.6", "debug": "4.3.2", "decimal.js-light": "^2.5.1", "immer": "^9.0.5", "object-sizeof": "1.5.3", "hash.js": "1.1.7"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.14.5", "@babel/plugin-proposal-decorators": "^7.14.5", "@babel/preset-react": "^7.16.7", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^20.0.0", "@rollup/plugin-node-resolve": "^13.0.4", "@types/debug": "^4.1.5", "@types/lodash-es": "4.17.7", "faker": "^5.1.0", "rollup": "^2.56.3", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-ts-treeshaking": "^1.0.2"}, "peerDependencies": {"@babel/runtime": "7.x", "immer": ">= 8 | >=9", "lodash-es": "*"}}