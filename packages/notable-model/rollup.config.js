import commonjs from '@rollup/plugin-commonjs';
import { nodeResolve } from '@rollup/plugin-node-resolve';
import tsTreeShaking from 'rollup-plugin-ts-treeshaking';
import { terser } from 'rollup-plugin-terser';
import { babel } from '@rollup/plugin-babel';

const protoMinimizer = require('../../scripts/rollup-plugins/proto-minimizer');

export default [{
  input: 'src/index.ts',
  output: {
    file: 'pkg/dist-node/index.js',
    format: 'cjs',
    sourcemap: true,
  },
  external: [],
  treeshake: {
    moduleSideEffects: false,
    unknownGlobalSideEffects: false,
    tryCatchDeoptimization: false,
    propertyReadSideEffects: false,
  },
  plugins: [
    protoMinimizer(),
    nodeResolve({
      extensions: ['.ts', '.tsx', '.js', '.jsx'],
      browser: false,
      preferBuiltins: false,
    }),
    commonjs(),
    babel({
      babelHelpers: 'bundled',
      extensions: ['.ts', '.tsx'],
      plugins: [
        ['@babel/plugin-proposal-decorators', { legacy: true }],
        '@babel/plugin-proposal-class-properties',
      ],
      presets: ['@babel/preset-env', '@babel/preset-typescript', '@babel/preset-react'],
    }),
    tsTreeShaking(),
    terser({
      output: {
        comments: false,
      },
    }),
  ],
}];
