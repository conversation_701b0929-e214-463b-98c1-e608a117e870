import { cloneDeep, filter, find, get, set } from 'lodash-es';
import { logger, WidgetTypeEnums, thousandSample, IFConfig, FieldTypes, DateConfig, IBaseField } from '@ali/notable-common';
import type { SheetModel } from '../sheet';
import { FIELD_DECORATOR_DEFAULT_ENUM_VALUE } from '../types';
import type { AssociationsConfig, AssociationsField, FieldId, FieldDTO, SelectConfig } from './types';

/**
 * 向下兼容 field 类型更改
 * @param type
 * @returns
 */
export function BC_mapToRealType(type: string): string {
  // 演进中将单行文本和多行文本合并为一个（text）
  if (['text', 'textarea'].includes(type)) {
    // 监控是否还存在 textarea 存在
    if (type === 'textarea') {
      thousandSample(() => logger.log('model_textarea', type));
    }

    return 'text';
  }

  return type;
}

/**
 * 修复字段配置中的脏数据
 */
export function BC_adjustFieldConfig<T extends FieldTypes = FieldTypes>(type: T, config: IBaseField['config']): IBaseField['config'] {
  if (type === 'date') {
    const format = (config?.renderFieldConfig?.props as unknown as DateConfig)?.format;
    if (!format) {
      const adjustedConfig = cloneDeep(config);
      set(adjustedConfig, 'renderFieldConfig.props.format', 'YYYY-MM-DD');
      return adjustedConfig;
    }
  }

  // 修正字段模板产生的重复单选/多选选项
  // 仅处理FIELD_DECORATOR_DEFAULT_ENUM_VALUE和其它enum重复的case，移除自动添加的默认选项。
  if ((type === 'select' || type === 'multiSelect') && config.decorator) {
    const fieldConfig = config?.renderFieldConfig?.props as SelectConfig;
    const enums = fieldConfig?.enums;
    if (enums && Array.isArray(enums)) {
      const defaultOption = enums.find(({ value }) => value === FIELD_DECORATOR_DEFAULT_ENUM_VALUE);
      if (defaultOption) {
        const sameIdOption = enums.find(({ id, value }) => id === defaultOption.id && value !== FIELD_DECORATOR_DEFAULT_ENUM_VALUE);
        if (sameIdOption) {
          logger.log('field_decorator_default_option_id_duplicated');
          // 其它option的id和defaultOption相同时，移除defaultOption
          const newEnums = enums.filter((e) => e.value !== FIELD_DECORATOR_DEFAULT_ENUM_VALUE);
          const adjustedConfig = cloneDeep(config);
          set(adjustedConfig, 'renderFieldConfig.props.enums', newEnums);
          return adjustedConfig;
        }
      }
    }
  }

  return {
    ...config,
  };
}

// #region AssociationField helpers
export function filterAssociationsFields(fields: Record<FieldId, FieldDTO>) {
  return filter(
    fields, (field: FieldDTO) => isAssociationField(field) && !field.deleted,
  ) as AssociationsField[];
}

export function isAssociationField(field: FieldDTO): field is AssociationsField {
  return field.type === WidgetTypeEnums.association;
}

export const getAssociationProps = (assoField: AssociationsField): AssociationsConfig => {
  const props = get(assoField.config, 'renderFieldConfig.props') as unknown as AssociationsConfig;
  if (!props) {
    throw new Error('[getAssociationProps] association not found');
  }
  return props;
};

export const isValidAssociationProps = (arg: unknown): arg is AssociationsConfig => {
  if (arg && typeof arg === 'object') {
    const asso = arg as AssociationsConfig;
    return (
      typeof asso.sheetId === 'string' && asso.sheetId.length > 0
      && typeof asso.pairFieldId === 'string' && asso.pairFieldId.length > 0
    );
  }
  return false;
};
// #endregion

/**
 * 判断是否是关联内置数据字段
 */
export function isRefBuildInPropertyField(field: Pick<FieldDTO, 'type'>) {
  return [
    WidgetTypeEnums.creator,
    WidgetTypeEnums.updater,
    WidgetTypeEnums.updatedTime,
    WidgetTypeEnums.createdTime,
  ].includes(field.type as WidgetTypeEnums);
}

/** 兼容单选多选在关联转换后引入额对象选项 case */
export function BC_SelectInvalidOptions(type: string, config: Record<string, unknown>) {
  if (type === WidgetTypeEnums.select || type === WidgetTypeEnums.multiSelect) {
    const options = get(config, 'renderFieldConfig.props.enums');

    if (Array.isArray(options) && options) {
      return set(cloneDeep(config), 'renderFieldConfig.props.enums', options.filter((i) => {
        return typeof i?.value !== 'object';
      }));
    }
  }

  return config;
}

export function BC_association_miss_pairfieldId(
  fieldId: string, type: string, config: Record<string, unknown>, sheet: SheetModel,
) {
  if (type !== 'association') return config;
  const props = get(config, 'renderFieldConfig.props') as unknown as AssociationsConfig;
  if (!props) return config;
  const { pairFieldId, sheetId } = props;
  if (typeof pairFieldId === 'string' && pairFieldId.length > 0) return config;
  const pairSheet = sheet.parent.getSheet(sheetId);
  if (!pairSheet) return config;
  const field = find(pairSheet.fieldMap, (item) => {
    if (item.type !== 'association') return false;
      const fieldProps = get(item.config, 'renderFieldConfig.props') as unknown as AssociationsConfig;
      return fieldProps.pairFieldId === fieldId;
  });

  if (!field) return config;

  return set(cloneDeep(config), 'renderFieldConfig.props.pairFieldId', field.id);
}
