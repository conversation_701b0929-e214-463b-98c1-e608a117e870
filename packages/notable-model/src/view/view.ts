/* eslint-disable no-bitwise */
import { cloneDeep, isNil, isEqual, get } from 'lodash-es';
import { createEvent } from '@ali/notable-common';
import type { FieldVM, WithFieldTypeFilter } from '@ali/notable-common';
import { Cache } from '../cache';
import { shallowEqualMemoize } from '../cache/cache';
import { clearCache } from '../cache/decorator';
import type {
  UpdateViewPayload,
} from '../operations/types';
import type { FieldId } from '../field/types';
import { withFieldTypeRules } from '../utils';
import { isAssociationField, isRefBuildInPropertyField } from '../field';
import type { SheetModel } from '../sheet';
import { WithFieldTypeGroup, WithFieldTypeSort } from '../calculate/types';
import { LocalView, ViewDTO, IModelView } from './types';
import {
  BC_FormDesignerViewDisplayFilters,
  BC_hasDateRangeViewDirtyData,
  BC_hideFields,
} from './helper';
import {
  deleteViewField,
  insertViewField,
  moveViewField,
  updateView,
} from './viewImplUtils';

const clearViewCache = clearCache<View>('cache');

export class View {
  private meta: IModelView;
  private originalMeta?: IModelView;

  flags: ViewDTO['flags'];
  id: ViewDTO['id'];
  timestamp: ViewDTO['timestamp'];
  filter: ViewDTO['filter'];
  sort: ViewDTO['sort'];
  group: ViewDTO['group'];
  aggregate: ViewDTO['aggregate'];
  custom: ViewDTO['custom'];
  type: ViewDTO['type'];
  subType: ViewDTO['subType'];
  name: ViewDTO['name'];
  columns: ViewDTO['columns'];
  createTime: ViewDTO['createTime'];
  creator: ViewDTO['creator'];
  description: ViewDTO['description'];
  recordOpenMethod: ViewDTO['recordOpenMethod'];
  conditionalFormats: ViewDTO['conditionalFormats'];
  // meta 修改顺序
  lastUpdatePaths: string[] = [];

  cache = Cache.create<Readonly<LocalView>>(() => {
    this.parent.cache.clear();
  });

  destroy = createEvent();

  constructor(readonly parent: SheetModel, json: ViewDTO) {
    // 需要将计算属性的 enumerable 置为 true，保障业务在使用时使用 ...操作符能正常获取到值
    Object.defineProperties(this, {
      id: { get() { return this.meta.id; }, enumerable: true },
      timestamp: { get() { return this.meta.timestamp; }, enumerable: true },
      filter: { get() { return this.meta.filter; }, enumerable: true },
      sort: { get() { return this.meta.sort; }, enumerable: true },
      group: { get() { return this.meta.group; }, enumerable: true },
      conditionalFormats: { get() { return this.meta.conditionalFormats; }, enumerable: true },
      aggregate: { get() { return this.meta.aggregate; }, enumerable: true },
      custom: { get() { return this.meta.custom; }, enumerable: true },
      type: { get() { return this.meta.type; }, enumerable: true },
      subType: { get() { return this.meta.subType; }, enumerable: true },
      name: { get() { return this.meta.name; }, enumerable: true },
      columns: { get() { return this.meta.columns; }, enumerable: true },
      flags: { get() { return this.meta.flags; }, enumerable: true },
      createTime: { get() { return this.meta.createTime; }, enumerable: true },
      creator: { get() { return this.meta.creator; }, enumerable: true },
      description: { get() { return this.meta.description; }, enumerable: true },
      recordOpenMethod: { get() { return this.meta.recordOpenMethod; }, enumerable: true },
    });

    this.meta = ({
      ...json,
      // 兼容下极端情况引入的脏数据
      sort: json.sort.filter((item) => !isNil(item)),
      custom: BC_FormDesignerViewDisplayFilters(
        BC_hideFields(
          BC_hasDateRangeViewDirtyData(json.type, { ...json.custom }),
        ),
        json.type,
      ),
    } as IModelView);
  }

  toJSON(): Readonly<ViewDTO> { return this.meta; }

  toLocalJSON(): Readonly<LocalView> {
    const cacheValue = this.cache.value();

    if (cacheValue) {
      return cacheValue;
    }

    const columns = this.getColumnsJSON();

    const view = {
      id: this.id,
      name: this.name,
      type: this.type as ViewDTO['type'],
      subType: this.subType,
      timestamp: this.timestamp,
      // 列头信息
      columns,
      // 查询条件
      filter: this.filter,
      flags: this.flags,
      group: this.group,
      sort: this.sort,
      conditionalFormats: this.conditionalFormats,
      aggregate: this.aggregate,
      // 用户自定义数据
      custom: this.custom,
      createTime: this.createTime,
      creator: this.creator,
      description: this.description,
      recordOpenMethod: this.recordOpenMethod,
    } as LocalView;

    if (view.type === 'Gallery' || view.type === 'Gantt') {
      if (view.custom) {
        if (!view.custom.hiddenFields) view.custom.hiddenFields = {};
        columns.forEach((column) => {
          if (!view.custom) return;
          if (column.isPrimary) {
            view.custom.hiddenFields![column.id] = false;
            return;
          }
          if (view.custom.hiddenFields![column.id] === undefined) {
            view.custom.hiddenFields![column.id] = true;
          }
        });
      }
    }

    return this.cache.set(view);
  }

  @clearViewCache
  update(args: Pick<UpdateViewPayload, 'paths' | 'value'>) {
    this.meta = updateView(this.meta, args);
    if (this.originalMeta) {
      this.originalMeta = updateView(this.originalMeta, args);
    }
  }

  // 是否有未同步的配置
  hasTempConfig(type: string) {
    const { originalMeta, meta } = this;
    if (!originalMeta) {
      return false;
    }
    return !isEqual(get(meta, type), get(originalMeta, type));
  }

  // 获取未同步的配置
  getTempConfig(type: string) {
    if (!this.hasTempConfig(type)) {
      return undefined;
    }
    return get(this.meta, type);
  }

  @clearViewCache
  updateTempView(args: Pick<UpdateViewPayload, 'paths' | 'value'>) {
    if (!this.originalMeta) {
      this.originalMeta = cloneDeep(this.meta);
    }
    this.meta = updateView(this.meta, args);
    const path = args.paths.join('.');
    this.lastUpdatePaths = this.lastUpdatePaths.filter((p) => p !== path);
    this.lastUpdatePaths.unshift(path);
  }

  @clearViewCache
  deleteTempView() {
    if (this.originalMeta) {
      this.meta = this.originalMeta;
      this.originalMeta = undefined;
    }
  }

  @clearViewCache
  moveTempViewFields(id: FieldId, index: number, target: number) {
    if (!this.originalMeta) {
      this.originalMeta = cloneDeep(this.meta);
    }
    this.meta = moveViewField(this.meta, id, index, target);
  }

  @clearViewCache
  insertColumn(id: FieldId, index: number) {
    this.meta = insertViewField(this.meta, id, index);
    if (this.originalMeta) {
      this.originalMeta = insertViewField(this.originalMeta, id, index);
    }
  }

  @clearViewCache
  moveColumn(id: FieldId, index: number, target: number) {
    this.meta = moveViewField(this.meta, id, index, target);
    if (this.originalMeta) {
      this.originalMeta = moveViewField(this.originalMeta, id, index, target);
    }
  }

  @clearViewCache
  deleteColumn(id: FieldId, _: number) {
    this.meta = deleteViewField(this.meta, id, _);
    if (this.originalMeta) {
      this.originalMeta = deleteViewField(this.originalMeta, id, _);
    }
  }

  hasRules() {
    return [this.filter, this.group, this.sort].some((item) => item.length > 0);
  }

  @shallowEqualMemoize
  private getColumnsJSON() {
    return this.columns
      .map((fieldId) => this.parent.fieldMap[fieldId].toLocalJSON())
      .filter((field) => !isAssociationField(field) || !field.hidden)
      .filter((field) => this.shouldFieldHiddenCuzCellVisibility(field));
  }

  get withFieldTypeRules(): {
    sort: WithFieldTypeSort[];
    filter: WithFieldTypeFilter[];
    group: WithFieldTypeGroup[];
  } {
    return {
      sort: withFieldTypeRules(this.parent.id, this.sort, this.parent.parent.getField),
      filter: withFieldTypeRules(this.parent.id, this.filter, this.parent.parent.getField),
      group: withFieldTypeRules(this.parent.id, this.group, this.parent.parent.getField),
    };
  }

  private shouldFieldHiddenCuzCellVisibility(field: FieldVM) {
    if (!this.parent.isFieldHiddenByCellVisibility(field.id)) {
      return true;
    }
    if (isRefBuildInPropertyField(field)) {
      return true;
    }
    return false;
  }
}
