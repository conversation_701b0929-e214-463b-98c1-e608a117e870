import { set, mapValues, forEach, remove, has, isEqual, difference, isNil, pick, filter } from 'lodash-es';
import { APIException } from '@ali/zongheng-common';
import {
  logger, CellDataType, generateSheetId,
  WithFieldTypeCellVisibility, isComputedField,
  SheetType, SheetId,
  Deferred,
  hasPhysicalSheetId,
  convertOperatorToUser,
  isUserEqual,
} from '@ali/notable-common';
import { IModelView } from '../view/types';
import { View } from '../view';
import { type IModelField, Field } from '../field';
import { RowDB } from '../row/db/RowDB';
import { Row } from '../row';
import { adjustMoveTarget } from '../utils/move';
import { Cache } from '../cache';
import { clearCache } from '../cache/decorator';
import { DelayAndMergeEmit, Event, sortArrayBasedOnOrder, sureExist } from '../utils';
import type { Workbook } from '../workbook/index';
import type { RecordDTO, ObjectCellValue, RowId } from '../row/types';
import type {
  AppendRowPayload,
  InsertRowPayload,
  LockCellPayload,
  MergeSliceDataToViewPayload,
  MoveRowPayload,
  UpdateFieldPayload,
  UpdateRowPayload,
  UpdateSheetPayload,
  VersionedRawDataOperation,
} from '../operations/types';
import type { FieldId, FieldDTO } from '../field/types';
import type { ViewDTO, ViewId } from '../view/types';
import { WorkbookEventType } from '../workbook/types';
import { publicSheet } from '../public/sheet';
import { builtInDataToCells, getRefBuiltInDataFields } from '../row/helper';
import type { PublicSheet } from '../public/types';
import { getValidSheetFields } from '../utils/validate';
import type { SheetDTO, SheetVM, SheetEvent } from './types';
import { SelectionManager } from './selection';
import { BC_adjustColumnsMiss, BC_adjustViewFirstColumnNotPrimaryField } from './helper';
import { SheetIndexWithRank } from './SheetIndexWithRank';
import { UserCache } from './UserCache';
import { RecordChangesets } from './RecordChangesets';
import { CellValueExtractorManager } from './CellValueExtractorManager';

export type RowData = Omit<UpdateRowPayload, 'sheetId' | 'preRowId' | 'operator'> & { operator?: ObjectCellValue };

export type { SelectionManager };

interface UpdateRowOptions {
  skipOperationStamp?: boolean;
}

const clearSheetCache = clearCache<SheetModel>('cache');

export class SheetModel extends Event<SheetEvent> implements SheetDTO {
  static create(...args: ConstructorParameters<typeof SheetModel>) {
    return new SheetModel(...args);
  }

  static createFromJSON(parent: Workbook, json: SheetDTO) {
    return new SheetModel(parent).fromJSON(json);
  }

  readonly loadedDefer = new Deferred<void>();

  id = generateSheetId();
  timestamp = Date.now();
  name = '';
  description: SheetDTO['description'];
  fieldMap: Record<FieldId, IModelField> = {};
  viewMap: Record<ViewId, IModelView> = {};
  views: SheetDTO['views'] = [];
  sheetType?: SheetType;
  targetSheetId?: SheetId;
  synced?: boolean;
  sheetIndex: SheetIndexWithRank | undefined;
  userCache: UserCache | undefined;
  snapshot: SheetDTO | undefined;
  config?: SheetDTO['config'];

  /** 锁定 cell 不可编辑，本地存储不协同存储 */
  selectionManager = SelectionManager.create();
  valueExtractor = new CellValueExtractorManager(this);

  /** 管理行级数据 */
  rowDB: RowDB;

  cache = Cache.create<Readonly<SheetVM>>(() => {
    this.parent.cache.clear();
    this.delayAndMergeEmit.emit('change', () => [] as []);
  });

  fullRawData = false;

  delayAndMergeEmit = new DelayAndMergeEmit(this);

  recordChangesets = new RecordChangesets();

  private _public: PublicSheet | null = null;

  private visibleFieldIds: Set<string> | undefined;

  readonly usingInMemoryDB: boolean;

  private pendingOps: VersionedRawDataOperation[] = [];

  private doApply: (ops: VersionedRawDataOperation[]) => void = () => {};

  pushPendingOps(ops: VersionedRawDataOperation[], doApply: (ops: VersionedRawDataOperation[]) => void) {
    for (const op of ops) {
      this.pendingOps.push(op);
    }
    this.doApply = doApply;
  }

  get loaded() {
    return this.loadedDefer.promise;
  }

  get public() {
    if (this._public === null) {
      this._public = publicSheet(this);
    }

    return this._public;
  }

  constructor(readonly parent: Workbook) {
    super();
    this.parent.on(WorkbookEventType.END_OF_OPERATION, () => {
      this.delayAndMergeEmit.run();
    });
    this.usingInMemoryDB = parent.usingInMemoryDB;
    this.rowDB = RowDB.create(this);
  }

  @clearSheetCache
  fromJSON(metaSheet: SheetDTO): SheetModel {
    const {
      fieldMap,
      viewMap,
      id,
      name,
      description,
      timestamp,
      views,
      rows,
      recordIds,
      userMap,
      sheetType,
      synced,
      targetSheetId,
      config,
    } = metaSheet;
    // 提前对 id 赋值保证后续取值正确
    this.id = id;
    this.name = name;
    this.description = description;
    this.timestamp = timestamp;
    this.sheetType = sheetType;
    this.targetSheetId = targetSheetId;
    this.synced = synced;
    this.config = config;
    // 打点，寻找 columns 异常视图并上报
    const {
      viewMap: adjustViewMap,
      fieldMap: adjustFieldMap,
    } = BC_adjustColumnsMiss(viewMap, fieldMap);

    this.fieldMap = mapValues(adjustFieldMap, (metaField) => new Field(this, metaField) as IModelField);
    this.valueExtractor.reset(fieldMap);
    const realViews = sortArrayBasedOnOrder(Object.keys(viewMap), views);

    // 清除被删除 view 的副作用
    this.views.forEach((item) => {
      if (!has(realViews, item)) { this.viewMap[item]?.destroy(); }
    });
    this.viewMap = mapValues(adjustViewMap, (metaView) => {
      const afterAdjustMetaView = BC_adjustViewFirstColumnNotPrimaryField(metaView, this.fieldMap);
      return (new View(this, afterAdjustMetaView)) as IModelView;
    });
    this.views = realViews.slice(0);

    // 在非独立存储下，给到的 rows 将被丢弃
    if (rows && this.parent.keepExistingRows) {
      // 这里的行数据是缓存在 cp 中的 30 行，大概率不是所有的行，不应该调用 setFullRawData 的，先增加 flag 临时区分
      this.setFullRawData(rows, undefined, false);
    }

    if (recordIds) {
      this.sheetIndex = new SheetIndexWithRank(recordIds);
    }
    if (userMap) {
      this.userCache = new UserCache(userMap);
    }

    if (hasPhysicalSheetId(sheetType)) {
      if (targetSheetId) {
        this.parent.updateLoopSheetRef(targetSheetId, id);
      }
    }

    this._public = null;
    return this;
  }

  getFieldDTOMap(BC_word?: boolean) {
    return mapValues(this.fieldMap, (field) => field.toJSON(BC_word));
  }

  toJSON(includeAllRows?: boolean, BC_word?: boolean): Readonly<SheetDTO> {
    const fieldMap = this.getFieldDTOMap(BC_word);
    const viewMap = mapValues(this.viewMap, (view) => view.toJSON());
    // 被删除和被关联到内置字段的都不被导出
    const validFields = getValidSheetFields(this.fieldMap);
    const rowsData: { rows?: RecordDTO[] } = {};

    if (includeAllRows || this.parent.keepExistingRows) {
      const rows = this.rowDB.getAllJSONRow(validFields);
      rowsData.rows = rows;
    }

    return {
      id: this.id,
      name: this.name,
      description: this.description,
      timestamp: this.timestamp,
      fieldMap,
      viewMap,
      views: this.views.slice(0),
      recordIds: this.sheetIndex?.generateOrderedKeys(),
      userMap: this.userCache?.toJSON(),
      sheetType: this.sheetType,
      targetSheetId: this.targetSheetId,
      synced: this.synced,
      config: this.config,
      ...rowsData,
    };
  }

  toLocalJSON(): Readonly<SheetVM> {
    const cacheValue = this.cache.value();

    if (cacheValue) {
      return cacheValue;
    }

    return this.cache.set({
      id: this.id,
      name: this.name,
      description: this.description,
      timestamp: this.timestamp,
      views: this.views.map((viewId) => this.viewMap[viewId]?.toLocalJSON()).filter((v) => !!v),
      selectionManager: this.selectionManager,
      sheetType: this.sheetType,
      synced: this.synced,
      targetSheetId: this.targetSheetId,
      config: this.config,
    });
  }

  getSheetJSONWithoutRows() {
    const fieldMap = mapValues(this.fieldMap, (field) => field.toJSON());
    const viewMap = mapValues(this.viewMap, (view) => view.toJSON());
    return {
      id: this.id,
      name: this.name,
      sheetType: this.sheetType,
      description: this.description,
      timestamp: this.timestamp,
      fieldMap,
      viewMap,
      views: this.views.slice(0),
      config: this.config,
    };
  }

  /**
   * 获取 View 实例
   * @param id
   * @returns
   */
  getView(id: ViewId): IModelView | undefined {
    return this.viewMap[id] as (IModelView | undefined);
  }

  getMetaView(id: ViewId) {
    return this.getView(id)?.toJSON();
  }

  /**
   * 获取 View 实例数组
   *
   * 注意：
   *    模型层中的allView包含个人视图，业务中请尽量使用ViewController中的数据，
   *    避免将无权限的个人视图显示出来；
   *
   * @param id
   * @returns
   */
  getAllView() {
    return this.views.map((viewId) => this.viewMap[viewId]);
  }

  getAllMetaView() {
    return this.views.map((viewId) => this.viewMap[viewId].toJSON());
  }

  getAllLocalView() {
    return this.views.map((viewId) => this.viewMap[viewId].toLocalJSON());
  }

  /**
   * 获取 FieldId 实例
   * @param id
   * @returns
   */
  getField(id: FieldId): IModelField | undefined {
    return this.fieldMap[id];
  }

  getAllFields(): IModelField[] {
    return Object.values(this.fieldMap).filter((field) => !field.deleted);
  }

  getMetaField(id: FieldId) {
    return this.getField(id)?.toJSON();
  }

  isFieldHiddenByCellVisibility(fieldId: FieldId) {
    return this.visibleFieldIds === undefined
      ? false
      : !this.visibleFieldIds.has(fieldId);
  }

  /**
   * 调用时确定本地一定应该存在 field 数据，并获取 dataType
   * @param fieldId
   */
  sureGetFieldDataType(fieldId: FieldId): CellDataType {
    return sureExist(this.getField(fieldId), '本地 field 信息丢失').dataType;
  }

  setCellVisibility(visibility?: WithFieldTypeCellVisibility) {
    // 如果所有 role 中都没有改列（单元格）的权限，则不显示该列。
    const visibleFieldIds = visibility?.reduce((fieldIds, vItem) => {
      if (fieldIds === 'all' || vItem.cells === undefined) {
        return 'all';
      }
      if (vItem.cells) {
        vItem.cells?.forEach((id) => fieldIds.add(id));
      }
      return fieldIds;
    }, new Set<FieldId>() as Set<FieldId> | 'all');

    this.visibleFieldIds = visibleFieldIds === 'all'
      ? undefined
      : visibleFieldIds;

    this.getAllView().forEach((view) => view.cache.clear());

    this.rowDB.setCellVisibility(visibility);
  }

  getRowCount() {
    return this.rowDB.getRowCount();
  }

  /**
   * 获取可见的 row 实例
   * @param rowId
   * @returns
   */
  getRow(rowId: RowId): Row | undefined {
    return this.rowDB.getRow(rowId);
  }

  getMetaRow(rowId: RowId) {
    return this.rowDB.getMetaRow(rowId);
  }

  getLocalRow(rowId: RowId) {
    return this.rowDB.getLocalRow(rowId);
  }

  getLocalRowWithVisibleCells(rowId: RowId) {
    return this.rowDB.getLocalRowWithVisibleCells(rowId);
  }

  /**
   * 慎用，有性能问题
   */
  getAllRows_Ordered() {
    return this.rowDB.getAllRows_Ordered();
  }

  getAllRows_UnOrdered() {
    return this.rowDB.getAllRows_UnOrdered();
  }

  getAllRowIds() {
    return this.rowDB.getAllRowIds();
  }

  getAllRecordVM() {
    return this.rowDB.getAllRecordVM();
  }

  getFirstNRows(count: number) {
    const rows: Row[] = [];
    const ids = this.rowDB.getAllRowIds();
    let index = 0;
    while (rows.length < count && index < ids.length) {
      const r = this.rowDB.getRow(ids[index]);
      r && rows.push(r);
      index += 1;
    }
    return rows;
  }

  getPagedRows(pageSize?: number, offset?: string, recordIds?: string[], fieldIds?: string[]) {
    if (!recordIds && !pageSize) throw new APIException('internal_error', 'set either pageSize or recordIds to get paged rows');
    if (isNil(pageSize)) {
      const rows = this.rowDB.getRowsByIds(recordIds || []);
      if (fieldIds) {
        const filteredRows = rows.map((row) => ({ ...row, cells: pick(row.cells, fieldIds) }));
        return { rows: filteredRows, hasMore: false };
      } else {
        return { rows, hasMore: false };
      }
    }
    const result = this.rowDB.getPagedRows(pageSize, offset, recordIds);
    if (fieldIds) {
      const filteredRows = result.rows.map((row) => ({ ...row, cells: pick(row.cells, fieldIds) }));
      return Object.assign(result, { rows: filteredRows });
    }
    return result;
  }

  @clearSheetCache
  update({ paths, value }: Pick<UpdateSheetPayload, 'paths' | 'value'>) {
    if (paths.join('') === 'targetSheetId') {
      if (this.sheetType === SheetType.LOOP_HISTORY) return;
      if (value && value !== this.id) {
        this.parent.updateLoopSheetRef(value as string, this.id);
      }
    }
    if (paths.join('') === 'sheetType' && value === SheetType.LOOP_HISTORY) {
      this.targetSheetId = undefined;
    }
    set(this, paths, value);
  }

  mergeSliceData({
    viewId,
    value,
  }: Omit<MergeSliceDataToViewPayload, 'sheetId'>) {
    if (value.rawData) {
      const { mergeSliceField } = value.rawData;
      this.rowDB.syncDataFromServer(
        value.rawData.rows.map((metaRow) => {
          if (mergeSliceField) {
            const row = this.getMetaRow(metaRow.id);
            return this.createRow({ ...metaRow, cells: { ...row?.cells, ...metaRow.cells } });
          } else {
            return this.createRow(metaRow);
          }
        }),
      );
    }
  }

  @clearSheetCache
  insertView(value: ViewDTO, index: number) {
    // 异常监控和兜底
    if (this.views.indexOf(value.id) !== -1) {
      logger.error('model_insert_view', new Error('insertView view 数据存在，重复执行 OP'));
      return;
    }

    const view = (new View(this, value)) as IModelView;

    this.viewMap[value.id] = view;
    this.views.splice(index, 0, value.id);
  }

  @clearSheetCache
  moveView(id: ViewId, index: number, target: number) {
    if (this.views[index] !== id) {
      logger.error('move_view', new Error('原位置与实际位置不匹配'));
    }
    remove(this.views, (viewId) => viewId === id);
    this.views.splice(adjustMoveTarget(index, target), 0, id);
  }

  @clearSheetCache
  deleteView(id: ViewId, _: number) {
    remove(this.views, (viewId) => viewId === id);
    this.viewMap[id]?.destroy();
    delete this.viewMap[id];
  }

  @clearSheetCache
  insertField(value: FieldDTO, position: Record<ViewId, number>) {
    this.fieldMap[value.id] = new Field(this, value) as IModelField;
    this.valueExtractor.insertOrUpdate(value.id);

    forEach(this.viewMap, (view) => {
      const index = position[view.id] ?? view.columns.length;
      view.insertColumn(value.id, index);
    });
  }

  @clearSheetCache
  deleteField(id: FieldId, position: Record<ViewId, number>) {
    this.fieldMap[id]?.delete();
    this.valueExtractor.remove(id);

    forEach(this.viewMap, (view) => {
      const index = position[view.id] ?? view.columns.indexOf(id);

      if (index !== -1) {
        view.deleteColumn(id, index);
      }
    });
  }

  @clearSheetCache
  updateField(data: UpdateFieldPayload) {
    const field = this.getField(data.fieldId);
    if (field) {
      field.update(data);
      this.valueExtractor.insertOrUpdate(field.id);
      this.getAllView().forEach((view) => view.cache.clear());
      this.parent.fxCellValueConvertorManager.onFieldChange(this.id, field.id);

      // 对于字段类型发生变更，并且目标字段是内置字段（创建/更新 时间/用户）
      // 清除下 row 的缓存信息，以便后置消费能拿到正确的数据比如类型转换
      if (data.paths[0] === 'type' && (isComputedField(data.value) || isComputedField(data.origin))) {
        this.getAllRows_UnOrdered().forEach((record) => {
          this.recordChangesets.change(record.id, { columns: [data.fieldId] });
          record.clearCache();
        });
      }
    }
  }

  /** 仅更新 field 的 config 属性，不调用其它任何逻辑 */
  updateFieldConfig_noSideEffect(id: FieldId, config: FieldDTO['config']) {
    const field = this.fieldMap[id];
    const prev = field?.config;
    if (prev) {
      Object.assign(prev, config);
      this.valueExtractor.insertOrUpdate(id);
    }
  }

  @clearSheetCache
  restoreField(id: FieldId, position: Record<ViewId, number>) {
    const field = this.fieldMap[id];
    if (field) {
      field.restore();
      this.valueExtractor.insertOrUpdate(id);
    }
    forEach(position, (index, viewId) => {
      this.viewMap[viewId].insertColumn(id, index);
    });
  }

  @clearSheetCache
  fetchServerRecords(records: RecordDTO[]) {
    const inserted = records.map((r) => {
      const preRowId = (() => {
        const prev = this.sheetIndex?.prev(r.id);
        return prev === undefined
          ? this.getLastRowId() // 如果没有这个行，则插入到最后一行
          : prev;
      })();

      const row = this.createRow({
        id: r.id,
        preRowId,
        creator: r.creator,
        createdTime: r.createdTime,
        updater: r.updater,
        updatedTime: r.updatedTime,
        cells: r.cells ?? {},
      });

      this.rowDB.insert(row);
      this.recordChangesets.insert([row.id]);
      return row;
    });
    this.rowDB.insertLokiDoc(inserted);
  }

  @clearSheetCache
  insertRow(
    data: Omit<InsertRowPayload, 'origin' | 'sheetId' | 'preRank'> & { creator?: ObjectCellValue; createdTime?: number },
  ) {
    const row = this.createRow({
      id: data.rowId,
      preRowId: data.preRowId,
      creator: data.creator ?? data.operator,
      createdTime: data.createdTime ?? data.timestamp,
      updater: data.operator,
      updatedTime: data.timestamp,
      cells: data.value ?? {},
    });
    const inserted = this.rowDB.insert(row);
    if (inserted) {
      this.rowDB.insertLokiDoc(inserted);
    }
    this.recordChangesets.insert([row.id]);
    if (this.sheetIndex) {
      const rank = data.rank ?? this.sheetIndex.genRankAfter(data.preRowId);
      this.sheetIndex.setRecordRank(data.rowId, rank);
    }
    this.sheetIndex?.insert(data.preRowId, data.rowId);

    this.userCache?.insertOrUpdateOperatorUser(data.operator);
  }

  @clearSheetCache
  deleteRow(row: RecordDTO) {
    this.rowDB.delete(row.id, row.preRowId);
    this.recordChangesets.delete([row.id]);
    this.sheetIndex?.delete(row.id);
  }

  @clearSheetCache
  moveRow(data: Omit<MoveRowPayload, 'sheetId'>) {
    // 更新操作信息
    this.rowDB.getRealRow(data.rowId)?.update(
      null,
      null,
      data.operator,
      data.timestamp,
    );

    this.rowDB.move(data.rowId, data.preRowId, data.targetPreRowId);
    this.recordChangesets.change(data.rowId, {
      targetPreRowId: data.targetPreRowId,
    });
    // rank 如果没有的话需要在 move 执行前生成，在 move 执行后配置
    const rank = data.rank ?? this.sheetIndex?.genRankAfter(data.targetPreRowId);
    this.sheetIndex?.move(data.targetPreRowId, data.rowId);
    this.sheetIndex?.setRecordRank(data.rowId, rank);

    this.userCache?.insertOrUpdateOperatorUser(data.operator);
  }

  updateOperatorUser(data: RowData) {
    // 服务端生成 cp 脚本会出现没有 row 但是必须更新 operator 的情况
    if (data.operator) {
      this.userCache?.insertOrUpdateOperatorUser(data.operator);
    }
  }

  @clearSheetCache
  updateRow(data: RowData, opts: UpdateRowOptions = {}) {
    const updated = this.updateOneRow(data, opts);
    if (updated) {
      this.rowDB.updateLokiDoc(updated);
    }
  }

  @clearSheetCache
  updateRows(list: RowData[], opts: UpdateRowOptions) {
    const affected = list.map((data) => {
      this.updateOperatorUser(data);
      return this.updateOneRow(data, opts);
    }).filter<Row>((r): r is Row => !!r);
    this.rowDB.updateLokiDoc(affected);
  }

  updateOneRow(data: RowData, opts: UpdateRowOptions = {}) {
    this.updateOperatorUser(data);
    const row = this.rowDB.getRealRow(data.rowId);
    if (!row) return;
    const operator = data.operator ?? row.updater;
    const timestamp = opts.skipOperationStamp ? undefined : data.timestamp;
    const changedFieldIds = row.update(
      data.origin,
      data.value,
      operator,
      timestamp,
    );
    const updated = this.rowDB.update(row);
    this.recordChangesets.change(row.id, {
      columns: changedFieldIds,
    });
    changedFieldIds.forEach((changedFieldId) => {
      this.parent.fxCellValueConvertorManager.onRecordChange(this.id, changedFieldId, row.id);
    });
    return updated;
  }

  @clearSheetCache
  appendRow(data: AppendRowPayload) {
    const lastRowId = this.getLastRowId();
    const row = this.createRow({
      id: data.rowId,
      preRowId: lastRowId,
      createdTime: data.timestamp,
      updatedTime: data.timestamp,
      creator: data.operator,
      updater: data.operator,
      cells: data.value ?? {},
    });
    const inserted = this.rowDB.insert(row);
    if (inserted) {
      this.rowDB.insertLokiDoc(inserted);
    }
    this.recordChangesets.insert([row.id]);

    if (this.sheetIndex) {
      const rank = this.sheetIndex.genTailRank();
      this.sheetIndex.setRecordRank(row.id, rank);
    }
    this.sheetIndex?.append(data.rowId);

    this.userCache?.insertOrUpdateOperatorUser(data.operator);
  }

  lockCell({ rowId, fieldId, operator }: Omit<LockCellPayload, 'sheetId'>) {
    this.selectionManager.lock(operator, rowId, fieldId);
  }

  unlockCell({ operator }: Pick<LockCellPayload, 'operator'>) {
    this.selectionManager.unlock(operator);
  }

  getLastRowId() {
    return this.rowDB.getLastRowId() ?? null;
  }

  saveSnapshot() {
    this.snapshot = this.toJSON();
  }

  setFullRawData(
    rows: RecordDTO[],
    fullRankData: Array<{ id: string; rank: string }> | undefined,
    fullDataForSure = true,
    forceGenerateSheetIndex = false,
  ) {
    const newRecordIds = rows.map((r) => r.id);
    const oldRecordIds = this.rowDB.getAllRowIds();
    // 在分表时会重新配置数据，这个时候需要将移除的标记出来，以描述实际的数据变更保障后续依赖变更信息的正确执行
    this.recordChangesets.delete(difference(oldRecordIds, newRecordIds));
    this.rowDB.overrideByFullRawData(rows.map((metaRow) => this.createRow(metaRow)));
    this.recordChangesets.insert(newRecordIds);

    // 老 sheet（2023/09/01之前创建的）中没有「前端索引」和「用户缓存」，
    // 在拉取全量数据后会构建起前端索引和用户缓存
    if (fullDataForSure) {
      if (fullRankData) {
        this.sheetIndex = this.sheetIndex ?? new SheetIndexWithRank([]);
        this.sheetIndex.resetByRankData(fullRankData);
      } else {
        this.buildSheetIndexIfNotExist(forceGenerateSheetIndex);
      }
      this.buildUserMapIfNotExist(forceGenerateSheetIndex);
    }

    this.loadedDefer.resolve();
    this.snapshot = undefined;

    // 等待 pendingOps 被应用结束之后再设置 fullRawData
    if (this.pendingOps.length) {
      this.doApply(this.pendingOps);
      this.pendingOps = [];
    }

    this.fullRawData = true;
  }

  hasFullRawData() {
    return this.fullRawData;
  }

  hasSheetIndex() {
    return !!this.sheetIndex;
  }

  hasUserMap() {
    return !!this.userCache;
  }

  diffSheetIndex() {
    if (!this.sheetIndex) {
      return;
    }
    const recordIdsFromIndex = this.sheetIndex.generateOrderedKeys();
    const recordIdsFromRowDB = this.rowDB.getOrderedAllRowIds();
    return isEqual(recordIdsFromIndex, recordIdsFromRowDB);
  }

  isUserChanged(operator: ObjectCellValue) {
    if (!this.userCache) {
      return false;
    }
    const old = this.userCache.get(operator?.identifier);
    if (!old) {
      return false;
    }
    const newUser = convertOperatorToUser(operator);
    return !isUserEqual(newUser, old);
  }

  private buildSheetIndexIfNotExist(forceGenerateSheetIndex = false) {
    if (this.sheetIndex !== undefined && !forceGenerateSheetIndex) {
      return;
    }
    const recordIds = this.rowDB.getOrderedAllRowIds();
    this.sheetIndex = new SheetIndexWithRank(recordIds);
  }

  private buildUserMapIfNotExist(forceGenerateSheetIndex = false) {
    if (this.userCache !== undefined && !forceGenerateSheetIndex) {
      return;
    }
    this.userCache = new UserCache({});
    this.rowDB.getAllRows_UnOrdered().forEach((r) => {
      this.userCache?.insertOrUpdateOperatorUser(r.creator);
      this.userCache?.insertOrUpdateOperatorUser(r.updater);
    });
  }

  private createRow(json: RecordDTO) {
    const row = new Row({
      ...json,
      cells: {
        ...json.cells,
      },
    }, (ins) => (
      builtInDataToCells(ins, getRefBuiltInDataFields(
        Object.values(this.fieldMap).filter((field) => !field.deleted),
      ))
    ));

    return row;
  }
}
