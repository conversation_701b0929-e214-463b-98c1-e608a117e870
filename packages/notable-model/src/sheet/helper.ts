import { logger, SheetId, ViewTypes } from '@ali/notable-common';
import { sortBy } from 'lodash-es';
import type { FieldId, FieldDTO } from '../field/types';
import { RecordDTO, PreRowId } from '../row/types';
import { NSheet } from '../nsheet/types';
import { ViewDTO, ViewId } from '../view/types';
import { SheetDTO } from './types';

function fixListBy(
  list: string[],
  refList: string[],
  viewType: ViewTypes,
  primaryFieldId: string,
): string[] {
  if (list.length > refList.length) {
    logger.log('columns_max_field_map');
  } else if (list.length < refList.length) {
    logger.log('columns_min_field_map');
  }

  const indexMap = list.reduce<Record<string, number>>((acc, field, index) => {
    if (typeof acc[field] === 'number') { logger.log('columns_duplicate'); }

    acc[field] = index;
    return acc;
  }, {});

  return sortBy(refList, (fieldId) => {
    const originIndex = indexMap[fieldId] ?? refList.length;
    if (primaryFieldId === fieldId && indexMap[fieldId] === undefined) {
      return 0;
    }

    // 对于非表单视图的字段将字段校准到第一列
    if (primaryFieldId === fieldId && viewType !== 'FormDesigner' && originIndex !== 0) {
      return 0;
    }

    return originIndex;
  });
}

function findLastField(list: FieldDTO[], refCountMap: Record<string, number>): FieldDTO | undefined {
  if (list.length === 1) return list[0];
  const fields = list.filter(({ id }) => refCountMap[id] === undefined);

  return fields[0] ?? list[0];
}
/**
 * 校准主键被删除、字段未被删除却不再 columns 场景
 * 脏数据的来源是移动字段时，移动 id 与 id 原 index 不一致
 * 移动消费侧直接消费 index 对原数据进行修改，导致出现重复 id 和缺失其它未操作字段 id
 */
export function BC_adjustColumnsMiss(
  viewMap: Record<ViewId, ViewDTO>, fieldMap: Record<FieldId, FieldDTO>,
) {
  const newFieldMap: Record<FieldId, FieldDTO> = {};
  const validColumns: FieldId[] = [];
  const deletedPrimaryField: FieldDTO[] = [];
  const deletedPrimaryRefCount: Record<string, number> = {};
  let primaryFieldId = '';

  Object.values(fieldMap).forEach((field) => {
    newFieldMap[field.id] = field;
    if (field.isPrimary) {
      if (field.deleted) {
        deletedPrimaryField.push(field);
        if (field.refId) {
          deletedPrimaryRefCount[field.refId] = (deletedPrimaryRefCount[field.refId] ?? 0) + 1;
        }
      } else {
        primaryFieldId = field.id;
      }
    } else {
      if (field.deleted) return;
      validColumns.push(field.id);
    }
  });

  if (primaryFieldId === '') {
    logger.log('primary_field_miss');
    const field = findLastField(deletedPrimaryField, deletedPrimaryRefCount);
    if (field) {
      newFieldMap[field.id] = { ...field, deleted: false };
      validColumns.unshift(field.id);
      primaryFieldId = field.id;
    }
  } else {
    validColumns.unshift(primaryFieldId);
  }


  const newViewMap = Object.values(viewMap).reduce<Record<ViewId, ViewDTO>>((acc, view) => {
    acc[view.id] = { ...view, columns: fixListBy(view.columns, validColumns, view.type, primaryFieldId) };
    return acc;
  }, {});

  return {
    viewMap: newViewMap,
    fieldMap: newFieldMap,
  };
}

/** 清洗视图主键不在第一列的情况 */
export function BC_adjustViewFirstColumnNotPrimaryField(
  view: ViewDTO,
  fieldMap: SheetDTO['fieldMap'],
): ViewDTO {
  const columns = [...view.columns];
  const len = columns.length;

  // 表单视图，主列允许不在第一列了
  if (view.type !== 'FormDesigner') {
    for (let i = 0; i < len; i++) {
      const fieldId = columns[i];
      const field = fieldMap[fieldId];

      if (field?.isPrimary && i === 0) {
        break;
      }

      if (field?.isPrimary && i !== 0) {
        columns.splice(i, 1);
        columns.unshift(fieldId);
        break;
      }
    }
  }

  return { ...view, columns };
}
/**
 * 线上观察到有 nsheet 存在但 sheet 不存在的情况，是之前
 * deleteSheet 和 deleteNsheet 的实现问题导致，需要处理成一致
 */
export function BC_removeNsheetIfSheetNotExists(nsheet: NSheet[], sheetMap: Record<SheetId, SheetDTO>) {
  return nsheet.filter((ns) => {
    return ns.type !== 'sheet' || !!sheetMap[ns.id];
  });
}
