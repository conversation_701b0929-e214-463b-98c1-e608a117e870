import { FeatAction, OperationTarget } from '@ali/notable-common';
import type {
  InsertWorkflowOP,
  UpdateWorkflowOP,
  DeleteWorkflowOP,
} from './workflow/types/op';
import * as workflows from './workflow/apply';
import * as appLocks from './appLock/apply';
import * as permissions from './permission/apply';
import * as documents from './document/apply';
import * as references from './reference/apply';
import * as devMeta from './devMeta/apply';
import * as userMap from './userMap/apply';
import * as partition from './partition/apply';
import * as workdayConfig from './workdayConfig/apply';
import * as schemaVersions from './schemaVersion/apply';
import { insertPrimaryDocRecord } from './primaryDocRecord/apply';
import {
  InsertAppLockOp,
  UpdateAppLockOp,
  DeleteAppLockOp,
} from './appLock';
import { DeleteAdvancedPermissionOp, InsertAdvancedPermissionOp, UpdateAdvancedPermissionOp } from './permission';
import { DeleteDocumentOp, InsertDocumentOp, UpdateDocumentOp } from './document';
import { UpdateReferenceOp } from './reference';
import { DeleteDevMetaOp, InsertDevMetaOp, UpdateDevMetaOp } from './devMeta';
import { UpdateUserMapOp } from './userMap';
import { UpdatePartitionOp } from './partition';
import { UpdateWorkdayConfigOp, InsertWorkdayConfigOp, DeleteWorkdayConfigOp } from './workdayConfig';
import { UpdateSchemaVersionOp } from './schemaVersion';
import { DoFxCalcOP, MarkRecordsMutationOP } from './calc';
import { InsertPrimaryDocRecordOp } from './primaryDocRecord';

export { FeatType } from './feat';

export {
  createInsertWorkflowOp,
  createUpdateWorkflowOp,
  createDeleteWorkflowOp,
} from './workflow';

export type {
  Workflow,
  WorkflowId,
  InsertWorkflowOP,
  UpdateWorkflowOP,
  DeleteWorkflowOP,
} from './workflow';

export type {
  AppLock,
  SheetLock,
  FieldLockSetting,
} from './appLock';

export type {
  InsertAppLockOp,
  UpdateAppLockOp,
  DeleteAppLockOp,
} from './appLock';

export {
  createInsertAppLockOp,
  createUpdateAppLockOp,
  createDeleteAppLockOp,
} from './appLock';

export type {
  AdvancedPermission,
} from './permission';

export type {
  InsertAdvancedPermissionOp,
  UpdateAdvancedPermissionOp,
  DeleteAdvancedPermissionOp,
} from './permission';

export {
  createInsertAdvancedPermissionOp,
  createUpdateAdvancedPermissionOp,
  createDeleteAdvancedPermissionOp,
} from './permission';

export type {
  WorkdayConfig,
  InsertWorkdayConfigOp,
  UpdateWorkdayConfigOp,
  DeleteWorkdayConfigOp,
} from './workdayConfig';

export {
  createInsertWorkdayConfigOp,
  createUpdateWorkdayConfigOp,
  createDeleteWorkdayConfigOp,
} from './workdayConfig';

export type {
  InsertDocumentOp,
  UpdateDocumentOp,
  DeleteDocumentOp,
} from './document';

export {
  createInsertDocumentOp,
  createUpdateDocumentOp,
  createDeleteDocumentOp,
} from './document';

export type {
  UpdateReferenceOp,
  ReferenceOperation,
} from './reference';

export {
  isReferenceOperation,
  migrateOperation,
  migrateOperations,
} from './reference';

export type {
  DeveloperMetadata,
  DocMetadata,
  GeneralDevMetadata,
  InsertDevMetaOp,
  UpdateDevMetaOp,
  DeleteDevMetaOp,
} from './devMeta';

export {
  createInsertDevMetaOp,
  createUpdateDevMetaOp,
  createDeleteDevMetaOp,
} from './devMeta';

export type { UserMapData, UpdateUserMapOp } from './userMap';

export { createUpdateUserMapOp } from './userMap';

export type {
  UpdatePartitionOp,
} from './partition';

export {
  isUpdatePartitionOp,
  createUpdatePartitionOp,
} from './partition';

export type {
  DoFxCalcOP,
  MarkRecordsMutationOP,
} from './calc';

export {
  isDoFxCalcOP,
  createDoFxCalcOp,
  createMarkRecordsMutationOp,
} from './calc';

export type {
  PrimaryDocRecord,
  InsertPrimaryDocRecordOp,
} from './primaryDocRecord';

export {
  createInsertPrimaryDocRecordOp,
  isInsertPrimaryDocRecordOp,
} from './primaryDocRecord';

export const strategies = {
  ...appLocks,
  ...workflows,
  ...permissions,
  ...documents,
  ...references,
  ...devMeta,
  ...userMap,
  ...partition,
  ...workdayConfig,
  ...schemaVersions,
  insertPrimaryDocRecord,
  // ...calc, // 占位，calc 没有本地操作
};

export type FeatOperation =
  InsertWorkflowOP | UpdateWorkflowOP | DeleteWorkflowOP |
  InsertAppLockOp | UpdateAppLockOp | DeleteAppLockOp |
  InsertAdvancedPermissionOp | UpdateAdvancedPermissionOp |
  DeleteAdvancedPermissionOp | InsertDocumentOp | UpdateDocumentOp |
  DeleteDocumentOp | UpdateReferenceOp | UpdatePartitionOp | InsertDevMetaOp |
  UpdateDevMetaOp | DeleteDevMetaOp | InsertWorkdayConfigOp | UpdateWorkdayConfigOp |
  DeleteWorkdayConfigOp | EndSyncOP | UpdateSchemaVersionOp | UpdateUserMapOp |
  MarkRecordsMutationOP | DoFxCalcOP | InsertPrimaryDocRecordOp;

export type EndSyncOP = {
  target: OperationTarget.FEAT;
  action: FeatAction.END_SYNC;
  payload: {
    sheetId: string;
    feat: undefined;
  };
};
