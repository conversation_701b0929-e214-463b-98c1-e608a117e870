import { get } from 'lodash-es';
import { OADate, FormulaErrorType } from '@ali/zongheng-common';
import type { NotableArray, ConstArrayItem } from '@ali/zongheng-formula';
import {
  NestedMap2, SheetId, RecordId, RecordDTO, CellExtractor,
  getFxCellExtractorByField, CellDataType, WidgetTypeEnums,
  AutoNumUtils, ObjectCellValue,
  SelectConfigEnum,
  RowCell,
  deserializePersonCell,
  serializePersonCell,
  deserializeGroup,
  serializeGroup,
  deserializeDepartment,
  serializeDepartment,
  deserializeSingleSelect,
  serializeSingleSelect,
  deserializeMultiSelect,
  serializeMultiSelect,
  deserializeAssociation,
  serializeAssociation,
  deserializeFile,
  serializeFile,
  logger,
  isNil,
  isFxOriginObjectValue,
  isNotableFormulaErrorValue,
  isObjectCellValue,
  FxObjectValue,
  ObjectCell,
  IStageNode,
  ResultFieldType,
  getFxValueOfFxObjValue,
} from '@ali/notable-common';
import type { FieldId, FieldDTO, SelectConfig } from '../field';
import { FORMAT_PERCENT } from '../types';
import type { SheetModel } from '../sheet';
import { getValueByIdentifier, getIdentifiersByCellValue } from '../calculate/helper';
import * as FxValue from '../formula-engine/utils/FxValueUtils';
import { makeVirtualFormulaField } from '../formula-engine/utils/common';
import { getResultFieldType } from '../formula-engine/formula-parser/detect-fx-result-field-type';
import type { IModel } from '../formula-engine/formula-parser/formula-rpn';
import {
  selectOptionsToMap, getSelectedOfSelectValue, flowOptionsToMap, getSelectedOfFlowValue,
} from './cell-value';

export type ValueTypeNoRefANull = Exclude<FxValue.Types, 'REFERENCE' | 'NULL' | 'NOTABLE_ARRAY' | 'DEFER_CONDITION'>;
const pathFormatInField = 'renderFieldConfig.props.format';
type CacheItem = {
  v: undefined | FxValue.Value<ValueTypeNoRefANull>;
};

export type FieldSelectOptionCacheItem = Record<string, string | number | boolean>;
export type FieldFlowOptionCacheItem = Record<string, string | number | boolean>;
export type fieldTextToSelectOptionMapCacheItem = Record<string, string>;
export type fieldTextToFlowOptionMapCacheItem = Record<string, string>;

function getAndEnsureObjectCellValue(cell: RowCell | null) {
  if (!(cell?.dataType === CellDataType.OBJECT) || !cell.value) return;
  return cell.value;
}

function convertNumberCell(cell: RowCell | null, bFixPercent?: boolean) {
  if (cell?.value == null || cell.dataType !== CellDataType.NUMBER) {
    return;
  }
  let num = Number(cell.value);
  if (bFixPercent) num /= 100;
  return FxValue.makeNumber(num);
}

function convertStringCell(cell: RowCell | null) {
  if (cell?.value == null || cell.dataType !== CellDataType.STRING) {
    return;
  }
  return FxValue.makeString(cell.value);
}

function convertBooleanCell(cell: RowCell | null) {
  if (!cell?.value || cell.dataType !== CellDataType.BOOLEAN) {
    return FxValue.makeBoolean(false);
  }
  return FxValue.makeBoolean(true);
}

function convertDateCell(cell: RowCell | null) {
  if (cell?.value == null || cell.dataType !== CellDataType.DATE) {
    return;
  }
  return FxValue.makeNumber(OADate.fromDate(cell.value));
}

function convertAutoNumCell(cell: RowCell | null, field: FieldDTO, rowCreatedTime: number) {
  if (cell?.value == null || cell.dataType !== CellDataType.AUTO_NUM) {
    return;
  }
  const autoNum = AutoNumUtils.getResultWithField(field, cell.value, rowCreatedTime);
  return autoNum == null ? undefined : FxValue.makeString(autoNum);
}

function convertObjectValueForFormula(cell: RowCell | null) {
  if (cell?.value == null || cell.dataType !== CellDataType.OBJECT) {
    return;
  }
  // 当公式字段的结果为 ConstArray
  const fxVal = getFxValueOfFxObjValue(cell.value)?.map<ConstArrayItem>((item) => {
    if (typeof item === 'object' && item) {
      return FxValue.makeError(item.fxError as FormulaErrorType);
    }
    // 这里写 as 是因为 apps/we-notable 的 tsconfig 中 strictNullChecks 为 false，无法类型收窄
    return item as FxValue.Value<'BOOLEAN' | 'STRING' | 'NUMBER'>;
  });
  if (fxVal?.length) {
    return FxValue.makeConstArray([fxVal]);
  }
}

export class ConvertCellValueCache {
  /** 缓存 cellValue 转换成 FxCellValue 的结果 */
  private readonly cellValueCache: NestedMap2<SheetId, FieldId, Map<RecordId, CacheItem>>;
  /** 缓存 单选多选enums 中 str 转成 number｜boolean｜string 的结果 */
  private readonly fieldSelectOptionsCache: NestedMap2<SheetId, FieldId, FieldSelectOptionCacheItem>;
  /** 缓存 单选多选enums 转换成 Record<title, id> 反向索引的数据 */
  private readonly fieldTextToSelectOptionMapCache: NestedMap2<SheetId, FieldId, fieldTextToSelectOptionMapCacheItem>;
  /** 缓存 流程enums 中 str 转成 number｜boolean｜string 的结果 */
  private readonly fieldFlowOptionMapCache: NestedMap2<SheetId, FieldId, fieldTextToSelectOptionMapCacheItem>;
  /** 缓存 流程enums 转换成 Record<title, id> 反向索引的数据 */
  private readonly fieldTextToFlowOptionMapCache: NestedMap2<SheetId, FieldId, fieldTextToSelectOptionMapCacheItem>;
  /** 缓存 公式计算中，整列引用转换成 NotableArray 的结果 */
  private readonly fieldNotableArrayCache: NestedMap2<SheetId, FieldId, FxValue.Value<'NOTABLE_ARRAY'>>;
  /** 缓存 公式计算中，动态检测的 resultFieldType 结果 */
  private readonly fieldResultFieldTypeCache: NestedMap2<SheetId, FieldId, ResultFieldType>;

  constructor() {
    this.cellValueCache = new NestedMap2();
    this.fieldSelectOptionsCache = new NestedMap2();
    this.fieldTextToSelectOptionMapCache = new NestedMap2();
    this.fieldFlowOptionMapCache = new NestedMap2();
    this.fieldTextToFlowOptionMapCache = new NestedMap2();
    this.fieldNotableArrayCache = new NestedMap2();
    this.fieldResultFieldTypeCache = new NestedMap2();
  }

  addCellValue(sId: SheetId, fId: FieldId, rId: RecordId, item: CacheItem) {
    let sheetItem = this.cellValueCache.getItem(sId, fId);
    if (!sheetItem) {
      sheetItem = new Map();
      this.cellValueCache.addItem(sId, fId, sheetItem);
    }
    sheetItem.set(rId, item);
  }

  getCellValue(sId: SheetId, fId: FieldId, rId: RecordId) {
    let sheetItem = this.cellValueCache.getItem(sId, fId);
    if (!sheetItem) {
      sheetItem = new Map();
      this.cellValueCache.addItem(sId, fId, sheetItem);
    }
    const cellItem = sheetItem.get(rId);
    if (cellItem) {
      return cellItem.v;
    }
  }

  clearCellValue(sId: SheetId, fId: FieldId, rId?: RecordId) {
    if (!rId) {
      this.cellValueCache.deleteItem(sId, fId);
      return;
    }
    const sheetItem = this.cellValueCache.getItem(sId, fId);
    if (!sheetItem) return;
    sheetItem.delete(rId);
  }

  addSelectOptions(sId: SheetId, fId: FieldId, item: FieldSelectOptionCacheItem) {
    this.fieldSelectOptionsCache.addItem(sId, fId, item);
  }

  getSelectOptions(sId: SheetId, fId: FieldId) {
    return this.fieldSelectOptionsCache.getItem(sId, fId);
  }

  clearSelectOptions(sId: SheetId, fId: FieldId) {
    this.fieldSelectOptionsCache.deleteItem(sId, fId);
  }

  addTextToSelectOptionMap(sId: SheetId, fId: FieldId, item: fieldTextToSelectOptionMapCacheItem) {
    this.fieldTextToSelectOptionMapCache.addItem(sId, fId, item);
  }

  getTextToSelectOptionMap(sId: SheetId, fId: FieldId) {
    return this.fieldTextToSelectOptionMapCache.getItem(sId, fId);
  }

  clearTextToSelectOptionMap(sId: SheetId, fId: FieldId) {
    this.fieldTextToSelectOptionMapCache.deleteItem(sId, fId);
  }

  addFlowOptions(sId: SheetId, fId: FieldId, item: FieldSelectOptionCacheItem) {
    this.fieldSelectOptionsCache.addItem(sId, fId, item);
  }

  getFlowOptions(sId: SheetId, fId: FieldId) {
    return this.fieldSelectOptionsCache.getItem(sId, fId);
  }

  clearFlowOptions(sId: SheetId, fId: FieldId) {
    this.fieldSelectOptionsCache.deleteItem(sId, fId);
  }

  addTextToFlowOptionMap(sId: SheetId, fId: FieldId, item: fieldTextToFlowOptionMapCacheItem) {
    this.fieldTextToFlowOptionMapCache.addItem(sId, fId, item);
  }

  getTextToFlowOptionMap(sId: SheetId, fId: FieldId) {
    return this.fieldTextToFlowOptionMapCache.getItem(sId, fId);
  }

  clearTextToFlowOptionMap(sId: SheetId, fId: FieldId) {
    this.fieldTextToFlowOptionMapCache.deleteItem(sId, fId);
  }

  addNotableArray(sId: SheetId, fId: FieldId, item: FxValue.Value<'NOTABLE_ARRAY'>) {
    this.fieldNotableArrayCache.addItem(sId, fId, item);
  }

  getNotableArray(sId: SheetId, fId: FieldId) {
    return this.fieldNotableArrayCache.getItem(sId, fId);
  }

  clearNotableArray(sId: SheetId, fId: FieldId) {
    this.fieldNotableArrayCache.deleteItem(sId, fId);
  }

  addResultFieldType(sId: SheetId, fId: FieldId, type: ResultFieldType) {
    this.fieldResultFieldTypeCache.addItem(sId, fId, type);
  }

  getResultFieldType(sId: SheetId, fId: FieldId) {
    return this.fieldResultFieldTypeCache.getItem(sId, fId);
  }

  clearResultFieldType() {
    this.fieldResultFieldTypeCache.clear();
  }

  drop() {
    this.cellValueCache.clear();
    this.fieldSelectOptionsCache.clear();
    this.fieldTextToSelectOptionMapCache.clear();
    this.fieldFlowOptionMapCache.clear();
    this.fieldTextToFlowOptionMapCache.clear();
    this.fieldNotableArrayCache.clear();
    this.fieldResultFieldTypeCache.clear();
  }
}

export class FxCellValueConvertorManager {
  private readonly converterMap = new NestedMap2<string, string, FxCellValueConvertor>();
  private readonly convertCellValueCache = new ConvertCellValueCache();
  constructor(
    private readonly model: IModel,
  ) {}

  getConvertor(sheetId: string, fieldId: string, isVirtual = false) {
    const c = this.converterMap.getItem(sheetId, fieldId);
    if (c) {
      return c;
    }
    const sheet = this.model.getSheet(sheetId);
    let field: FieldDTO | undefined = sheet?.fieldMap?.[fieldId];
    if (!field && isVirtual) {
      field = makeVirtualFormulaField(fieldId);
    }
    if (!field || !sheet) {
      return;
    }
    const convertor = new FxCellValueConvertor(this.model, sheet, field, this.convertCellValueCache);
    this.converterMap.addItem(sheetId, fieldId, convertor);
    return convertor;
  }

  onFieldChange(sheetId: string, fieldId: string) {
    this.converterMap.deleteItem(sheetId, fieldId);
    this.convertCellValueCache.clearCellValue(sheetId, fieldId);
    this.convertCellValueCache.clearSelectOptions(sheetId, fieldId);
    this.convertCellValueCache.clearTextToSelectOptionMap(sheetId, fieldId);
    this.convertCellValueCache.clearFlowOptions(sheetId, fieldId);
    this.convertCellValueCache.clearTextToFlowOptionMap(sheetId, fieldId);
    this.convertCellValueCache.clearNotableArray(sheetId, fieldId);
    this.convertCellValueCache.clearResultFieldType();
  }

  onRecordChange(sheetId: string, fieldId: string, recordId: string) {
    this.convertCellValueCache.clearCellValue(sheetId, fieldId, recordId);
    this.convertCellValueCache.clearNotableArray(sheetId, fieldId);
  }

  init() {
    this.converterMap.clear();
  }
}

export type FromCellValueFunc = (row: RecordDTO) => undefined | FxValue.Value<ValueTypeNoRefANull>;

export type FromCellValueFuncResult = ReturnType<FromCellValueFunc>;

export type CellValueByIdentifierExtractor = (row: RecordDTO, filterFactor: string | number | boolean) => [ConstArrayItem, RowCell] | null;

export function getConstArrayFromRow(row: RecordDTO, convertor: FxCellValueConvertor | undefined) {
  const constArray: ConstArrayItem[] = [];
  const fxCellValue = convertor?.fromCellValue(row);
  if (FxValue.isConstArray(fxCellValue)) {
    FxValue.iterateConstArray(fxCellValue.value, (item) => {
      constArray.push(item);
    });
  } else if (!isNil(fxCellValue)) {
    constArray.push(fxCellValue);
  }
  return constArray;
}

export class FxCellValueConvertor {
  private readonly model: IModel;
  private readonly sheet: SheetModel;
  private readonly field: FieldDTO;
  private readonly cellValueCache: ConvertCellValueCache;
  _fromCellValue: FromCellValueFunc;
  extractCellValue: CellExtractor;
  extractCellValueByIdentifier: CellValueByIdentifierExtractor;
  constructor(model: IModel, sheet: SheetModel, field: FieldDTO, cache: ConvertCellValueCache) {
    this.model = model;
    this.sheet = sheet;
    this.field = field;
    this.cellValueCache = cache;
    this.extractCellValue = getFxCellExtractorByField(field);
    this._fromCellValue = this.generateFromCellValueFunc();
    this.extractCellValueByIdentifier = this.generateExtractCellValueByIdentifierFunc();
  }

  get sheetId() {
    return this.sheet.id;
  }

  private _fromCellValueWithCache(row: RecordDTO) {
    const cached = this.cellValueCache.getCellValue(this.sheetId, this.field.id, row.id);
    if (cached) return cached;
    const item = this._fromCellValue(row);
    this.cellValueCache.addCellValue(this.sheetId, this.field.id, row.id, { v: item });
    return item;
  }

  fromCellValue(row: RecordDTO) {
    const cell = this.extractCellValue(row);
    if (cell?.value == null) return;

    if (cell.dataType === CellDataType.OBJECT) {
      return this._fromCellValueWithCache(row);
    }
    return this._fromCellValue(row);
  }

  getFieldSelectOptions = () => {
    let options = this.cellValueCache.getSelectOptions(this.sheetId, this.field.id);
    if (options) return options;
    options = selectOptionsToMap(get(this.field.config, 'renderFieldConfig.props.enums'));
    this.cellValueCache.addSelectOptions(this.sheetId, this.field.id, options);
    return options;
  };

  getFieldFlowOptions = () => {
    let options = this.cellValueCache.getFlowOptions(this.sheetId, this.field.id);
    if (options) return options;
    options = flowOptionsToMap(get(this.field.config, 'renderFieldConfig.props.options'));
    this.cellValueCache.addFlowOptions(this.sheetId, this.field.id, options);
    return options;
  };

  getFieldNotableArray = (row?: RecordDTO) => {
    if (!row) {
      const notableArray = this.cellValueCache.getNotableArray(this.sheetId, this.field.id);
      if (notableArray) return notableArray;
    }

    const arr: NotableArray = { type: 'sheetField', sId: this.sheetId, fId: this.field.id, items: [] };

    const dealSingleRow = (r: RecordDTO | undefined) => {
      if (!r) return;
      const rawCell = this.extractCellValue(r);
      if (rawCell?.dataType === CellDataType.OBJECT) {
        const { value } = rawCell;
        if (value) {
          // rich-text特殊，目前identifier存的是asl，没办法根据identifier拆分
          // rich-text目前未支持原值，所以这里直接跳过，走到else里按照非原值处理
          if (Array.isArray(value.filterFactor) && this.field.type !== 'richText') {
            const extractedData = JSON.parse(value.data);
            let identifiers: string[] | null = null;
            if (value.refData) {
              identifiers = [];
               value.refData.value.forEach((v) => {
                if (isObjectCellValue(v)) {
                  identifiers?.push(...getIdentifiersByCellValue(v, this.field) ?? '');
                } else {
                  identifiers?.push(`${v ?? ''}`);
                }
              });
            } else {
              identifiers = getIdentifiersByCellValue(value, this.field);
            }

            if (!Array.isArray(extractedData)) return;
            if (!identifiers) return;

            identifiers.forEach((id, index) => {
              const fakeRecord = { ...r };

              const extractCell = this.extractCellValueByIdentifier(r, id);
              if (!extractCell) return;

              const rowCell = extractCell[1];
              if (!rowCell) return;

              let newCell: ObjectCell;
              if (rowCell.dataType === CellDataType.OBJECT) {
                if (!rowCell.value) return;
                newCell = {
                  dataType: rowCell.dataType,
                  value: {
                    ...rowCell.value,
                    data: extractedData[index] ? JSON.stringify([extractedData[index]]) : '',
                    refData: value.refData?.value?.[index] ? {
                      dataType: value.refData.dataType,
                      value: [value.refData.value[index]],
                    } : undefined,
                  },
                };
              } else {
                newCell = {
                  dataType: CellDataType.OBJECT,
                  value: {
                    identifier: id,
                    sequence: id,
                    filterFactor: id,
                    data: extractedData[index] ? JSON.stringify([extractedData[index]]) : '',
                    refData: value.refData?.value?.[index] ? {
                      dataType: value.refData.dataType,
                      value: [value.refData.value[index]],
                    } : undefined,
                  },
                };
              }

              fakeRecord.cells = { [this.field.id]: newCell };
              arr.items.push({
                value: {
                  rId: r.id,
                  data: newCell,
                },
                // !!! 用内部方法，因为这里传进去的r是个假的行，不能用缓存
                getConstValue: () => FxValue.makeFlattenConstValueByConvertResult(this._fromCellValue(fakeRecord)),
              });
            });
          } else {
            arr.items.push({
              value: {
                rId: r.id,
                data: rawCell,
              },
              getConstValue: () => FxValue.makeConstValueByConvertResult(this.fromCellValue(r)),
            });
          }
        }
      } else {
        arr.items.push({
          value: {
            rId: r.id,
            data: rawCell,
          },
          getConstValue: () => FxValue.makeConstValueByConvertResult(this._fromCellValue(r)),
        });
      }
    };

    if (row) {
      dealSingleRow(row);
    } else {
      const records = this.sheet.getAllRecordVM();
      records.forEach((r) => {
        dealSingleRow(r);
      });
    }

    const notableArray = FxValue.makeNotableArray(arr);
    if (!row) {
      this.cellValueCache.addNotableArray(this.sheetId, this.field.id, notableArray);
    }
    return notableArray;
  };

  getFieldResultFieldType = () => {
    const cached = this.cellValueCache.getResultFieldType(this.sheetId, this.field.id);
    if (cached) {
      return cached;
    }

    const type = getResultFieldType(this.model, this.sheetId, this.field.id);
    this.cellValueCache.addResultFieldType(this.sheetId, this.field.id, type);
    return type;
  };

  getTextToSelectOptionMap() {
    const cached = this.cellValueCache.getTextToSelectOptionMap(this.sheetId, this.field.id);
    if (cached) return cached;
    const optionsMap: fieldTextToSelectOptionMapCacheItem = {};
    const options: SelectConfigEnum[] = get(this.field.config, 'renderFieldConfig.props.enums') ?? [];
    if (Array.isArray(options)) {
      options.forEach((opt) => {
        if (
          (opt.id && typeof opt.id === 'string')
          && (opt.value && typeof opt.value === 'string')
        ) {
          optionsMap[opt.value] = opt.id;
        }
      });
    }
    this.cellValueCache.addTextToSelectOptionMap(this.sheetId, this.field.id, optionsMap);
    return optionsMap;
  }

  getTextToFlowOptionMap() {
    const cached = this.cellValueCache.getTextToFlowOptionMap(this.sheetId, this.field.id);
    if (cached) return cached;
    const optionsMap: fieldTextToFlowOptionMapCacheItem = {};
    const options: IStageNode[] = get(this.field.config, 'renderFieldConfig.props.options') ?? [];
    if (Array.isArray(options)) {
      options.forEach((opt) => {
        if (
          (opt.id && typeof opt.id === 'string')
          && (opt.name && typeof opt.name === 'string')
        ) {
          optionsMap[opt.name] = opt.id;
        }
      });
    }
    this.cellValueCache.addTextToFlowOptionMap(this.sheetId, this.field.id, optionsMap);
    return optionsMap;
  }

  /**
   * - 将日期相关字段值转换为 OADate 参与公式计算。`date, createTime, updateTime`;
   */
  private generateFromCellValueFunc(): FromCellValueFunc {
    const { field } = this;

    switch (field.type) {
      case WidgetTypeEnums.createdTime:
        return (row) => FxValue.makeNumber(OADate.fromDate(row.createdTime));
      case WidgetTypeEnums.updatedTime:
        return (row) => FxValue.makeNumber(OADate.fromDate(row.updatedTime));
      case WidgetTypeEnums.creator: {
        return (row) => {
          let name: string | undefined;
          try {
            const creator = JSON.parse(row.creator.data);
            name = get(creator, 'nick') ?? get(creator, 'name');
          } catch (err) { /* nothing */ }
          return typeof name === 'string' ? name : undefined;
        };
      }
      case WidgetTypeEnums.updater: {
        return (row) => {
          let name: string | undefined;
          try {
            const creator = JSON.parse(row.updater.data);
            name = get(creator, 'nick') ?? get(creator, 'name');
          } catch (err) { /* nothing */ }

          return typeof name === 'string' ? name : undefined;
        };
      }
      case WidgetTypeEnums.checkbox: {
        return (row) => {
          return convertBooleanCell(this.extractCellValue(row));
        };
      }
      case WidgetTypeEnums.flow: {
        return (row) => {
          const cell = this.extractCellValue(row);
          const value = getAndEnsureObjectCellValue(cell);
          if (!value) {
            return;
          }
          const optionsMap = this.getFieldFlowOptions();
          const selected = getSelectedOfFlowValue(value, optionsMap);
          return selected?.[0];
        };
      }
      case WidgetTypeEnums.select: {
        return (row) => {
          const cell = this.extractCellValue(row);
          const value = getAndEnsureObjectCellValue(cell);
          if (!value) {
            return;
          }
          const optionsMap = this.getFieldSelectOptions();
          const selected = getSelectedOfSelectValue(value, optionsMap);
          return selected?.[0];
        };
      }
      case WidgetTypeEnums.multiSelect: {
        return (row) => {
          const cell = this.extractCellValue(row);
          const value = getAndEnsureObjectCellValue(cell);
          if (!value) {
            return;
          }
          const optionsMap = this.getFieldSelectOptions();
          const selected = getSelectedOfSelectValue(value, optionsMap);
          if (selected?.length) {
            return FxValue.makeConstArray([selected]);
          }
        };
      }
      case WidgetTypeEnums.person: {
        return (row) => {
          const cell = this.extractCellValue(row);
          const value = getAndEnsureObjectCellValue(cell);
          if (!value) {
            return;
          }
          const { sequence, filterFactor } = value;
          let people = sequence.split(',');
          if (people.length !== filterFactor.length) {
            people = deserializePersonCell(value).map((m) => m.name);
          }
          if (people.length) {
            return people.length === 1 ? people[0] : FxValue.makeConstArray([people]);
          }
        };
      }
      case WidgetTypeEnums.group: {
        return (row) => {
          const cell = this.extractCellValue(row);
          const value = getAndEnsureObjectCellValue(cell);
          if (!value) {
            return;
          }
          const { sequence, filterFactor } = value;
          let groups = sequence.split(',');
          if (groups.length !== filterFactor.length) {
            groups = deserializeGroup(value).map((m) => m.name);
          }
          if (groups.length) {
            return groups.length === 1 ? groups[0] : FxValue.makeConstArray([groups]);
          }
        };
      }
      case WidgetTypeEnums.department: {
        return (row) => {
          const cell = this.extractCellValue(row);
          const value = getAndEnsureObjectCellValue(cell);
          if (!value) {
            return;
          }
          const { sequence, filterFactor } = value;
          let depts = sequence.split(',');
          if (depts.length !== filterFactor.length) {
            depts = deserializeDepartment(value).map((m) => m.name);
          }
          if (depts.length) {
            return depts.length === 1 ? depts[0] : FxValue.makeConstArray([depts]);
          }
        };
      }
      case WidgetTypeEnums.file:
      case WidgetTypeEnums.image: {
        return (row) => {
          const cell = this.extractCellValue(row);
          const value = getAndEnsureObjectCellValue(cell);
          if (!value) {
            return;
          }
          const { sequence, filterFactor, data } = value;
          if (filterFactor.length <= 1) {
            return sequence;
          }

          let files: unknown;
          try {
            files = JSON.parse(data) as unknown;
          } catch (err) { /** nothing */ }

          if (Array.isArray(files)) {
            const vals = files.map((item) => item?.name).filter((name) => typeof name === 'string');
            if (vals.length) {
              return FxValue.makeConstArray([vals]);
            }
          }
        };
      }
      case WidgetTypeEnums.richText:
      case WidgetTypeEnums.geolocation:
      case WidgetTypeEnums.address:
      case WidgetTypeEnums.sign:
      case WidgetTypeEnums.link: {
        return (row) => {
          const cell = this.extractCellValue(row);
          const value = getAndEnsureObjectCellValue(cell);
          if (!value) {
            return;
          }
          if (typeof value.sequence === 'string') {
            return value.sequence;
          }
        };
      }
      case WidgetTypeEnums.association: {
        return (row) => {
          const cell = this.extractCellValue(row);
          const value = getAndEnsureObjectCellValue(cell);
          if (!value) {
            return;
          }
          const { filterFactor } = value;
          if (filterFactor && Array.isArray(filterFactor) && filterFactor.length) {
            const constArr: ConstArrayItem[] = [];
            for (let i = 0; i < filterFactor.length; i++) {
              const item = filterFactor[i];
              // 应只有 string, number 值
              if (typeof item !== 'number' && typeof item !== 'string') {
                return;
              }
              constArr.push(item);
            }
            return FxValue.makeConstArray([constArr]);
          }
        };
      }
      case WidgetTypeEnums.formula:
      case WidgetTypeEnums.lookup:
      case WidgetTypeEnums.filterUp: {
        return (row) => {
          const cell = this.extractCellValue(row);
          if (!cell) {
            return;
          }
          switch (cell.dataType) {
            case CellDataType.AUTO_NUM:
              return convertAutoNumCell(cell, field, row.createdTime);
            case CellDataType.BOOLEAN:
              return convertBooleanCell(cell);
            case CellDataType.NUMBER:
              return convertNumberCell(cell);
            case CellDataType.DATE:
              return convertDateCell(cell);
            case CellDataType.STRING:
              return convertStringCell(cell);
            case CellDataType.OBJECT:
              return convertObjectValueForFormula(cell);
            default:
          }
        };
      }
      case WidgetTypeEnums.number: {
        return (row) => {
          const cell = this.extractCellValue(row);
          if (cell?.value == null) {
            return;
          }
          // 特殊处理：设置了百分比的数字字段，存的值有问题 `10% 存了 10、而非 0.1` version 1 之前的版本，需要兼容
          const bFixPercent =
            field.type === WidgetTypeEnums.number
            && get(field.config, pathFormatInField) === FORMAT_PERCENT
            && get(field.config, 'renderFieldConfig.props.version') === undefined;
          return convertNumberCell(cell, bFixPercent);
        };
      }
      case WidgetTypeEnums.currency:
      case WidgetTypeEnums.starRating:
      case WidgetTypeEnums.progress: {
        return (row) => {
          return convertNumberCell(this.extractCellValue(row));
        };
      }
      case WidgetTypeEnums.autoNumber: {
        return (row) => {
          return convertAutoNumCell(this.extractCellValue(row), field, row.createdTime);
        };
      }
      case WidgetTypeEnums.text:
      case WidgetTypeEnums.textarea:
      case WidgetTypeEnums.barcode:
      case WidgetTypeEnums.email:
      case WidgetTypeEnums.telephone:
      case WidgetTypeEnums.idCard:
      case WidgetTypeEnums.primaryDoc: {
        return (row) => {
          return convertStringCell(this.extractCellValue(row));
        };
      }
      case WidgetTypeEnums.date: {
        return (row) => {
          return convertDateCell(this.extractCellValue(row));
        };
      }
      default: {
        logger.error(field.type as never, `[FxCellValueConverter] unknown field type: ${field.type}`);
        return () => undefined;
      }
    }
  }

  // 基于 identifier 还原 cell value
  private generateExtractCellValueByIdentifierFunc(): CellValueByIdentifierExtractor {
    const { field } = this;

    switch (field.type) {
      case 'person':
        return (row, identifier) => {
          const cell = this.extractCellValue(row);
          if (cell === null) return null;
          const values = deserializePersonCell(cell.value as ObjectCellValue);
          const itemIndex = values.findIndex((v) => v.key === identifier);
          if (itemIndex < 0) return null;
          const fxCellValues = getConstArrayFromRow(row, this);
          if (fxCellValues.length === 0) return null;
          const fxCellValue = fxCellValues[itemIndex];
          const originCell = {
            dataType: cell.dataType,
            value: serializePersonCell([values[itemIndex]]),
          } as RowCell;
          return [fxCellValue, originCell];
        };
      case 'group':
        return (row, identifier) => {
          const cell = this.extractCellValue(row);
          if (cell === null) return null;
          const values = deserializeGroup(cell.value as ObjectCellValue);
          const itemIndex = values.findIndex((v) => v.cid === identifier);
          if (itemIndex < 0) return null;
          const fxCellValues = getConstArrayFromRow(row, this);
          if (fxCellValues.length === 0) return null;
          const fxCellValue = fxCellValues[itemIndex];
          const originCell = {
            dataType: cell.dataType,
            value: serializeGroup([values[itemIndex]]),
          } as RowCell;
          return [fxCellValue, originCell];
        };
      case 'department':
        return (row, identifier) => {
          const cell = this.extractCellValue(row);
          if (cell === null) return null;
          const values = deserializeDepartment(cell.value as ObjectCellValue);
          const itemIndex = values.findIndex((v) => v.key === identifier);
          if (itemIndex < 0) return null;
          const fxCellValues = getConstArrayFromRow(row, this);
          if (fxCellValues.length === 0) return null;
          const fxCellValue = fxCellValues[itemIndex];
          const originCell = {
            dataType: cell.dataType,
            value: serializeDepartment([values[itemIndex]]),
          } as RowCell;
          return [fxCellValue, originCell];
        };
      case 'select':
        return (row, identifier) => {
          const cell = this.extractCellValue(row);
          if (cell === null) return null;
          const values = deserializeSingleSelect(cell.value as ObjectCellValue);
          const itemIndex = values.findIndex((v) => v === identifier);
          if (itemIndex < 0) return null;
          const fxCellValues = getConstArrayFromRow(row, this);
          if (fxCellValues.length === 0) return null;
          const fxCellValue = fxCellValues[itemIndex];
          const originCell = {
            dataType: cell.dataType,
            value: serializeSingleSelect(values[itemIndex]),
          } as RowCell;
          return [fxCellValue, originCell];
        };
      case 'multiSelect':
        return (row, identifier) => {
          const cell = this.extractCellValue(row);
          if (cell === null) return null;
          const values = deserializeMultiSelect(cell.value as ObjectCellValue);
          const itemIndex = values.findIndex((v) => v === identifier);
          if (itemIndex < 0) return null;
          const fxCellValues = getConstArrayFromRow(row, this);
          if (fxCellValues.length === 0) return null;
          const fxCellValue = fxCellValues[itemIndex];
          const originCell = {
            dataType: cell.dataType,
            value: serializeMultiSelect([values[itemIndex]], this.field.config.renderFieldConfig?.props as SelectConfig),
          } as RowCell;
          return [fxCellValue, originCell];
        };
      case 'association':
        // identifier 需要预先处理成 rowId
        return (row, identifier) => {
          const cell = this.extractCellValue(row);
          if (cell === null) return null;
          const values = deserializeAssociation(cell.value as ObjectCellValue);
          const itemIndex = values.findIndex((v) => v.rowId === identifier);
          if (itemIndex < 0) return null;
          const fxCellValues = getConstArrayFromRow(row, this);
          if (fxCellValues.length === 0) return null;
          const fxCellValue = fxCellValues[itemIndex];
          const originCell = {
            dataType: cell.dataType,
            value: serializeAssociation([values[itemIndex]]),
          } as RowCell;
          return [fxCellValue, originCell];
        };
      case 'file':
        // identifier 需要预先处理成 url
        return (row, identifier) => {
          const cell = this.extractCellValue(row);
          if (cell === null) return null;
          const values = deserializeFile(cell.value as ObjectCellValue);
          const itemIndex = values.findIndex((v) => v.url === identifier);
          if (itemIndex < 0) return null;
          const fxCellValues = getConstArrayFromRow(row, this);
          if (fxCellValues.length === 0) return null;
          const fxCellValue = fxCellValues[itemIndex];
          const originCell = {
            dataType: cell.dataType,
            value: serializeFile([values[itemIndex]]),
          } as RowCell;
          return [fxCellValue, originCell];
        };
      case 'filter-up':
      case 'lookup':
      case 'formula':
        return (row, identifier) => {
          const cell = this.extractCellValue(row);
          if (cell === null) return null;

          if (isFxOriginObjectValue(cell.value)) {
            const values = cell.value.refData.value;
            const itemIndex = values.findIndex((v) => {
              if (isObjectCellValue(v)) {
                const cellIdentifier = getIdentifiersByCellValue(v, this.field);
                return cellIdentifier?.includes(identifier as string);
              }
              return `${v}` === identifier;
            });
            if (itemIndex < 0) return null;
            const fxCellValues = getConstArrayFromRow(row, this);
            if (fxCellValues.length === 0) return null;
            const fxCellValue = fxCellValues[itemIndex];
            const originValue = values[itemIndex];
            const originCell = {
              dataType: cell.value.refData.dataType,
              value: originValue,
            } as RowCell;
            return [fxCellValue, originCell];
          }

          switch (cell.dataType) {
            case CellDataType.AUTO_NUM:
            case CellDataType.BOOLEAN:
            case CellDataType.NUMBER:
            case CellDataType.DATE:
            case CellDataType.STRING: {
              const fxCellValue = this.fromCellValue(row);
              if (isNil(fxCellValue)) return null;
              return [fxCellValue as ConstArrayItem, cell];
            }
            case CellDataType.OBJECT: {
              if (isNil(cell.value)) return null;
              const values = getFxValueOfFxObjValue(cell.value);
              if (isNil(values)) return null;
              const itemIndex = values.findIndex((v) => {
                if (isNotableFormulaErrorValue(v)) {
                  return v.fxError === identifier;
                } else {
                  return `${v}` === identifier;
                }
              });
              const fxCellValues = getConstArrayFromRow(row, this);
              const fxCellValue = fxCellValues[itemIndex];
              const data: FxObjectValue = [];
              const newFilterFactors: string[] = [];
              if (FxValue.isError(fxCellValue)) {
                data.push({ fxError: fxCellValue.value.type });
                newFilterFactors.push(fxCellValue.value.type);
              } else if (!FxValue.isNull(fxCellValue)) {
                data.push(fxCellValue);
                newFilterFactors.push(`${fxCellValue}`);
              }
              const sequence = newFilterFactors.join(',');
              const value: ObjectCellValue = {
                identifier: sequence,
                sequence,
                filterFactor: newFilterFactors.slice().sort(),
                data: JSON.stringify(data),
              };
              const originCell: RowCell = {
                dataType: CellDataType.OBJECT,
                value,
              };
              return [fxCellValue, originCell];
            }
            default:
              return null;
          }
        };
      default:
        return (row, identifier) => {
          const cell = this.extractCellValue(row);
          if (cell === null) return null;

          const cellIdentifiers = getValueByIdentifier(row, this.field);
          // 理论上这里不会出现多值
          if (Array.isArray(cellIdentifiers) && cellIdentifiers.length > 1) {
            return null;
          }

          const currentIdentifier = Array.isArray(cellIdentifiers) ? cellIdentifiers[0] : cellIdentifiers;
          if (currentIdentifier === null) return null;

          if (identifier !== currentIdentifier) return null;

          const fxCellValues = getConstArrayFromRow(row, this);
          if (!fxCellValues) return null;

          const fxCellValue = fxCellValues[0];
          const originCell = {
            dataType: cell.dataType,
            value: cell.value,
          } as RowCell;

          return [fxCellValue, originCell];
        };
    }
  }
}
