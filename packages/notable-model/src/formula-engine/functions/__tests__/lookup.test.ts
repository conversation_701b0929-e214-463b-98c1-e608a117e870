import lookupTestData from './testData/lookup.json';
import { CellDataType, EventMap, RowCell } from '@ali/notable-common';
import { Model } from '../../../model';
import { FormulaEngine } from '../../formula-engine';

type DeepPartial<T> = Partial<{ [P in keyof T]: DeepPartial<T[P]> }>;

const sheetId = 'hERWDMS';
const results: Record<string, Array<DeepPartial<RowCell> | undefined>> = {
  // unique
  EKDq7wI: [
    { dataType: CellDataType.OBJECT, value: { sequence: '岳韬,高翔,蓝锋专属钉,aaa' } },
    { dataType: CellDataType.OBJECT, value: { sequence: '岳韬,高翔,蓝锋专属钉,aaa' } },
    { dataType: CellDataType.OBJECT, value: { sequence: '岳韬,高翔,蓝锋专属钉,aaa' } },
    { dataType: CellDataType.OBJECT, value: { sequence: '岳韬,高翔,蓝锋专属钉,aaa' } },
    { dataType: CellDataType.OBJECT, value: { sequence: '岳韬,高翔,蓝锋专属钉,aaa' } },
  ],
  // lookup
  e8qY9OF: [
    { dataType: CellDataType.OBJECT, value: { sequence: 'f7cf3493-835a-41aa-b9aa-8e41f2c14261,FibPMMIivW' } },
    undefined,
    { dataType: CellDataType.OBJECT, value: { sequence: 'CuFwt1ORkH,XBWNhr1Hlo' } },
    { dataType: CellDataType.OBJECT, value: { sequence: 'AZYsSroy6E' } },
    { dataType: CellDataType.OBJECT, value: { sequence: 'b8c1f338-aefd-4755-a088-7ddaa9eb9e6f,3dzlJGCtCt' } },
  ],
  // lookup, 条件是静态值
  bSPGrrs: [
    { dataType: CellDataType.OBJECT, value: { sequence: '1,2222,3' } },
    { dataType: CellDataType.OBJECT, value: { sequence: '1,2222,3' } },
    { dataType: CellDataType.OBJECT, value: { sequence: '1,2222,3' } },
    { dataType: CellDataType.OBJECT, value: { sequence: '1,2222,3' } },
    { dataType: CellDataType.OBJECT, value: { sequence: '1,2222,3' } },
  ],
  // lookup，参数带if条件
  qe6fui3: [
    { dataType: CellDataType.OBJECT, value: { sequence: 'f7cf3493-835a-41aa-b9aa-8e41f2c14261,FibPMMIivW' } },
    { dataType: CellDataType.OBJECT, value: { sequence: 'b8c1f338-aefd-4755-a088-7ddaa9eb9e6f,3dzlJGCtCt' } },
    { dataType: CellDataType.OBJECT, value: { sequence: 'b8c1f338-aefd-4755-a088-7ddaa9eb9e6f,3dzlJGCtCt' } },
    { dataType: CellDataType.OBJECT, value: { sequence: 'b8c1f338-aefd-4755-a088-7ddaa9eb9e6f,3dzlJGCtCt' } },
    { dataType: CellDataType.OBJECT, value: { sequence: 'b8c1f338-aefd-4755-a088-7ddaa9eb9e6f,3dzlJGCtCt' } },
  ],
  // 直接引用
  kb3DFGL: [
    { dataType: CellDataType.OBJECT, value: { sequence: '选项一,选项一,选项一,选项二,选项二' } },
    { dataType: CellDataType.OBJECT, value: { sequence: '选项一,选项一,选项一,选项二,选项二' } },
    { dataType: CellDataType.OBJECT, value: { sequence: '选项一,选项一,选项一,选项二,选项二' } },
    { dataType: CellDataType.OBJECT, value: { sequence: '选项一,选项一,选项一,选项二,选项二' } },
    { dataType: CellDataType.OBJECT, value: { sequence: '选项一,选项一,选项一,选项二,选项二' } },
  ],
};


function testFieldResult(model: Model) {
  const rows = model.getSheet(sheetId)?.getAllRows_Ordered();
  Object.entries(results).forEach(([fieldId, results]) => {
    rows?.forEach((r, index) => {
      if (results[index] === undefined) {
        expect(r.cells?.[fieldId]).toBeUndefined();
        return;
      }
      expect(r.cells?.[fieldId]).toMatchObject(results[index] as any);
    });
  });
}

describe('lookup', () => {
  it('testAllInOne - normal', () => {
    const model = new Model({ usingInMemoryDB: true, keepExistingRows: true });
    model.setFxEngine(new FormulaEngine(model, { asyncCalc: false, enableIncrementalCalc: 'manual', skipInitialFullCalc: true, enableSheetFormulaRef: true, enableSplitAst: false }));
    model.fromJSON(lookupTestData as any);
    model.fxEngine?.forceFullCalc();
    testFieldResult(model);
  });

  it('testAllInOne - splitAST', () => {
    const model = new Model({ usingInMemoryDB: true, keepExistingRows: true });
    model.setFxEngine(new FormulaEngine(model, { asyncCalc: false, enableIncrementalCalc: 'manual', skipInitialFullCalc: true, enableSheetFormulaRef: true, enableSplitAst: true }));
    model.fromJSON(lookupTestData as any);
    model.fxEngine?.forceFullCalc();
    testFieldResult(model);
  });
});
