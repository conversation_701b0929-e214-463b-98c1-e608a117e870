import {
  FxValue,
  EvaluateContext,
} from '@ali/zongheng-formula';
import { FxCellValueConvertor } from '../../utils';
import type { RecordDTO, RowCell, RowId } from '../../row/types';
import type { Model } from '../../model';
import type { FieldAddr } from '../types';
import type { CalcField, LookupSource } from '../model/calc-field';

export type FnGetSheet = Model['getSheet'];

export type JoinResult = undefined | FxValue.Value<'NULL' | 'CONST_ARRAY'>;

export interface LookupContext {
  values: undefined | ValuesTarget;
  joinResultCache?: () => JoinResult;
  setResultCache?: (c: JoinResult) => void;
  cache: undefined | FxValue.Value<'NULL' | 'CONST_ARRAY'>;
  fxCellValueConvertor?: FxCellValueConvertor;
}

export type { EvaluateContext };

export type EvaluateCtxForNotable = EvaluateContext & {
  rowId: RowId;
  baseField: CalcField;
  getLookupValues: HookGetLookupValues;
  getLookupUniqueValues: HookGetLookupValues;
  getJoinResult: HookGetJoinResult;
  getSheet: FnGetSheet;
  shouldAddCellItem: () => boolean;
  addCellItem: (value: RowCell) => void;
  lookupContext: LookupContext;
};

export type ValuesTarget = {
  valuesSource: LookupSource;
  valuesField: FieldAddr;
};

export type HookGetLookupValues = (
  context: Pick<EvaluateCtxForNotable, 'sheetId' | 'baseField' | 'getSheet' | 'addCellItem' | 'shouldAddCellItem'>,
  rowId: RowId,
  lookupContext: LookupContext,
  matchedRecords: RecordDTO[],
  shouldAddCellItem?: boolean,
) => FxValue.Value<'NULL' | 'CONST_ARRAY'>;

export type HookGetJoinResult = (
  context: Pick<EvaluateCtxForNotable, 'sheetId' | 'baseField' | 'getSheet' | 'addCellItem' | 'shouldAddCellItem'>,
  rowId: RowId,
  lookupContext: LookupContext,
) => FxValue.Value<'NULL' | 'CONST_ARRAY'>;
