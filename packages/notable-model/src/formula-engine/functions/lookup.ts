import {
  AnyFormulaValue,
  FunctionCategory,
  FxValue,
  defineFunction,
} from '@ali/zongheng-formula';
import { FilterUpFieldData, RecordDTO } from '@ali/notable-common';
import { tryGetSelectOptionIfSelectField } from '../utils/filter-up';
import { EvaluateCtxForNotable, LookupContext } from './types';

const fnLookup = (ctx: unknown) => (
  lookupValue: AnyFormulaValue,
  lookupSourceField: AnyFormulaValue,
  resultField: AnyFormulaValue,
  mode: AnyFormulaValue,
) => {
  const { rowId, getLookupValues, getSheet, getJoinResult } = ctx as EvaluateCtxForNotable;

  let isContainMode = true;
  if (FxValue.isNumber(mode)) {
    isContainMode = (mode === 1);
  }

  if (FxValue.isNotableArray(lookupSourceField)) {
    if (lookupSourceField.value.type !== 'sheetField') {
      return FxValue.makeError('#REF!');
    }
    if (!FxValue.isNotableArray(resultField) || (resultField.value.type !== 'field' && resultField.value.type !== 'sheetField')) {
      return FxValue.makeError('#REF!');
    }
    if (resultField.value.sId !== lookupSourceField.value.sId) {
      return FxValue.makeError('#REF!');
    }
    if (!resultField.value.fId) {
      return FxValue.makeError('#REF!');
    }
    if (!lookupSourceField.value.fId) {
      return FxValue.makeError('#REF!');
    }

    const sheet = getSheet(lookupSourceField.value.sId);
    if (!sheet) {
      return FxValue.makeError('#REF!');
    }

    let symbol: 'EQ' | 'ANY_OF' = 'EQ';
    const sourceField = sheet.getField(lookupSourceField.value.fId);
    if (isContainMode && (sourceField?.type === 'person' || sourceField?.type === 'multiSelect')) {
      symbol = 'ANY_OF';
    }

    const filters: FilterUpFieldData['filters'] = [];
    if (FxValue.isConstantValue(lookupValue)) {
      let constValue: string | number | boolean = '';
      if (FxValue.isError(lookupValue)) {
        constValue = lookupValue.value.type;
      } else if (FxValue.isNull(lookupValue)) {
        constValue = '';
      } else {
        constValue = lookupValue;
        if (sourceField?.type === 'select' || sourceField?.type === 'multiSelect') {
          const getConverter = () => sheet.parent.fxCellValueConvertorManager.getConvertor(
            sheet.id,
            sourceField.id,
          );
          constValue = `${tryGetSelectOptionIfSelectField(lookupValue, sourceField, symbol, getConverter)}`;
        }
      }
      filters.push({
        fieldId: lookupSourceField.value.fId,
        symbol,
        value: constValue,
        link: 'AND',
      });
    } else if (FxValue.isNotableArray(lookupValue)) {
      if (!lookupValue.value.fId) {
        return FxValue.makeError('#REF!');
      }
      filters.push({
        fieldId: lookupSourceField.value.fId,
        symbol,
        value: { fieldId: lookupValue.value.fId },
        link: 'AND',
      });
    }

    const lookupContext: LookupContext = {
      values: {
        valuesField: {
          fieldId: resultField.value.fId,
          sheetId: resultField.value.sId,
        },
        valuesSource: {
          type: 'filter',
          filters,
        },
      },
      cache: undefined,
      fxCellValueConvertor: sheet.parent.fxCellValueConvertorManager.getConvertor(resultField.value.sId, resultField.value.fId),
    };

    sheet.rowDB.ensureIndexes(lookupSourceField.value.fId, [lookupSourceField.value.fId]);
    const joinResult = getJoinResult(ctx as EvaluateCtxForNotable, rowId, lookupContext);
    return getLookupValues(ctx as EvaluateCtxForNotable, rowId, lookupContext, joinResult.value as unknown as RecordDTO[], true);
  }
  // 查找区域不是整列引用，后续再支持
  return FxValue.makeError('#REF!');
};

export function injectLookupFunction() {
  defineFunction('LOOKUP', {
    funcWithContext: fnLookup,
    category: FunctionCategory.Alidoc,
    argOptions: {
      minArgs: 3,
      maxArgs: 4,
      repeatStep: 1,
      argTypes: [
        { acceptArray: true },
        { acceptArray: true },
        { acceptArray: true },
        {},
      ],
    },
  });
}
