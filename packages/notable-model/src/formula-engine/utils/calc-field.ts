import { isNil } from 'lodash-es';
import {
  isValidLookupConfig,
  isValidFilterUpConfig,
  isValidFormulaConfig,
  isCalcField,
  type FilterUpFieldData,
  type SheetId,
  type FieldDTO,
  isRelativeDateObj,
  FieldId,
  isSupportedResultFieldType,
} from '@ali/notable-common';
import type { CalcField } from '../model/calc-field';
import { detectFormulaResultInfo } from '../formula-parser/detect-formula-result';
import { GetFieldFunc } from './common';
import { AGGREGATOR_FOLLOW_SOURCE, getLookupValuesFieldByConfig } from './lookup-field';
import { hasVolatileFunc } from './rpn';

export const getFilterUpValueFieldByConfig = (config: FilterUpFieldData, getField: GetFieldFunc) => {
  if (!config) return null;
  const field = getField(config.targetSheet, config.valuesField);
  if (!field) return null;
  return field;
};

export interface CalcValuesFieldResult {
  valuesField: FieldDTO;
  nextSheetId: string;
}

export const getCalcValuesField = (
  sheetId: SheetId,
  calcFieldProps: {} | undefined,
  getField: GetFieldFunc,
  depth = 0,
): CalcValuesFieldResult | null => {
  if (!calcFieldProps) return null;
  // 递归次数大于 100 直接退出，避免死循环
  if (depth >= 100) return null;

  let valuesField: FieldDTO | null = null;
  let nextSheetId = '';

  if (isValidLookupConfig(calcFieldProps) && AGGREGATOR_FOLLOW_SOURCE.includes(calcFieldProps.aggregator)) {
    const valuesFieldInfo = getLookupValuesFieldByConfig(sheetId, calcFieldProps, getField);
    if (!valuesFieldInfo) return null;
    valuesField = valuesFieldInfo.field;
    nextSheetId = valuesFieldInfo.sheetId;
  }

  if (isValidFilterUpConfig(calcFieldProps) && AGGREGATOR_FOLLOW_SOURCE.includes(calcFieldProps.aggregator)) {
    valuesField = getFilterUpValueFieldByConfig(calcFieldProps, getField);
    nextSheetId = calcFieldProps.targetSheet;
  }

  if (isValidFormulaConfig(calcFieldProps)) {
    const { formula } = calcFieldProps;
    if (formula && typeof formula === 'object') {
      const resultFieldInfo = detectFormulaResultInfo(formula, sheetId, (sid, fid) => {
        const refField = getField(sid, fid);
        if (refField) {
            return { resultFieldType: refField.type, resultSheetId: sid, resultFieldId: fid };
        }
      });
      if (resultFieldInfo && resultFieldInfo.resultSheetId && resultFieldInfo.resultFieldId) {
        nextSheetId = resultFieldInfo.resultSheetId;
        valuesField = getField(resultFieldInfo.resultSheetId, resultFieldInfo.resultFieldId);
      }
    }
  }

  if (isCalcField(valuesField?.type)) {
    const configProps = valuesField?.config?.renderFieldConfig?.props;
    if (configProps) {
      return getCalcValuesField(nextSheetId, configProps, getField, depth + 1);
    }
  }

  return valuesField ? { valuesField, nextSheetId } : null;
};

export const shouldUpgradeCalcField = (
  sheetId: SheetId,
  fieldId: FieldId,
  getField: GetFieldFunc,
) => {
  const field = getField(sheetId, fieldId);
  if (field?.type !== 'filter-up' && field?.type !== 'lookup') {
    return false;
  }
  const configProps = field.config?.renderFieldConfig?.props;
  if (!configProps) {
    return false;
  }
  if (configProps.aggregator !== 'VALUES' && configProps.aggregator !== 'UNIQUE') {
    return false;
  }

  const { valuesField } = getCalcValuesField(sheetId, configProps, getField) || {};
  if (!valuesField) {
    return false;
  }
  if (!isSupportedResultFieldType(valuesField.type)) {
    return false;
  }

  return isNil(configProps.valuesVersion) || isNil(configProps.resultFieldType);
};

export function isVolatileCalcField(cf: CalcField) {
  if (cf.rpn && hasVolatileFunc(cf.rpn)) {
    return true;
  }
  if (cf.type === 'lookup' && cf.valuesSource.type === 'filter') {
    return cf.valuesSource.filters.some((f) => {
      return f.value && isRelativeDateObj(f.value);
    });
  }
  return false;
}
