/* eslint-disable max-len */
import { setGlobalLocal, LocaleType } from '@ali/zongheng-common';
import { Event, FieldId, IFormulaEngine, LocalOperationActon, SheetId } from '@ali/notable-common';
import type { Model } from '../model';
import type { WorkbookDTO } from '../workbook/types';
import type { Operation } from '../operations/types';
import type { Row } from '../row';
import type { FieldAddr } from './types';
import type { CalcField, FilterUpCalcField, CompositeFilterUpCalcField } from './model/calc-field';
import { CalcFieldManager, isDeltaMakingDirty } from './model';
import { CalcResultChangeData, FxCalcContext, HookCalcSliceDone, QOCalcChain, QOCalcChainOptions, RowsCalculator } from './model/rows-calculator';
import { isCalcFieldFinallyCallingFunction } from './utils/iter-calc-field-by-ref-chain';
import { buildAllResultFieldType } from './utils/build-all-result-field-type';
import { initFuncsForNotable } from './functions';

const LOCALE_MAP: Record<string, LocaleType> = {
  'zh-CN': 'zh',
  'zh-HK': 'zh',
  'zh-TW': 'zh',
  'ja-JP': 'ja',
  'en-US': 'en',
};

export interface ExternalFxEngine {
  evaluate: (
    calcField: FilterUpCalcField | CompositeFilterUpCalcField,
    ctx: FxCalcContext,
    rows: Row[],
  ) => CalcResultChangeData[];
}

/**
 * - `manual-full` 尚未启用增量计算，只能手动触发全量计算
 * - `manual-incremental` 启用了增量计算，需要手动触发增量计算
 * - `auto-incremental` 启用了增量计算，每次 fromJSON or applyOP 时都自动触发增量计算
 */
export type FxCalcMode = 'manual-full' | 'manual-incremental' | 'auto-incremental';

export type FormulaEngineAppConfig = {
  enableIncrementalCalc: false | 'auto' | 'manual';
  asyncCalc: boolean;
  externalFxEngine?: ExternalFxEngine;
  skipInitialFullCalc?: boolean;
  enableSheetFormulaRef?: boolean;
  enableSplitAst?: boolean;
  onlyCalcActiveSheet?: boolean;
};

export class FormulaEngine implements IFormulaEngine {
  private readonly calcFieldManager: CalcFieldManager;
  private readonly calculator: RowsCalculator;
  private readonly evt = new Event<{ start: HookCalcSliceDone }>();

  private readonly enableSheetFormulaRef: boolean;

  constructor(
    private readonly model: Model,
    opts: FormulaEngineAppConfig,
    private readonly bizLogger?: IBizLogger,
  ) {
    this.calcFieldManager = new CalcFieldManager(this.model);
    this.calculator = new RowsCalculator(this.model, this.calcFieldManager, opts.externalFxEngine, this.bizLogger, opts.enableSheetFormulaRef, opts.enableSplitAst, opts.onlyCalcActiveSheet);
    if (opts.enableIncrementalCalc) {
      this.enableIncrementalCalc(opts.enableIncrementalCalc);
    }
    this.enableSheetFormulaRef = !!opts.enableSheetFormulaRef;

    this.toggleAsyncCalc(opts.asyncCalc);
    initFuncsForNotable(opts.enableSheetFormulaRef);
  }

  destroy() {
    this.calculator.destroy();
    // todo
  }

  // #region 控制异步计算
  toggleAsyncCalc(enable: boolean) {
    return this.calculator.toggleAsyncCalc(enable);
  }
  isAsyncCalc() {
    return this.calculator.isAsyncCalc();
  }
  hasAsyncCalcTasks() {
    return this.calculator.hasAsyncCalcTasks();
  }
  setAsyncCalcDuration(duration: number) {
    return this.calculator.setAsyncCalcDuration(duration);
  }
  setSyncCalcExpireAt(expireAt: number) {
    this.calculator.setSyncCalcExpireAt(expireAt);
  }
  // #endregion
  setCalcSheetIds(sheetIds: string[]) {
    this.calculator.setCalcSheetIds(sheetIds);
  }
  toggleSplitAst(enable: boolean) {
    this.calculator.toggleSplitAst(enable);
  }

  // #region 计算状态调度
  private _calcMode: FxCalcMode = 'manual-full';

  get calcMode() {
    return this._calcMode;
  }

  /** 在启用之前已有的计算字段值认为已经是计算准确的 */
  enableIncrementalCalc(mode: 'auto' | 'manual') {
    this._calcMode = mode === 'auto' ? 'auto-incremental' : 'manual-incremental';
  }

  readonly suspendAutoIncrementalCalc = () => this.calculator.suspendCalc();
  readonly resumeAutoIncrementalCalc = () => this.calculator.resumeCalc();
  readonly calcRpnOnRow = (...args: Parameters<RowsCalculator['calcRpnOnRow']>) => this.calculator.calcRpnOnRow(...args);
  readonly recalculate = (...args: Parameters<RowsCalculator['recalculate']>) => this.calculator.recalculate(...args);
  readonly hasTask = () => this.calculator.hasTask();
  /** 忽略 suspend 状态，强制执行全量计算 */
  readonly forceFullCalc = (...args: Parameters<RowsCalculator['forceFullCalc']>) => this.calculator.forceFullCalc(...args);
  /** 仅计算 sheet 内不涉及引用其它行记录的 formula 字段 */
  readonly calcBasicFxFieldsInSheet = (sheetId: SheetId) => this.calculator.calcBasicFxFieldsInSheet(sheetId);
  /** 计算给定的 calcFields */
  readonly calcCalcFields = (...args: Parameters<RowsCalculator['calcCalcFields']>) => this.calculator.calcCalcFields(...args);
  // #endregion

  readonly getAllCalcFieldsAndRefered = () => this.calcFieldManager.getAllCalcFieldsAndRefered();
  readonly getAllCalcChain = () => this.calcFieldManager.getAllCalcChain();

  readonly getDirtyAndReferedRowsInfo = () => this.calculator.getDirtyAndReferedRowsInfo();

  readonly setLookupDirtyMode = (...args: Parameters<RowsCalculator['setLookupDirtyMode']>) => this.calculator.setLookupDirtyMode(...args);

  readonly calculateTempRecordFormulaChangeset = (...args: Parameters<RowsCalculator['calculateTempRecordFormulaChangeset']>) => this.calculator.calculateTempRecordFormulaChangeset(...args);

  isFieldFormulaAndNotReferingLookup(sheetId: SheetId, fieldId: FieldId) {
    const [circular, normal] = this.getCalcChainInSheet_FormulaOnly(sheetId);
    return normal.includes(fieldId) || circular.includes(fieldId);
  }

  readonly isCalcFieldFinallyCallingFunction = (
    fieldAddr: FieldAddr,
    funcIds: string[],
  ) => isCalcFieldFinallyCallingFunction(fieldAddr, funcIds, this.calcFieldManager.getCalcField);

  getCalcChainInSheet_FormulaOnly(sheetId: SheetId) {
    return this.calcFieldManager.getCalcChainInSheet_FormulaOnly(sheetId);
  }

  fromQOCalcChain(qoCalcChain: QOCalcChain) {
    return this.calcFieldManager.fromQOCalcChain(qoCalcChain);
  }

  /** 对指定 sheet/field 找到其相关的计算字段，以及计算字段直接或间接引用的所有字段 */
  getFieldDependencies(sheetId: string, fieldIds: string[]): Map<string, Set<string>> {
    const related = new Set<{ sheetId: string; fieldId: string }>();

    const setOfHasBeenInQueue: Set<CalcField> = new Set();
    this.calcFieldManager.getCalcFieldsInSheet(sheetId)?.forEach((value) => {
      if (!fieldIds.includes(value.addr.fieldId)) {
        return;
      }

      setOfHasBeenInQueue.add(value);
    });
    const queue = Array.from(setOfHasBeenInQueue);

    let item = queue.shift();
    while (item) {
      related.add(item.addr);

      item.getAllRefs().forEach((ref) => {
        const refItem = this.calcFieldManager.getCalcField(ref);
        if (refItem && !setOfHasBeenInQueue.has(refItem)) {
          queue.push(refItem);
          setOfHasBeenInQueue.add(refItem);
        } else {
          related.add(ref);
        }
      });

      item = queue.shift();
    }

    const result = new Map<SheetId, Set<FieldId>>();
    for (const addr of related) {
      let list = result.get(addr.sheetId);
      if (!list) {
        list = new Set();
        result.set(addr.sheetId, list);
      }
      list.add(addr.fieldId);
    }
    return result;
  }

  isFieldDirty(sheetId: string, fieldId: string) {
    return !!this.calcFieldManager.getCalcField({ sheetId, fieldId })?.isDirty();
  }

  isFieldDependsOnAllRecords(sheetId: string, fieldId: string) {
    const calcField = this.calcFieldManager.getCalcField({ sheetId, fieldId });
    if (!calcField) return false;
    if (calcField?.type === 'formula') {
      return !!calcField.getAllRefs().find((ref) => ref.isEntireField);
    }
    return true;
  }

  getCalcFieldChain(): [chain: FieldAddr[], circular: FieldAddr[], refError: FieldAddr[]] {
    const [calcChain, circular, refError] = this.calcFieldManager.getAllCalcChain();
    const pickAddr = (
      { addr: { sheetId, fieldId } }: CalcField,
    ) => ({ sheetId, fieldId });
    return [
      calcChain.map(pickAddr),
      circular.map(pickAddr),
      refError.map(pickAddr),
    ];
  }

  /**
   * 增量的计算链
   */
  getQOCalcChain = (options?: QOCalcChainOptions) => this.calculator.getQOCalcChain(options);
  /**
   * 全量的计算链
   */
  getFullQOCalcChain = (options?: QOCalcChainOptions) => this.calculator.getFullQOCalcChain(options);

  enableVolatileCalcByOP = () => this.calcFieldManager.enableVolatileCalcByOP();

  setRefErrorIfMissingFlag = (v: boolean) => this.calcFieldManager.setRefErrorIfMissingFlag(v);

  hasVolatileCalcField = () => this.calcFieldManager.hasVolatileCalcField();

  // #region 响应 model 操作
  handleFromJSON(json: WorkbookDTO) {
    // todo: locale 应该是一个文件属性
    // 在文件属性支持前，固定使用 zh
    setGlobalLocal(LOCALE_MAP['zh-CN']);

    this.calcFieldManager.handleFromJSON(json);

    buildAllResultFieldType(this.model, this.calcFieldManager.getAllCalcChain(), this.enableSheetFormulaRef);

    if (this._calcMode !== 'manual-full') {
      this.calculator.handleFromJSON(json);
      if (this._calcMode === 'auto-incremental') {
        this.calculator.recalculate();
      }
    }
  }

  handleApplyOP(ops: Operation[]) {
    if (!ops.length) return;

    this.calcFieldManager.handleApplyOP(ops);

    if (this._calcMode !== 'manual-full') {
      this.calculator.handleApplyOP(ops);
      if (!ops.every(isOpSkipCalc)) {
        if (this._calcMode === 'manual-incremental') {
          this.evt.emit('start', this.model.workbook.sheets);
        } else if (this._calcMode === 'auto-incremental') {
          this.calculator.recalculate();
        }
      }
    }
  }

  isDeltaMakingDirty = (ops: Operation[]) => isDeltaMakingDirty(ops, this.calcFieldManager);
  // #endregion

  clearAllDirtyInfo = () => {
    this.calculator.clearAllDirtyInfo();
    this.evt.emit('start', this.model.workbook.sheets);
  };

  readonly onCalcSliceDone = (cb: HookCalcSliceDone) => {
    const unsub1 = this.evt.on('start', cb);
    const unsub2 = this.calculator.onCalcSliceDone(cb);
    return () => {
      unsub1();
      unsub2();
    };
  };
}

const isOpSkipCalc = (op: Operation) => (
  op.target === 'local'
  && op.action !== LocalOperationActon.SET_FULL_RAW_DATA
  && op.action !== LocalOperationActon.MERGE_SLICE_DATA
  && op.action !== LocalOperationActon.LOCAL_UPDATE_RECORDS
);
