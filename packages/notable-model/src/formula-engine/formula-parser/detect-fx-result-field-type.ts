import { ResultFieldType, isCalcField, isSupportedResultFieldType, logger } from '@ali/notable-common';
import type { FormulaRpn } from '../types';
import { getCalcValuesField } from '../utils/calc-field';
import { AGGREGATOR_FOLLOW_SOURCE, AGGREGATOR_MUST_NUMBER } from '../utils/lookup-field';
import { formulaToRpn, makeGetFieldIdByName, type IModel } from './formula-rpn';
import { detectFormulaResultInfo } from './detect-formula-result';

// 获取字段的类型
// 对 formula 字段，会分析 rpn，递归地求出原值类型 (包含循环检测)；如果无法判断（循环或IF），返回undefined
// 对 lookup/filter-up 字段，会取模型里存的 resultFieldType
// 对 其他字段，直接取 fieldType
export const getResultFieldType = (model: IModel, sheetId: string, fieldId: string): ResultFieldType => {
  const result = _getResultFieldType(model, sheetId, fieldId, new Set());
  if (isSupportedResultFieldType(result?.resultFieldType)) {
    return result;
  }
};

const _getResultFieldType = (model: IModel, sheetId: string, fieldId: string, detectingSet: Set<string>): ResultFieldType => {
  const field = model.getSheet(sheetId)?.getField(fieldId);
  if (!field) return;

  const getField = (sId: string, fId: string) => {
    return model.getSheet(sId)?.getField(fId) ?? null;
  };

  if (isCalcField(field.type)) {
    switch (field.type) {
      case 'formula': {
        return _detectFormulaFieldResultType(model, sheetId, fieldId, detectingSet);
      }
      case 'lookup': {
        const { valuesField, nextSheetId } = getCalcValuesField(sheetId, field?.config.renderFieldConfig?.props, getField) || {};
        if (!valuesField || !nextSheetId) return;
        return { resultFieldType: valuesField?.type, resultFieldId: valuesField?.id, resultSheetId: nextSheetId };
      }
      case 'filter-up': {
        const props = field.config.renderFieldConfig?.props;
        if (!props) return;

        const { aggregator } = props;
        // LATER: 自定义 aggregator 暂未开放
        if (typeof aggregator !== 'string') return;

        if (AGGREGATOR_MUST_NUMBER.includes(aggregator)) {
          return { resultFieldType: 'number' };
        }

        if (AGGREGATOR_FOLLOW_SOURCE.includes(aggregator)) {
          const { valuesField, nextSheetId } = getCalcValuesField(sheetId, field?.config.renderFieldConfig?.props, getField) || {};
          if (!valuesField || !nextSheetId) return;
          return { resultFieldType: valuesField?.type, resultFieldId: valuesField?.id, resultSheetId: nextSheetId };
        }
        return;
      }
      default:
        break;
    }
    return;
  }

  return { resultFieldType: field.type, resultSheetId: sheetId, resultFieldId: fieldId };
};


const _detectFormulaFieldResultType = (
  model: IModel,
  sheetId: string,
  fieldId: string,
  detectingSet: Set<string>,
): ResultFieldType => {
  try {
    if (detectingSet.has(`${sheetId}_${fieldId}`)) {
      // 有循环引用，属于正常的业务错误，直接返回空就好
      return;
    }
    detectingSet.add(`${sheetId}_${fieldId}`);
    const field = model.getSheet(sheetId)?.getField(fieldId);
    if (field?.type !== 'formula') return;

    const formula = field.config.renderFieldConfig?.props?.formula;
    if (!formula) return;

    let rpn: FormulaRpn;
    if (typeof formula === 'string') {
      const parsed = formulaToRpn(formula, makeGetFieldIdByName(model, sheetId));
      if ('formula' in parsed && typeof parsed.formula !== 'undefined') {
        rpn = parsed.formula;
      } else {
        return;
      }
    } else {
      rpn = formula;
    }

    return detectFormulaResultInfo(rpn, sheetId, (sId, fId) => _getResultFieldType(model, sId, fId, detectingSet));
  } catch (e) {
    logger.warn('field to detectFormulaFieldResultType: ', e);
  } finally {
    detectingSet.delete(`${sheetId}_${fieldId}`);
  }
};
