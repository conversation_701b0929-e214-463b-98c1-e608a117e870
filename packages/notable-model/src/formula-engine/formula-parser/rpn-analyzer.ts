/* eslint-disable no-param-reassign */
/* eslint-disable @typescript-eslint/no-non-null-assertion */
import hash from 'hash.js';
import { assertNever } from '@ali/zongheng-common';
import {
  AnyRpnLeafToken, AnyRpnListToken, AnyRpnToken,
  deserializeRpn, RpnData, RpnNameData, RpnNameIndexToken, RPNTreeNode,
  serializeRpn,
} from '@ali/zongheng-formula';
import { CalcFieldResultFieldType, CellDataType, isSupportedResultFieldType, LookupAggregator } from '@ali/notable-common';
import { FilterUpFieldData } from '../../field/types';
import { Calc<PERSON>ield, FilterUpCalcField, FormulaCalcField } from '../model/calc-field';
import { Model } from '../../model';
import { rpnToFormula } from './formula-rpn';

function popListItems<T>(list: T[], count: number): T[] {
  const result: T[] = [];
  for (let i = 0; i < count; i += 1) {
    if (list.length === 0) {
      throw new Error('[Formula][RPN][Utils] Cannot pop enough children');
    }
    result.push(list.pop()!);
  }
  return result.reverse();
}

type TreeNode = RPNTreeNode<unknown, unknown> & {
  parent?: TreeNode;
};

function convertTokenList(rpnNode: AnyRpnListToken) {
  return {
    node: rpnNode,
    children: [],
  };
}

function convertLeafToken(leafNode: AnyRpnLeafToken) {
  return {
    node: leafNode,
    children: [],
  };
}

function convertNameIndexToken(node: RpnNameIndexToken) {
  return { node, children: [] };
}

function pushToTokenList(
  tokenList: TreeNode,
  children: TreeNode[],
) {
  tokenList.children?.push(...children.map((n) => ({ node: n.node, children: n.children, parent: tokenList })));
  return tokenList;
}

export function convertRpnToAst(rpnExpr: string) {
  const rpnData = deserializeRpn(rpnExpr);
  if (!rpnData) return;

  const argTokenStack: TreeNode[] = [];
  for (let idx = 0; idx < rpnData.length; idx++) {
    const rpnItem = rpnData[idx];
    let token: TreeNode | undefined;
    switch (rpnItem.type) {
      case 'F': {
        const tokenList = convertTokenList(rpnItem);
        token = pushToTokenList(tokenList, popListItems(argTokenStack, rpnItem.data.argCount));
        break;
      }
      case 'O': {
        const { data: { opType } } = rpnItem;
        switch (opType) {
          case 'Infix': {
            const tokenList = convertTokenList(rpnItem);
            token = pushToTokenList(tokenList, popListItems(argTokenStack, 2));
            break;
          }
          case 'Postfix': {
            const tokenList = convertTokenList(rpnItem);
            token = pushToTokenList(tokenList, popListItems(argTokenStack, 1));
            break;
          }
          case 'Prefix': {
            const tokenList = convertTokenList(rpnItem);
            token = pushToTokenList(tokenList, popListItems(argTokenStack, 1));
            break;
          }
          default:
            assertNever(opType as never, 'Unknown Operator');
        }
        break;
      }
      case 'P': {
        const tokenList = convertTokenList(rpnItem);
        token = pushToTokenList(tokenList, popListItems(argTokenStack, 1));
        break;
      }
      case 'X': {
        token = convertNameIndexToken(rpnItem);
        break;
      }
      case 'A': {
        token = convertLeafToken(rpnItem);
        break;
      }
      case 'N':
      case 'S':
      case 'B':
      case 'E':
      case 'M':
      case 'C': {
        token = convertLeafToken(rpnItem);
        break;
      }
      case 'D':
      case 'R':
      case 'T':
      case 'U':
      case 'V':
      case 'W':
      default:
        break;
        // assertNever(rpnItem, 'Unknown RPN expression token');
    }
    if (token) {
      argTokenStack.push(token);
    }
  }
  if (argTokenStack.length !== 1) {
    return null;
  }
  return argTokenStack[0];
}

export function convertAstToRpnData(node: TreeNode, resultRpn: RpnData.IndexData) {
  node.children?.forEach((c) => convertAstToRpnData(c, resultRpn));

  switch (node.node.type) {
    case 'F':
    case 'O':
    case 'P':
    case 'X':
    case 'N':
    case 'S':
    case 'B':
    case 'E':
    case 'M':
    case 'A':
    case 'C': {
      resultRpn.add({ ...node.node });
      break;
    }
    case 'D':
    case 'R':
    case 'T':
    case 'U':
    case 'V':
    case 'W':
    default:
      break;
      // assertNever(rpnItem, 'Unknown RPN expression token');
  }
}

class RpnDataImpl extends Array<AnyRpnToken<unknown, unknown>> implements RpnData.AnyData {
  readonly names: RpnNameData[];

  constructor(names: RpnNameData[] = []) {
    super();
    this.names = names;
  }

  add(item: AnyRpnToken<unknown, unknown>): this {
    super.push(item);
    return this;
  }
}

function convertAstToRpnFormula(node: TreeNode, names: RpnNameData[]) {
  const rpnData: RpnData.AnyData = new RpnDataImpl(names);
  convertAstToRpnData(node, rpnData);

  // 重新分配 names 数组
  const newNames: RpnNameData[] = [];
  const namesMap: Map<string, number> = new Map();
  rpnData.forEach((r) => {
    if (r.type === 'X') {
      const name = names[r.data];
      const key = `${name.name}_${name.sheetId}`;
      const existIndex = namesMap.get(key);
      if (existIndex === undefined) {
        const newIndex = namesMap.size;
        namesMap.set(key, newIndex);
        newNames.push(name);
        r.data = newIndex;
      } else {
        r.data = existIndex;
      }
    }
  });
  rpnData.names = newNames;
  return serializeRpn(rpnData);
}

type Context = {
  model: Model;
  rootCalcField: CalcField;
  names: RpnNameData[];
  getNameByIndex: (index: number) => RpnNameData;
  virtualCalcFields: CalcField[];
  rootCalcFieldReplaced?: boolean;
};

const AggMap: Record<string, LookupAggregator> = {
  SUM: 'SUM',
  COUNTA: 'COUNTA',
  COUNT: 'COUNT',
  AVERAGE: 'AVERAGE',
  MAX: 'MAX',
  MIN: 'MIN',
  UNIQUE: 'UNIQUE',
};

interface SplitResult {
  canSplit: boolean;
  // 如果有 filterUpData，则可以转换为查找引用并拆分；反之可以拆分成子 ast
  lookupConfig?: FilterUpFieldData;
  fieldRef?: RpnNameData;
}

function tryToSplitLookupFunc(node: TreeNode, ctx: Context): SplitResult | undefined {
  if (node.node.type !== 'F') return;
  const [lookupValue, lookupField, targetField, lookupMode] = node.children ?? [];
  // 查找值、查找列、结果列，任一是不是字段，直接拆分
  if (lookupValue.node.type !== 'X' || lookupField.node.type !== 'X' || targetField.node.type !== 'X') {
    return { canSplit: true };
  }
  const lookupFieldData = ctx.getNameByIndex(lookupField.node.data);
  // 不是跨列引用，不拆分
  if (!lookupFieldData.sheetId) return;
  if (targetField.node.type !== 'X') return { canSplit: true };

  const lookupFieldModel = ctx.model.getSheet(lookupFieldData.sheetId)?.getField(lookupFieldData.name);

  // 单元格是Object，同时第四个参数缺省或等于1，则使用ANY_OF来比较
  const isAnyOfMode = (!lookupMode || (lookupMode.node.type === 'N' && lookupMode.node.data === 1))
    && lookupFieldModel?.dataType === CellDataType.OBJECT;

  const targetFieldData = ctx.getNameByIndex(targetField.node.data);

  const lookupConfig: FilterUpFieldData = {
    valuesVersion: 1,
    targetSheet: lookupFieldData.sheetId,
    valuesField: targetFieldData.name,
    aggregator: 'VALUES',
    filters: [],
  };
  // lookup([field], [sheet.field], [field])
  const lookupValueData = ctx.getNameByIndex(lookupValue.node.data);
  lookupConfig.filters?.push({
    fieldId: lookupFieldData.name,
    symbol: isAnyOfMode ? 'ANY_OF' : 'EQ',
    value: { fieldId: lookupValueData.name },
    link: 'AND',
  });
  return {
    canSplit: true,
    lookupConfig,
  };
}

function tryToSplitFieldRef(node: TreeNode, ctx: Context): SplitResult | undefined {
  if (node.node.type === 'X') {
    const name = ctx.getNameByIndex(node.node.data);
    if (name.sheetId) {
      return { canSplit: true, fieldRef: name };
    }
  }
}

function replaceNodeWithFieldRef(node: TreeNode, replaceChildIndex: number, newCalcFeild: CalcField, ctx: Context) {
  const { children } = node;
  if (!children || children?.length <= replaceChildIndex) return;

  if (!node.parent) {
    // 特判，待替换的节点已经是根节点了，不需要再生成一个引用节点了
    newCalcFeild.addr.fieldId = ctx.rootCalcField.addr.fieldId;
    ctx.rootCalcFieldReplaced = true;
    return;
  }

  children[replaceChildIndex] = {
    node: { type: 'X', data: ctx.names.length },
    children: [],
  };
  ctx.names.push({ name: newCalcFeild.addr.fieldId });
}

function createLookupField(fieldId: string, lookupConfig: FilterUpFieldData, ctx: Context) {
  let resultFieldType: CalcFieldResultFieldType;
  if (lookupConfig.aggregator === 'VALUES' || lookupConfig.aggregator === 'UNIQUE') {
    // 优先从已存在的field里找
    const targetField = ctx.model.getSheet(lookupConfig.targetSheet)?.getField(lookupConfig.valuesField);
    if (targetField) {
      if (isSupportedResultFieldType(targetField.type)) {
        if (targetField.type === 'filter-up' || targetField.type === 'lookup') {
          resultFieldType = targetField.config?.renderFieldConfig?.props?.resultFieldType;
        } else {
          resultFieldType = targetField.type;
        }
      }
    }

    // 其次从当前上下文的 virtualCalcField 内找
    const targetCalcField = ctx.virtualCalcFields.find(
      (f) => f.addr.sheetId === lookupConfig.targetSheet && f.addr.fieldId === lookupConfig.valuesField,
    );
    if (targetCalcField) {
      if (targetCalcField.type === 'lookup') {
        resultFieldType = targetCalcField.resultFieldType;
      } else {
        // formula TODO
      }
    }
  }

  const filterUpCalcField = new FilterUpCalcField(
    { sheetId: ctx.rootCalcField.addr.sheetId, fieldId },
    { type: 'filter', filters: lookupConfig.filters },
    { sheetId: lookupConfig.targetSheet, fieldId: lookupConfig.valuesField },
    lookupConfig.aggregator,
    resultFieldType,
    undefined,
    1,
  );
  ctx.virtualCalcFields.push(filterUpCalcField);
  return filterUpCalcField;
}

function createFormulaField(fieldId: string, node: TreeNode, ctx: Context) {
  const rpn = convertAstToRpnFormula(node, ctx.names);
  const formulaCalcField = new FormulaCalcField(
    { sheetId: ctx.rootCalcField.addr.sheetId, fieldId },
    rpn,
  );
  ctx.virtualCalcFields.push(formulaCalcField);
  return formulaCalcField;
}

function getMergableAggregatorFromNode(node: TreeNode) {
  if (node.node.type === 'F') {
    const { funcID: funcName } = node.node.data;
    if (node.children?.length === 1) {
      return AggMap[funcName];
    }
  }
}

// 生成「稳定」的 FieldId
// 根据待拆分节点的整颗子树的 rpnString 做 hash
function generateVirtualFieldId(node: TreeNode, ctx: Context) {
  const rpn = convertAstToRpnFormula(node, ctx.names);
  const formulaStr = rpnToFormula({ formula: rpn }, (fId: string, sId?: string) => {
    return { fieldName: fId, sheetName: sId };
  });
  const fullId = `${ctx.rootCalcField.addr.sheetId}_${ctx.rootCalcField.addr.fieldId}_${formulaStr}`;
  return hash.sha256().update(fullId).digest('hex').slice(0, 16);
}

function dealWithNode(node: TreeNode, ctx: Context) {
  if (node.children?.length) {
    // case1: 有多个子节点，对每个子节点做拆分
    if (node.children.length > 1) {
      let hasSplited = false;
      for (let index = 0; index < node.children.length; ++index) {
        const child = node.children[index];
        if (!child) continue;
        const splitResult = dealWithNode(child, ctx);
        if (splitResult?.canSplit) {
          const { lookupConfig, fieldRef } = splitResult;
          const virtualFieldId = generateVirtualFieldId(child, ctx);
          if (lookupConfig) {
            // case1.1: 转成查找引用
            const lookupCalcField = createLookupField(virtualFieldId, lookupConfig, ctx);
            replaceNodeWithFieldRef(node, index, lookupCalcField, ctx);
            hasSplited = true;
          } else if (!fieldRef) {
            // case1.2: 转成普通公式
            const formulaCalcField = createFormulaField(virtualFieldId, child, ctx);
            replaceNodeWithFieldRef(node, index, formulaCalcField, ctx);
            hasSplited = true;
          }
          // case1.3: 仅仅只是一个引用，没必要拆了
        }
      }
      // 一旦有任何节点被拆分了，本层不需要再判断，直接返回
      if (hasSplited) {
        return;
      }
    } else {
      // case2: 只有一个子节点，可以尝试和本层合并
      const splitResult = dealWithNode(node.children[0], ctx);
      if (splitResult?.canSplit) {
        const { lookupConfig, fieldRef } = splitResult;
        const currentAgg = getMergableAggregatorFromNode(node);
        let backtracking = false;
        if (lookupConfig) {
          // case2.1: 下层的 lookup 可以和本层的 aggregate 合并
          if (currentAgg) {
            if (lookupConfig.aggregator === 'VALUES') {
              lookupConfig.aggregator = currentAgg;
              // 这种情况，agg有可能还能继续和上层合并，将结果抛到上层处理
              backtracking = true;
            }
            if (lookupConfig.aggregator === 'UNIQUE' && currentAgg === 'COUNTA') {
              lookupConfig.aggregator = 'COUNTA.UNIQUE';
              backtracking = true;
            }
            if (lookupConfig.aggregator === 'COUNTA' && currentAgg === 'UNIQUE') {
              lookupConfig.aggregator = 'COUNTA.UNIQUE';
              backtracking = true;
            }
          }
          if (backtracking) {
            return splitResult;
          }
          const virtualFieldId = generateVirtualFieldId(node, ctx);
          const lookupCalcField = createLookupField(virtualFieldId, lookupConfig, ctx);
          replaceNodeWithFieldRef(node, 0, lookupCalcField, ctx);
        } else if (currentAgg && fieldRef && fieldRef.sheetId) {
          // case2.2: 下层的引用可以和本层的 aggregate 合并
          const thisLookupConfig: FilterUpFieldData = {
            valuesVersion: 1,
            targetSheet: fieldRef.sheetId,
            valuesField: fieldRef.name,
            aggregator: currentAgg,
            filters: [{
              fieldId: fieldRef.name,
              symbol: 'EXIST',
              value: null,
              link: 'AND',
            }],
          };
          switch (currentAgg) {
            case 'SUM':
            case 'MAX':
            case 'MIN':
            case 'AVERAGE':
            case 'UNIQUE':
            case 'COUNT':
            case 'COUNTA':
              return { canSplit: true, lookupConfig: thisLookupConfig };
            default:
              return { canSplit: true };
          }
        } else {
          // case2.3: 转成ast子树
          return { canSplit: true };
        }
        return;
      }
    }
  }

  // case3: 没有命中上面的拆分逻辑，尝试处理本层
  switch (node.node.type) {
    case 'F': {
      const { funcID: funcName } = node.node.data;
      if (funcName === 'LOOKUP') {
        return tryToSplitLookupFunc(node, ctx);
      } else if (funcName === 'COUNTIF' || funcName === 'SUMIF' || funcName === 'AVERAGEIF' || funcName === 'FILTER') {
        // TODO: 需要一个条件解析逻辑
      }
      break;
    }
    case 'X': {
      return tryToSplitFieldRef(node, ctx);
    }
    default:
      break;
  }
}

/**
 * 遍历整颗ast，拆分成多个ast和多个查找引用
 */
export function splitFormulaCalcField(node: FormulaCalcField, model: Model): CalcField[] {
  // console.log(node);
  const astRoot = convertRpnToAst(node.rpn);
  if (!astRoot) return [node];
  const names = node.rpnRefs.map(([fieldId, sheetId]) => ({ name: fieldId, sheetId }));

  // 加一个guard节点，处理root本身就被替换的case
  const guardRoot: TreeNode = { node: { type: 'N', data: 0 }, children: [astRoot] };
  astRoot.parent = guardRoot;
  const ctx: Context = {
    model,
    rootCalcField: node,
    names,
    getNameByIndex: (index: number) => names[index],
    virtualCalcFields: [],
  };
  dealWithNode(guardRoot, ctx);

  let allCalcFields: CalcField[] = [];

  if (!ctx.rootCalcFieldReplaced) {
    const rpn = convertAstToRpnFormula(guardRoot.children![0], ctx.names);
    const formulaCalcField = new FormulaCalcField(
      node.addr,
      rpn,
    );
    allCalcFields = [formulaCalcField, ...ctx.virtualCalcFields];
  } else {
    allCalcFields = ctx.virtualCalcFields;
  }

  allCalcFields.forEach((f) => {
    f.isVirtual = true;
    if (node.dirtyRows) {
      f.overwriteDirtyRows(node.dirtyRows);
    }
  });

  return allCalcFields;
}
