/* eslint-disable @typescript-eslint/no-invalid-void-type */
import debug from 'debug';
import { get, partition } from 'lodash-es';
import { logger, NestedMap2, WidgetTypeEnums, isValidFormulaConfig, isValidLookupConfig, isValidFilterUpConfig } from '@ali/notable-common';
import type { RpnExpr } from '@ali/zongheng-formula';
import type { SheetId } from '../../../sheet/types';
import type { Model } from '../../../model';
import type { SheetModel } from '../../../sheet';
import type { CalcFieldResultType, FieldAddr } from '../../types';
import {
  FieldId, FieldDTO,
  isValidAssociationProps,
} from '../../../field';
import { findAssoSheets, getLookupValuesField, isEqualFieldAddr, mergeCalcFieldProps } from '../../utils';
import { detectAssociateFieldResultType, detectFormulaFieldResultType, detectLookupFieldResultType } from '../../formula-parser';
import {
  type LookupCalcField,
  AssoCalcField, CalcField, FormulaCalcField,
  LookupAssoCalcField, FilterUpCalcField,
} from '../calc-field';
import { fromQOCalcChain, type QOCalcChain } from '../rows-calculator/utils';

const EMPTY_RPN: RpnExpr<number> = { expr: '', refs: [] };
const FX_PATH_IN_CONFIG = 'renderFieldConfig.props';

type CalcChain = [
  CalcChain: CalcField[],
  Circular: CalcField[],
  RefError: CalcField[],
];

/** 基础的 CalcField 增删、查询逻辑 */
export class CalcFieldManagerBase {
  private refErrorIfMissing = false;
  protected readonly calcFieldStore = new NestedMap2<SheetId, FieldId, CalcField>();
  private cachedCalcChain: undefined | CalcChain;
  protected logger = debug('calc:fields');

  constructor(
    readonly model: Model,
  ) {
    this.calcFieldStore.onItemsChange(() => {
      this.cachedCalcChain = undefined;
    });
  }

  setRefErrorIfMissingFlag(v: boolean) {
    this.refErrorIfMissing = v;
    this.cachedCalcChain = undefined; // 清除 cache 的计算链
  }

  hasCalcFieldInSheet(sheetId: SheetId) {
    return !this.calcFieldStore.isEmpty(sheetId);
  }

  hasLookupFieldInSheet(sheetId: SheetId) {
    const calcFieldsInSheet = this.calcFieldStore.get(sheetId);
    if (calcFieldsInSheet?.size) {
      for (const calcField of calcFieldsInSheet.values()) {
        if (calcField.type === 'lookup') return true;
      }
    }
    return false;
  }

  getCalcField = (addr: FieldAddr) => this.calcFieldStore.getItem(addr.sheetId, addr.fieldId);

  getCalcFieldsInSheet(sheet: SheetId) {
    return this.calcFieldStore.get(sheet);
  }

  getAllCalcFieldsAndRefered() {
    const result = new Map<SheetId, Set<FieldId>>();
    const getListOrCreate = (sheetId: SheetId) => {
      let list = result.get(sheetId);
      if (!list) {
        list = new Set();
        result.set(sheetId, list);
      }
      return list;
    };

    const isRefSheetExists = (sheetId: SheetId) => {
      return !!this.model.getSheet(sheetId);
    };

    this.iterCalcFieldsBySheet((calcFields, sheet) => {
      const list = getListOrCreate(sheet.id);
      for (const { addr } of calcFields) {
        list.add(addr.fieldId);
      }
      for (const calcField of calcFields) {
        for (const ref of calcField.getAllRefs()) {
          if (isRefSheetExists(ref.sheetId)) {
            const listInRefSheet = getListOrCreate(ref.sheetId);
            listInRefSheet.add(ref.fieldId);
          }
        }
      }
    });
    return result;
  }

  iterAllCalcField(
    handler: (calcField: CalcField, sheet: SheetModel) => void | 'break',
  ) {
    for (const [sheetId, calcFields] of this.calcFieldStore) {
      const sheet = this.model.getSheet(sheetId);
      if (sheet) {
        for (const calcField of calcFields.values()) {
          const res = handler(calcField, sheet);
          if (res === 'break') return;
        }
      }
    }
  }

  iterCalcFieldsBySheet(
    handler: (calcFields: Set<CalcField>, sheet: SheetModel) => void | 'break',
  ) {
    for (const [sheetId, calcFields] of this.calcFieldStore) {
      const sheet = this.model.getSheet(sheetId);
      if (sheet) {
        const res = handler(new Set(calcFields.values()), sheet);
        if (res === 'break') return;
      }
    }
  }

  iterCalcFieldsByRefField(
    changedField: FieldAddr,
    handler: (sheetId: SheetId, calcFields: Set<CalcField>) => void,
  ) {
    for (const [sheetId, calcFields] of this.calcFieldStore) {
      const sheet = this.model.getSheet(sheetId);
      if (sheet) {
        const impacted = new Set<CalcField>();
        for (const calcField of calcFields.values()) {
          if (calcField.hasRefField(changedField)) impacted.add(calcField);
        }
        if (impacted.size) {
          handler(sheetId, impacted);
        }
      }
    }
  }

  /**
   * 取得指定 sheet 中的、没有引用到 lookup/filter-up/association 的 formula 字段，按引用关系排序
   */
  getCalcChainInSheet_FormulaOnly(sheetId: SheetId): [
    circularFxFields: FieldId[],
    basicFxFields: FieldId[],
    fxFieldsRefering: FieldId[],
  ] {
    const calcFieldsMap = this.calcFieldStore.get(sheetId);
    if (!calcFieldsMap) return [[], [], []];

    // 最终所有的 formula field，不在 result 就在 fxFieldsReferingLookup
    const result = new Set<FieldId>();
    const fxFieldsReferingLookup = new Set<FormulaCalcField>();

    // 解析 formula field 的引用情况，如果其引用了其它尚未解析的 formula field，就返回 true、表示要在下一轮再解析
    const shouldCheckAgain = (item: CalcField): boolean => {
      if (item.type !== 'formula') return false;

      const calcFieldRefs = item.rpnRefs.map(([ref]) => calcFieldsMap.get(ref)).filter((ref): ref is CalcField => !!ref);
      if (!calcFieldRefs.length) {
        result.add(item.addr.fieldId);
        return false;
      }

      const foundLookup = calcFieldRefs.some(({ addr }) => {
        const calcField = calcFieldsMap.get(addr.fieldId);
        return calcField && (calcField.type !== 'formula' || fxFieldsReferingLookup.has(calcField));
      });
      if (foundLookup) {
        fxFieldsReferingLookup.add(item);
        return false;
      }

      const allRefsDealt = calcFieldRefs.every(({ addr }) => result.has(addr.fieldId));
      if (allRefsDealt) {
        result.add(item.addr.fieldId);
        return false;
      }
      // 存在某个引用还未判断
      return true;
    };

    let calcFields = calcFieldsMap;
    while (calcFields.size) {
      const nextCalcFields = new Map<FieldId, CalcField>();
      for (const item of calcFields.values()) {
        if (shouldCheckAgain(item)) nextCalcFields.set(item.addr.fieldId, item);
      }
      if (nextCalcFields.size === calcFields.size) {
        break;
      }
      calcFields = nextCalcFields;
    }

    return [
      Array.from(calcFields.values()).map((i) => i.addr.fieldId), // 剩余的是循环引用
      Array.from(result),
      Array.from(fxFieldsReferingLookup).map((i) => i.addr.fieldId),
    ];
  }

  private validateCalcFieldRef(calcChain: CalcField[]): [normalCalcChain: CalcField[], refErrorChain: CalcField[]] {
    const refErrorSet = new Set<CalcField>();

    const findRefErrorField = (fields: CalcField[]) => {
      let hasMore = false;
      fields.forEach((c) => {
        if (!refErrorSet.has(c)) {
          refErrorSet.add(c);
          hasMore = true;
        }
      });

      if (!hasMore) {
        return;
      }

      const next = fields.reduce((all, f) => {
        for (const n of f.next.values()) {
          all.add(n);
        }
        return all;
      }, new Set<CalcField>());

      findRefErrorField(Array.from(next.values()));
    };

    const firstLevel = calcChain.filter((calcField) => {
      return calcField.getAllRefs().some((ref) => {
        // 线上有存量的 filter-up 条件中存在 fieldId 为空字符串
        // 实际上没有用到这个字段，忽略即可
        if (!ref.sheetId || !ref.fieldId) {
          return false;
        }
        const sheet = this.model.getSheet(ref.sheetId);
        if (!sheet) {
          return true;
        }
        const field = sheet.getField(ref.fieldId);
        if (!field) {
          return true;
        }
        return this.refErrorIfMissing ? !!field.deleted : false;
      });
    });

    if (firstLevel.length) {
      findRefErrorField(firstLevel);
    }

    return [
      calcChain.filter((c) => !refErrorSet.has(c)),
      Array.from(refErrorSet.values()),
    ];
  }

  fromQOCalcChain(qoCalcChain: QOCalcChain): CalcChain {
    const [normal, circular, refError] = fromQOCalcChain(qoCalcChain);

    return [
      normal.map((node) => this.addCalcField(node, node.sheetId, node.isVirtual)).filter((c): c is CalcField => !!c),
      circular.map((node) => this.addCalcField(node, node.sheetId, node.isVirtual)).filter((c): c is CalcField => !!c),
      refError.map((node) => this.addCalcField(node, node.sheetId, node.isVirtual)).filter((c): c is CalcField => !!c),
    ];
  }

  getAllCalcChain(): CalcChain {
    if (!this.cachedCalcChain) {
      const fieldsToSolve: CalcField[] = [];
      for (const calcFields of this.calcFieldStore.values()) {
        fieldsToSolve.push(
          // 先 sheet 范围内排序一次，使尽量先算完 sheet 范围内的公式
          ...sortCalcFieldsInSheet(this.calcFieldStore, calcFields),
        );
      }

      // 整体排序
      for (const calcField of fieldsToSolve) {
        calcField.buildRelation(this.calcFieldStore);
      }
      const [calcChain, circularChain] = topoSortCalcChain(fieldsToSolve);
      const [normalChain, refErrorChain] = this.validateCalcFieldRef(calcChain);

      for (let idx = 0; idx < normalChain.length; idx++) {
        normalChain[idx].sortOrder = idx;
      }
      for (let idx = 0; idx < circularChain.length; idx++) {
        circularChain[idx].sortOrder = Infinity;
      }

      this.cachedCalcChain = [
        normalChain,
        circularChain,
        refErrorChain,
      ];

      // 再构建一遍关系，为之后查询引用关系
      this.buildAllRelations();
    }

    return [
      this.cachedCalcChain[0].slice(0),
      this.cachedCalcChain[1].slice(0),
      this.cachedCalcChain[2].slice(0),
    ];
  }

  protected addCalcField(
    field: FieldDTO,
    sheetId: SheetId,
    isVirtual?: boolean,
  ): undefined | CalcField {
    if (field.deleted) return;

    const fieldId = field.id;
    const calcField = this.createCalcField(field.type, { sheetId, fieldId }, field.config);
    if (calcField) {
      if (isVirtual) {
        calcField.isVirtual = true;
      }
      // if field just inserted is Formula/Lookup, update its resultType to field right now
      this.updateCalcFieldResultTypeToModel(calcField);

      const statistics = this.buildCalcFieldLogStatistics(sheetId, calcField);
      statistics && logger.info('calc_field_stat', 'log', { data: statistics });
      this.logger('[new_calc_field] sheetId: %s, name: %s, calcField: %O', sheetId, field.name, calcField);
      this.calcFieldStore.addItem(sheetId, fieldId, calcField);
      return calcField;
    }
  }

  protected updateCalcFieldResultTypeToModel(
    calcField: CalcField,
  ) {
    const { addr } = calcField;
    const sheet = this.model.getSheet(addr.sheetId);
    if (sheet) {
      let resultType: CalcFieldResultType;
      switch (calcField.type) {
        case 'lookup':
          resultType = detectLookupFieldResultType(this.model, calcField);
          break;
        case 'formula':
          resultType = detectFormulaFieldResultType(this.model, addr.sheetId, calcField.getProps());
          break;
        case 'associate':
          resultType = detectAssociateFieldResultType(this.model, calcField);
          break;
        default:
          break;
      }

      mergeCalcFieldProps(sheet, addr.fieldId, { resultType });
    }
  }

  protected mergeCalcField(
    existed: CalcField,
    newCalcFieldData: CalcField,
  ) {
    // 保持引用、覆写属性
    existed.merge(newCalcFieldData);

    const { sheetId, fieldId } = existed.addr;
    // 重新 add 一次 => 触发 ItemChange 事件
    this.calcFieldStore.addItem(sheetId, fieldId, existed);
  }

  protected deleteCalcField(calcField: CalcField) {
    const { sheetId, fieldId } = calcField.addr;
    this.calcFieldStore.deleteItem(sheetId, fieldId);
    calcField.remove(false);
  }

  protected createCalcField(
    type: string,
    addr: FieldAddr,
    fieldConfig: FieldDTO['config'],
  ): undefined | CalcField {
    if (type === WidgetTypeEnums.formula) {
      const calcData = get(fieldConfig, FX_PATH_IN_CONFIG) as unknown;
      if (isValidFormulaConfig(calcData)) {
        const rpn = typeof calcData.formula === 'object' ? calcData.formula : EMPTY_RPN;
        return new FormulaCalcField(addr, rpn, calcData.resultFieldType);
      }
      return;
    }
    if (type === WidgetTypeEnums.lookup) {
      const calcData = get(fieldConfig, FX_PATH_IN_CONFIG);
      if (isValidLookupConfig(calcData)) {
        const { aggregator, associateField, resultFieldType, formatter, valuesVersion } = calcData;
        return new LookupAssoCalcField(
          addr,
          { type: 'associate', associateField },
          getLookupValuesField(this.model, addr.sheetId, calcData),
          aggregator,
          resultFieldType,
          formatter,
          valuesVersion,
        );
      }
      return;
    }
    if (type === WidgetTypeEnums.filterUp) {
      const calcData = get(fieldConfig, FX_PATH_IN_CONFIG);
      if (isValidFilterUpConfig(calcData)) {
        const { targetSheet, filters, aggregator, valuesField, resultFieldType, formatter, valuesVersion } = calcData;
        return new FilterUpCalcField(
          addr,
          { type: 'filter', filters },
          { sheetId: targetSheet, fieldId: valuesField },
          aggregator,
          resultFieldType,
          formatter,
          valuesVersion,
        );
      }
      return;
    }
    if (type === WidgetTypeEnums.association) {
      const assoProps = get(fieldConfig, FX_PATH_IN_CONFIG);
      if (isValidAssociationProps(assoProps)) {
        const targetSheetId = assoProps.sheetId;
        const fieldsInTargetSheet = this.model.getSheet(targetSheetId)?.fieldMap ?? {};
        const titleField = Object.values(fieldsInTargetSheet).find((f) => (f.isPrimary && !f.deleted));
        if (titleField) {
          return new AssoCalcField(addr, assoProps.sheetId, titleField.id);
        }
      }
    }
  }

  /**
   * 当指定 field 内容变化时，所有受影响的公式字段。
   * - NOTE: 这里只关心 formula refs 的引用关系，不关心 lookup 的引用
   */
  getCalcFieldsInSheetByRpnRef(
    refSheetId: SheetId,
    refFieldId: FieldId,
  ): undefined | Set<CalcField> {
    const impacted = new Set<CalcField>();

    this.calcFieldStore.get(refSheetId)?.forEach((field) => {
      if (field.getAllRefs().some(({ fieldId, sheetId }) => sheetId === refSheetId && fieldId === refFieldId)) impacted.add(field);
    });

    if (impacted.size) return impacted;
  }

  protected getLookupFieldsByValuesAndGroupBySheet(
    valuesSheet: SheetId,
    valuesField: FieldId,
  ) {
    const result = new Map<SheetId, Set<LookupCalcField>>();

    findAssoSheets(
      this.model.getSheet(valuesSheet),
    ).forEach((assoSheet) => {
      const lookups = this.getLookupFieldsInSheetByValues(assoSheet, valuesSheet, valuesField);
      if (lookups.size) {
        result.set(assoSheet, lookups);
      }
    });

    return result;
  }

  getLookupFieldsInSheetByValues(
    sheetId: SheetId,
    valuesSheet: SheetId,
    valuesField: FieldId,
  ) {
    const lookups = new Set<LookupCalcField>();

    this.calcFieldStore.get(sheetId)?.forEach((calcField) => {
      if (
        calcField.type === 'lookup'
        && isEqualFieldAddr(calcField.valuesField, valuesSheet, valuesField)
      ) {
        lookups.add(calcField);
      }
    });

    return lookups;
  }

  buildAllRelations() {
    for (const calcFields of this.calcFieldStore.values()) {
      for (const calcField of calcFields.values()) {
        calcField.buildRelation(this.calcFieldStore);
      }
    }
  }

  private buildCalcFieldLogStatistics(thisSheetId: string, calcField: CalcField) {
    try {
      // 目前只记录关联引用
      if (calcField.type !== 'lookup' || calcField.valuesSource.type !== 'filter') {
        return;
      }

      const { valuesSource, aggregator, valuesField: { fieldId, sheetId } } = calcField;

      const statistics = {
        vft: '', // ValueFieldType
        vsrc: 0, // ValueSheetRecordCount
        fc: valuesSource?.filters?.length, // FilterCount
        fs: '', // JSON.stringify([{ lt: '', rt: '', op: '', lk: '' }]), Filters{ LeftType, RightType, Operator, Link }
        tsrc: 0, // ThisSheetRecordCount
        agt: aggregator, // aggregate count
      };

      const sheet = this.model.getSheet(sheetId);
      if (!sheet) {
        return;
      }
      statistics.vsrc = sheet.getRowCount();

      const thisSheet = this.model.getSheet(thisSheetId);
      if (!thisSheet) {
        return;
      }
      statistics.tsrc = thisSheet.getRowCount();

      const valueField = sheet.getField(fieldId);
      if (!valueField) {
        return statistics;
      }
      statistics.vft = valueField.type;

      const fs = valuesSource.filters.map((f) => {
        const rt = (function getRightOperand() {
          const rightValue = f.value;
          if (rightValue && typeof rightValue === 'object' && 'fieldId' in rightValue) {
            return thisSheet.getField(rightValue.fieldId)?.type;
          }
          return '';
        })();

        return {
          lt: sheet.getField(f.fieldId)?.type as string,
          rt: rt as string,
          op: '' as string,
          lk: f.link as string,
        };
      });

      statistics.fs = JSON.stringify(fs);

      return statistics;
    } catch (e) {
      return null;
    }
  }
}

const sortCalcFieldsInSheet = (
  calcFieldStore: CalcFieldManagerBase['calcFieldStore'],
  calcFieldsMapInSheet: Map<FieldId, CalcField>,
) => {
  const calcFields = Array.from(calcFieldsMapInSheet.values());
  for (const item of calcFields) item.buildRelation(calcFieldStore);

  // 去掉对其他 sheet 的引用，纯 sheet 范围内排序
  for (const item of calcFields) {
    item.prev = new Set(
      Array.from(item.prev).filter((prevItem) => prevItem.addr.sheetId === item.addr.sheetId),
    );
  }
  const [normal, circular] = topoSortCalcChain(calcFields);
  return [...normal, ...circular];
};

/** CalcField 以 prev, next 构成一个图，通过 topo 排序获得计算顺序 */
export const topoSortCalcChain = (
  calcFields: CalcField[],
): [normalChain: CalcField[], circularChain: CalcField[]] => {
  const result: CalcField[] = [];

    // 拷贝 prev 副本，保证 topoSortCalcChain 不修改入参
  const prevMap = new Map<CalcField, Set<CalcField>>();
  calcFields.forEach((f) => prevMap.set(f, new Set(f.prev)));

  let fieldsToSolve = calcFields.slice(0);
  let refLevel = 0;
  while (fieldsToSolve.length) {
    const nextFields: CalcField[] = [];
    let solvedOne = false;
    for (let i = 0; i < fieldsToSolve.length; i++) {
      const field = fieldsToSolve[i];
      const fieldPrev = prevMap.get(field);
      if (fieldPrev?.size) {
        nextFields.push(field);
      } else {
        field.next.forEach((n) => prevMap.get(n)?.delete(field));
        result.push(field);
        field.refLevel = refLevel;
        solvedOne = true;
      }
    }
    fieldsToSolve = nextFields;
    refLevel += 1;

    if (!solvedOne && fieldsToSolve.length) break;
  }

  // 循环引用
  for (const f of fieldsToSolve) {
    f.refLevel = Infinity;
  }

  return [result, fieldsToSolve];
};
