import { FormulaErrorType, assertNever } from '@ali/zongheng-common';
import { type ConstArray, type FormulaValue, type FormulaNullValue } from '@ali/zongheng-formula';
import {
  limitations, CellDataType,
  isObjectCellValue,
  FxObjectValue, ArrayUtils,
  isFxOriginObjectValue,
  SheetId,
  FilterUpCondition,
  isObjHasFieldId,
  CalcFieldResultFieldType,
  NumberFormatter,
  isValidNumber,
  getFormulaLookupResultFieldType,
  FieldTypes,
  getFxResultFormat,
  LookupAggregator,
  FormulaRpn,
  isNotableFormulaErrorValue,
  createFxObjValByFxValue,
} from '@ali/notable-common';
import type { Model } from '../../../model';
import type { ObjectCellValue, RowCell, RecordId } from '../../../row/types';
import { rpnToFormula } from '../../formula-parser/formula-rpn';
import type { FieldDTO, FieldId } from '../../../field';
import type { CalcField, FilterUpCalcField, LookupCalcField } from '../calc-field';
import * as FxValue from '../../utils/FxValueUtils';
import { GetFieldFunc } from '../../../formula-engine/utils/common';
import type { FxCalcContext } from './fx-task-scheduler';

export interface QOCalcChainContext {
  model: Model;
}

interface QOLookupCalcChainContext extends QOCalcChainContext {
  thisField: FilterUpCalcField | LookupCalcField;
  criteriaType?: QOFilterCriteria['type'];
}

interface TooLargeError {
  type: 'ERROR';
  value: {
    type: '#TOO_LARGE';
  };
}

interface MismatchError {
  type: 'ERROR';
  value: {
    type: '#MISMATCH_TYPE';
  };
}

type NotableFormulaError =
  FormulaValue<'ERROR'>
  | TooLargeError
  | MismatchError;
type NotableValueItem = string | number | boolean | FormulaNullValue | NotableFormulaError;
export type NotableConstArray = NotableValueItem[][];

export const createCellValueOfFxError = (fxError: FormulaErrorType): RowCell => {
  const data: FxObjectValue = [{ fxError }];
  const filterFactor: string[] = [fxError];
  const sequence = filterFactor.join(',');
  return {
    dataType: CellDataType.OBJECT,
    value: {
      identifier: sequence,
      sequence,
      filterFactor,
      data: JSON.stringify(data),
    },
  };
};
export const cellValueOfFxError_ERROR = createCellValueOfFxError('#ERROR!');
export const cellValueOfFxError_CIRCULAR = createCellValueOfFxError('#CIRCULAR!');
export const cellValueOfFxError_REF = createCellValueOfFxError('#REF!');

const toCellValueByExtraInfo = (extraInfo: NonNullable<ObjectCellValue['refData']>, data: FxObjectValue) => {
  const identifier: string[] = [];
  const filterFactor: string[] = [];
  const sequence: string[] = [];
  const newValue: ObjectCellValue['refData'] = {
    dataType: extraInfo.dataType,
    value: [],
  };
  const parseExtraInfo = (info: NonNullable<ObjectCellValue['refData']>) => {
    info.value.forEach((value) => {
      if (isObjectCellValue(value)) {
        let objCell = value;
        try {
          if (isFxOriginObjectValue(value)) {
            parseExtraInfo(value.refData);
            return;
          }
          // 要原值引用忽略 FxError
          // 这里就不知道 ObjectCellValue 是什么字段的了……
          const dataObj = JSON.parse(value.data);
          if (Array.isArray(dataObj) && dataObj.some(isNotableFormulaErrorValue)) {
            // 这就肯定是计算字段结果了
            const newDataArr = dataObj.filter((item) => !isNotableFormulaErrorValue(item));
            if (!newDataArr.length) {
              return;
            }
            objCell = createFxObjValByFxValue(newDataArr);
          }
        } catch {
          // do nothing
        }

        if (Array.isArray(objCell.filterFactor)) {
          ArrayUtils.safePush(filterFactor, objCell.filterFactor);
        } else {
          filterFactor.push(objCell.filterFactor);
        }
        identifier.push(objCell.identifier);
        sequence.push(objCell.sequence);
        newValue.value.push(objCell);
      } else {
        const valueStr = `${value}`;
        identifier.push(valueStr);
        filterFactor.push(valueStr);
        sequence.push(valueStr);
        newValue.value.push(value);
      }
    });
  };
  parseExtraInfo(extraInfo);
  const value: ObjectCellValue = {
    identifier: identifier.join(','),
    sequence: sequence.join(','),
    filterFactor: filterFactor.slice().sort(),
    data: JSON.stringify(data),
    refData: newValue,
  };
  return value;
};

export const toCellValue = (
  calcValue: FxValue.AnyInternalValue,
  getField: GetFieldFunc,
  extraInfo?: ObjectCellValue['refData'],
  skipLimitationCheck?: boolean,
): null | RowCell => {
  switch (typeof calcValue) {
    case 'object': {
      if (calcValue) {
        if (FxValue.isReference(calcValue) || FxValue.isReferenceArray(calcValue)) {
          // should never here
          // todo: report error
          return null;
        }
        if (FxValue.isNull(calcValue)) {
          return null;
        }
        if (FxValue.isNotableArray(calcValue)) {
          const constArr: NotableConstArray = [];
          let refData: ObjectCellValue['refData'];
          const { sId, fId } = calcValue.value;
          if (sId && fId) {
            const originalRefField = getField(sId, fId);
            if (originalRefField) {
              refData = {
                dataType: originalRefField.dataType,
                value: [],
              };
            }
          }
          constArr[0] = [];
          FxValue.iterateNotableArray(calcValue.value, (item) => {
            const constValue = item.getConstValue();
            constArr[0].push(constValue);
            if (refData) {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              const originData = (item.value as any).data as RowCell;
              if (originData) {
                if (originData.dataType === CellDataType.OBJECT && originData.value?.refData) {
                  refData.value.push(...originData.value.refData.value);
                } else {
                  refData.value.push(originData.value);
                }
              }
            }
          });

          let data: FxObjectValue = [];
          FxValue.iterateConstArray(constArr as ConstArray, (item) => {
            if (FxValue.isError(item)) {
              data.push({ fxError: item.value.type });
            } else if (!FxValue.isNull(item)) {
              data.push(item);
            }
          }, true);
          // 聚合的原值引用 忽略 FxError，但若仅一个 FxError、则暂时保留
          if (data.some(isNotableFormulaErrorValue)) {
            if (data.length > 1) {
              data = data.filter((item) => !isNotableFormulaErrorValue(item));
            }
          }

          const filterFactor = data.map(
            (item) => (isNotableFormulaErrorValue(item) ? item.fxError : `${item}`),
          );
          const sequence = filterFactor.join(',');
          const value: ObjectCellValue = {
            identifier: sequence,
            sequence,
            filterFactor: filterFactor.slice().sort(),
            data: JSON.stringify(data),
            refData,
          };
          return { dataType: CellDataType.OBJECT, value };
        }

        let constArr: undefined | NotableConstArray;
        let hasError = false;
        if (FxValue.isError(calcValue)) {
          constArr = [[calcValue]];
          hasError = true;
        } else if (FxValue.isConstArray(calcValue)) {
          if (!skipLimitationCheck && calcValue.value.length > limitations.cellFormulaValueMaxCount) {
            constArr = [[{ type: 'ERROR', value: { type: '#TOO_LARGE' } }]];
            hasError = true;
          } else {
            constArr = calcValue.value;
          }
        }

        if (constArr) {
          const data: FxObjectValue = [];
          const filterFactor: string[] = [];
          FxValue.iterateConstArray(constArr as ConstArray, (item) => {
            if (FxValue.isError(item)) {
              data.push({ fxError: item.value.type });
              filterFactor.push(item.value.type);
            } else if (!FxValue.isNull(item)) {
              data.push(item);
              filterFactor.push(`${item}`);
            }
          }, true);
          if (!hasError && extraInfo) {
            const value = toCellValueByExtraInfo(extraInfo, data);
            return { dataType: CellDataType.OBJECT, value };
          }
          const sequence = filterFactor.join(',');
          const value: ObjectCellValue = {
            identifier: sequence,
            sequence,
            filterFactor: filterFactor.slice().sort(),
            data: JSON.stringify(data),
          };
          return { dataType: CellDataType.OBJECT, value };
        }
      }
      break;
    }
    case 'boolean': return { dataType: CellDataType.BOOLEAN, value: Boolean(calcValue) };
    case 'number': {
      const strVal = String(calcValue);
      return { dataType: CellDataType.NUMBER, value: isValidNumber(strVal) ? strVal : null };
    }
    case 'string': return { dataType: CellDataType.STRING, value: String(calcValue) };
    default: break;
  }
  return null;
};

export function getFxCellValueConvertor(ctx: FxCalcContext, field: CalcField) {
  if (field.type !== 'lookup') return;
  const { valuesField: { fieldId, sheetId } } = field;
  const convertor = ctx.fxCellValueConvertorManager.getConvertor(sheetId, fieldId);
  if (convertor) {
    return convertor;
  }
}

export interface QOCalcDirtyInfo {
  allRecords: boolean;
  recordIds: RecordId[];
}

export interface QOCalcNodeExtra {
  startDirtyInfo: QOCalcDirtyInfo;
}

interface QOCalcNodeBase {
  circularError?: boolean;
  refError?: boolean;
  virtualNode?: boolean;
  addr: {
    sheetId: SheetId;
    fieldId: FieldId;
  };
  dirtyInfo: {
    allRecords: boolean;
    recordIds: RecordId[];
  };
  extra: string; // QO/QC 不感知，会透传
}

interface QOFormulaCalcNode extends QOCalcNodeBase {
  type: 'formula';
  rpn: string;
  rpnRefs: string[];
  dbFormula: string;
  formula: string;
  formatter?: NumberFormatter;
}

export enum CompositeReasonCode {
  COMPOSITE_BY_SAME_FILTER = 1,
}

export interface QOCompositeCalcNode extends QOCalcNodeBase {
  type: 'composite';
  children: QOChildCalcNode[];
  code: CompositeReasonCode;
}

export interface QOLookupByFilterCalcNode extends QOCalcNodeBase {
  type: 'lookup-by-filter';
  select: FieldId;
  selectPropName: string;
  agg: LookupAggregator;
  fromSheetId: SheetId;
  on: QOFilterCompositeCriteria;
  resultFieldType?: CalcFieldResultFieldType;
  formatter?: NumberFormatter;
  valuesVersion?: number;
}

export interface QOLookupByAssociationCalcNode extends QOCalcNodeBase {
  type: 'lookup-by-association';
  associationFieldId: FieldId;
  associationFieldIdPropName: string;
  select: FieldId;
  selectPropName: string;
  agg: LookupAggregator;
  fromSheetId: SheetId;
  resultFieldType?: CalcFieldResultFieldType;
  formatter?: NumberFormatter;
  valuesVersion?: number;
}

export interface QOAssociationCalcNode extends QOCalcNodeBase {
  type: 'association';
  fromSheetId: SheetId;
  titleFieldId: FieldId;
  titleFieldIdPropName: string;
}

export interface QOFilterByFieldCriteria {
  type: 'field';
  criteria: {
    leftField: FieldId;
    leftFieldPropName: string;
    rightField: FieldId;
    rightFieldPropName: string;
    symbol: FilterUpCondition['symbol'];
    symbolV2: FilterCriteriaSymbols;
  };
}

export interface QOFilterByValueCriteria {
  type: 'rawValue';
  criteria: {
    leftField: FieldId;
    leftFieldPropName: string;
    rightValue: FilterUpCondition['value'];
    rightValueVersion?: number;
    symbol: FilterUpCondition['symbol'];
    symbolV2: FilterCriteriaSymbols;
  };
}

export interface QOFilterCompositeCriteria {
  type: 'composite';
  conjunction: 'AND' | 'OR';
  criteria: QOFilterCriteria[];
}

type FilterCriteriaSymbols =
  'EQ' | 'NE' |
  'DATE_EQ' | 'BEFORE' | 'AFTER' |
  'EXIST' | 'UN_EXIST' |
  'GT' | 'GTE' | 'LT' | 'LTE' |
  'ANY_OF' | 'NOT_ANY_OF' |
  'TEXTCONTAIN' | 'NOT_TEXTCONTAIN' |
  'ALL_OF' | 'NOT_ALL_OF' |
  'EQ_STRICT' | 'NOT_EQ_STRICT' |
  'EQ_JOINTEXT' | 'NOT_EQ_JOINTEXT' | 'INVALID';

type QOFilterCriteria = QOFilterCompositeCriteria | QOFilterByFieldCriteria | QOFilterByValueCriteria;

type QOChildCalcNode = QOFormulaCalcNode | QOLookupByFilterCalcNode | QOLookupByAssociationCalcNode | QOAssociationCalcNode;
export type QOCalcNode = QOChildCalcNode | QOCompositeCalcNode;

export interface QOCalcChain {
  normal: QOCalcNode[];
  circularError: QOCalcNode[];
  refError: QOCalcNode[];
}

function toQOFilterCompositeCriteria(ctx: QOLookupCalcChainContext, filters: FilterUpCondition[]): QOFilterCompositeCriteria {
  const composite: QOFilterCompositeCriteria = {
    type: 'composite',
    conjunction: 'AND',
    criteria: [],
  };
  if (!filters.length) {
    return composite;
  }
  const conjunction = filters[0].link || 'OR';
  composite.conjunction = conjunction;

  filters.forEach((f, index) => {
    const { fieldId, symbol, value } = f;

    if (isObjHasFieldId(value)) {
      ctx.criteriaType = 'field';
      const item: QOFilterByFieldCriteria = {
        type: 'field',
        criteria: {
          symbol,
          symbolV2: getSymbol(ctx, ctx.thisField.valuesField.sheetId, fieldId, symbol),
          leftField: fieldId,
          leftFieldPropName: getPropNameForLeftInLookup(ctx, ctx.thisField.valuesField.sheetId, fieldId),
          rightField: value.fieldId,
          rightFieldPropName: getPropNameForRightInLookup(ctx, ctx.thisField.addr.sheetId, value.fieldId),
        },
      };
      composite.criteria.push(item);
    } else {
      ctx.criteriaType = 'rawValue';
      const item: QOFilterByValueCriteria = {
        type: 'rawValue',
        criteria: {
          symbol,
          symbolV2: getSymbol(ctx, ctx.thisField.valuesField.sheetId, fieldId, symbol),
          leftField: fieldId,
          leftFieldPropName: getPropNameForLeftInLookup(ctx, ctx.thisField.valuesField.sheetId, fieldId),
          rightValue: value,
          rightValueVersion: f.version,
        },
      };
      composite.criteria.push(item);
    }
  });

  return composite;
}

function fromQOFilterCompositeCriteria(qoFilter: QOFilterCompositeCriteria): FilterUpCondition[] {
  if (qoFilter.type !== 'composite') {
    throw new Error('filter-up should be composite criteria');
  }
  const filter: FilterUpCondition[] = qoFilter.criteria.map((item) => {
    const { type, criteria } = item;
    if (type === 'field') {
      const f: FilterUpCondition = {
        link: qoFilter.conjunction,
        symbol: criteria.symbol,
        fieldId: criteria.leftField,
        value: { fieldId: criteria.rightField },
      };
      return f;
    } else if (type === 'rawValue') {
      const f = {
        link: qoFilter.conjunction,
        fieldId: criteria.leftField,
        value: criteria.rightValue,
        symbol: criteria.symbol,
        version: criteria.rightValueVersion,
      } as FilterUpCondition;
      return f;
    }
    throw new Error(`unknown filter-up criteria type: ${type}`);
  });
  return filter;
}

function toNormalCalcNode(ctx: QOCalcChainContext, calcField: CalcField): QOCalcNode {
  let node: QOCalcNode;
  const { type, dirtyRows } = calcField;
  const dirtyInfo = (() => {
    if (dirtyRows === true) {
      return { allRecords: true, recordIds: [] };
    }
    const recordIds = dirtyRows ? Array.from(dirtyRows?.values()) : [];
    if (recordIds.length) {
      return { allRecords: false, recordIds };
    }
    return { allRecords: false, recordIds: [] };
  })();
  if (type === 'lookup' && calcField.isComposite) {
    node = {
      virtualNode: true,
      type: 'composite',
      addr: { sheetId: calcField.addr.sheetId, fieldId: '' },
      dirtyInfo,
      children: calcField.getAllBuddyFields().map((f) => toNormalCalcNode(ctx, f) as QOChildCalcNode),
      code: CompositeReasonCode.COMPOSITE_BY_SAME_FILTER,
      extra: '',
    };
  } else if (type === 'lookup' && calcField.valuesSource.type === 'filter') {
    const { addr, aggregator, valuesField, valuesSource, valuesVersion, formatter } = calcField;
    node = {
      type: 'lookup-by-filter',
      addr,
      dirtyInfo,
      agg: aggregator,
      select: valuesField.fieldId,
      selectPropName: getPropNameForSelectInLookup(ctx, valuesField.sheetId, valuesField.fieldId),
      fromSheetId: valuesField.sheetId,
      on: toQOFilterCompositeCriteria({ ...ctx, thisField: calcField }, valuesSource.filters),
      resultFieldType: calcField.resultFieldType,
      valuesVersion,
      formatter,
      extra: '',
    };
  } else if (type === 'lookup' && calcField.valuesSource.type === 'associate') {
    const { addr, aggregator, valuesField, valuesSource, valuesVersion, formatter } = calcField;
    node = {
      type: 'lookup-by-association',
      addr,
      dirtyInfo,
      agg: aggregator,
      select: valuesField.fieldId,
      selectPropName: getPropNameForSelectInLookup(ctx, valuesField.sheetId, valuesField.fieldId),
      fromSheetId: valuesField.sheetId,
      associationFieldId: valuesSource.associateField,
      associationFieldIdPropName: getPropNameForAssociationInLookup(ctx, addr.sheetId, valuesSource.associateField),
      resultFieldType: calcField.resultFieldType,
      valuesVersion,
      formatter,
      extra: '',
    };
  } else if (type === 'formula') {
    const { addr, rpn, rpnRefs, formatter } = calcField;
    node = {
      type: 'formula',
      addr,
      dirtyInfo,
      rpn,
      rpnRefs: rpnRefs.map((ref) => ref?.[0]),
      dbFormula: rpnToFormula(
        { formula: { expr: rpn, names: rpnRefs.map(([name]) => ({ name })) } },
        (id) => ({ sheetName: addr.sheetId, fieldName: id, propName: getPropNameForFormula(ctx, addr.sheetId, id) }),
      ),
      formula: '',
      formatter,
      extra: '',
    };
  } else if (type === 'associate') {
    const { addr, targetSheetId, titleFieldId } = calcField;
    node = {
      type: 'association',
      addr,
      dirtyInfo,
      fromSheetId: targetSheetId,
      titleFieldId,
      titleFieldIdPropName: getPropNameForTitleInAssociation(ctx, targetSheetId, titleFieldId),
      extra: '',
    };
  } else {
    assertNever(type as never, `calcField type: ${type}`);
  }
  if (calcField.isVirtual) {
    node.virtualNode = true;
  }
  return node;
}

function toCircularErrorCalcNode(ctx: QOCalcChainContext, calcField: CalcField): QOCalcNode {
  const node = toNormalCalcNode(ctx, calcField);
  node.circularError = true;
  return node;
}

function toRefErrorCalcNode(ctx: QOCalcChainContext, calcField: CalcField): QOCalcNode {
  const node = toNormalCalcNode(ctx, calcField);
  node.refError = true;
  return node;
}

type QOFieldDTO = FieldDTO & { sheetId: SheetId; isVirtual?: boolean };

function fromCalcNode(node: QOCalcNode): QOFieldDTO {
  const { type, addr } = node;
  if (type === 'formula') {
    const { rpn, rpnRefs, formatter, virtualNode } = node;
    return {
      sheetId: addr.sheetId,
      type: 'formula',
      id: addr.fieldId,
      name: `A-Formula-Field-${addr.fieldId}`,
      dataType: CellDataType.OBJECT,
      deleted: false,
      timestamp: Date.now(),
      editable: false,
      isPrimary: false,
      isVirtual: virtualNode,
      config: {
        renderFieldConfig: {
          props: {
            formula: {
              expr: rpn,
              names: rpnRefs.map((id) => ({ name: id })),
            } as FormulaRpn,
            formatter,
          },
        },
      },
    };
  } else if (type === 'association') {
    const { titleFieldId, fromSheetId } = node;
    return {
      sheetId: addr.sheetId,
      type: 'association',
      id: addr.fieldId,
      name: `An-Association-Field-${addr.fieldId}`,
      dataType: CellDataType.OBJECT,
      deleted: false,
      timestamp: Date.now(),
      editable: false,
      isPrimary: false,
      hidden: false,
      config: {
        renderFieldConfig: {
          props: {
            allowMultiSelect: true,
            bidirectional: true,
            pairFieldId: titleFieldId,
            sheetId: fromSheetId,
          },
        },
      },
    };
  } else if (type === 'lookup-by-association') {
    const { agg, associationFieldId, select, resultFieldType, formatter, valuesVersion, virtualNode } = node;
    return {
      sheetId: addr.sheetId,
      type: 'lookup',
      id: addr.fieldId,
      name: `An-Lookup-Field-${addr.fieldId}`,
      dataType: CellDataType.OBJECT,
      deleted: false,
      timestamp: Date.now(),
      editable: false,
      isPrimary: false,
      hidden: false,
      isVirtual: virtualNode,
      config: {
        renderFieldConfig: {
          props: {
            associateField: associationFieldId,
            valuesField: select,
            aggregator: agg,
            formatter,
            valuesVersion: valuesVersion === undefined ? undefined : 1,
            resultFieldType,
          },
        },
      },
    };
  } else if (type === 'lookup-by-filter') {
    const { fromSheetId, agg, select, resultFieldType, formatter, valuesVersion, virtualNode } = node;
    return {
      sheetId: addr.sheetId,
      type: 'filter-up',
      id: addr.fieldId,
      name: `An-FilterUp-Field-${addr.fieldId}`,
      dataType: CellDataType.OBJECT,
      deleted: false,
      timestamp: Date.now(),
      editable: false,
      isPrimary: false,
      hidden: false,
      isVirtual: virtualNode,
      config: {
        renderFieldConfig: {
          props: {
            aggregator: agg,
            targetSheet: fromSheetId,
            valuesField: select,
            formatter,
            valuesVersion: valuesVersion === undefined ? undefined : 1,
            resultFieldType,
            filters: fromQOFilterCompositeCriteria(node.on),
          },
        },
      },
    };
  }
  throw new Error(`unknown calc node type: ${type}`);
}

export function toQOCalcChain(ctx: QOCalcChainContext, normal: CalcField[], circularError: CalcField[], refError: CalcField[]): QOCalcChain {
  return {
    normal: normal.map((n) => toNormalCalcNode(ctx, n)),
    circularError: circularError.map((n) => toCircularErrorCalcNode(ctx, n)),
    refError: refError.map((n) => toRefErrorCalcNode(ctx, n)),
  };
}

export function fromQOCalcChain(qoChain: QOCalcChain): [normal: QOFieldDTO[], circular: QOFieldDTO[], ref: QOFieldDTO[]] {
  const normal = qoChain.normal.map(fromCalcNode);
  const circular = qoChain.circularError.map(fromCalcNode);
  const ref = qoChain.refError.map(fromCalcNode);
  return [
    normal,
    circular,
    ref,
  ];
}

export function printCalcChainInMermaidFormat(ctx: Model, calcChain: CalcField[]) {
  let mermaidStr = 'graph TD;\n'; // 使用TD表示从上到下的布局
  const visited = new Set();

  function visit(node: CalcField) {
    if (visited.has(node.sortOrder)) return;
    visited.add(node.sortOrder);

    const sheet = ctx.getSheet(node.addr.sheetId);
    const fieldName = sheet?.getField(node.addr.fieldId)?.name;
    const name = `${sheet?.name}-${fieldName}`;
    // 定义节点，格式为：id["name (id)"]
    mermaidStr += `    ${node.sortOrder}["${name} (${node.sortOrder})"];\n`;

    node.prev.forEach((nextNode) => {
      mermaidStr += `    ${node.sortOrder} --> ${nextNode.sortOrder};\n`;
      visit(nextNode);
    });
  }

  // 遍历图中所有节点
  calcChain.forEach((node) => {
    if (!visited.has(node.sortOrder)) {
      visit(node);
    }
  });

  return mermaidStr;
}

type CalcFieldTypes = 'filter-up' | 'lookup' | 'formula';
type UserFieldTypes = Exclude<FieldTypes, CalcFieldTypes>;

const getPropNameForUserFieldType = (ft: UserFieldTypes) => {
  switch (ft) {
    case 'autoNumber':
      return 'textValue';
    case 'checkbox':
      return 'boolValue';
    case 'date':
    case 'createdTime':
    case 'updatedTime':
      return 'trimmedOADate';
    case 'currency':
    case 'number':
    case 'progress':
    case 'starRating':
      return 'numValue';
    case '':
    case 'barcode':
    case 'email':
    case 'idCard':
    case 'telephone':
    case 'text':
    case 'textarea':
    case 'primaryDoc':
      return 'textValue';
    case 'person':
      return 'fullname$list';
    case 'creator':
    case 'updater':
      return 'fullname';
    case 'select':
      return 'optionTitle';
    case 'multiSelect':
      return 'optionTitle$list';
    case 'association':
      return 'title$list';
    case 'richText':
      return 'plain';
    case 'link':
      return 'text';
    case 'group':
      return 'name$list';
    case 'department':
      return 'name$list';
    case 'geolocation':
      return 'fullname';
    case 'address':
      return 'fullname';
    case 'file':
    case 'sign':
    case 'image':
      return 'name$list';
    case 'flow':
      return 'stageName';
    case 'object':
      return 'textValue';
    case 'button':
      return 'textValue'; // button 也会返回值
    default:
      assertNever(ft, 'unknown field type');
  }
};

const getPropNameForLookupOriginValues = (ft: UserFieldTypes) => {
  switch (ft) {
    case 'checkbox':
      return 'boolValue$list';
    case '':
    case 'text':
    case 'barcode':
    case 'email':
    case 'idCard':
    case 'telephone':
    case 'autoNumber':
    case 'textarea':
      return 'textValue$list';
    case 'number':
    case 'currency':
    case 'progress':
    case 'starRating':
      return 'numValue$list';
    case 'date':
    case 'createdTime':
    case 'updatedTime':
      return 'oadate$list';
    case 'richText':
      return 'plain$list';
    case 'link':
      return 'text$list';
    case 'geolocation':
      return 'fullname$list';
    case 'address':
      return 'fullname$list';
    case 'flow':
      return 'stageName$list';
    case 'object':
      return 'textValue$list';
    case 'select':
      return 'optionTitle$list';
    case 'creator':
    case 'updater':
      return 'fullname$list';
    case 'person':
    case 'multiSelect':
    case 'image':
    case 'file':
    case 'sign':
    case 'association':
    case 'group':
    case 'department':
    case 'button':
    default:
      return getPropNameForUserFieldType(ft);
  }
};

function getPropNameForFormula(ctx: QOCalcChainContext, sheetId: string, fieldId: string) {
  const field = ctx.model.getSheet(sheetId)?.getField(fieldId);
  if (!field) {
    return '';
  }
  const { type } = field;

  switch (type) {
    case 'formula': {
      let ft: UserFieldTypes | undefined = getFxResultFormat(field)?.type;
      if (!ft) {
        ft = (getFormulaLookupResultFieldType(field) ?? 'text') as UserFieldTypes;
      }
      return getPropNameForUserFieldType(ft);
    }
    case 'filter-up':
    case 'lookup': {
      const ft = (getFormulaLookupResultFieldType(field) ?? 'text') as UserFieldTypes;
      const aggregator = field.config.renderFieldConfig?.props?.aggregator ?? 'VALUES';
      switch (aggregator) {
        case 'VALUES':
        case 'UNIQUE':
          return getPropNameForLookupOriginValues(ft);
        case 'AND':
        case 'OR':
        case 'XOR':
          return 'boolValue';
        case 'AVERAGE':
        case 'SUM':
        case 'MIN':
        case 'MAX':
        case 'COUNT':
        case 'COUNTA':
        case 'COUNTA.UNIQUE':
        case 'COUNTRECORDS':
          return 'numValue';
        case 'CONCATENATE':
          return 'textValue';
        default:
          assertNever(aggregator, 'unknown lookup aggregator');
      }
      break;
    }
    default:
      return getPropNameForUserFieldType(type);
  }
}

const getPropNameForValueLookupOrigin = (ft: UserFieldTypes) => {
  switch (ft) {
    case 'autoNumber':
      return 'textValue';
    case 'checkbox':
      return 'boolValue';
    case 'date':
    case 'createdTime':
    case 'updatedTime':
      return 'timestamp';
    case 'currency':
    case 'number':
    case 'progress':
    case 'starRating':
      return 'numValue';
    case '':
    case 'barcode':
    case 'email':
    case 'idCard':
    case 'telephone':
    case 'text':
    case 'textarea':
    case 'primaryDoc':
      return 'textValue';
    case 'person':
      return 'id$list';
    case 'creator':
    case 'updater':
      return 'uid';
    case 'select':
      return 'optionId';
    case 'multiSelect':
      return 'optionId$list';
    case 'association':
      return 'title$list';
    case 'richText':
      return 'plain';
    case 'link':
      return 'text';
    case 'group':
      return 'cid$list';
    case 'department':
      return 'orgId$list';
    case 'geolocation':
      return 'fullname';
    case 'address':
      return 'fullname';
    case 'file':
    case 'sign':
    case 'image':
      return 'name$list';
    case 'flow':
      return 'stageId';
    case 'object':
      return 'textValue';
    case 'button':
      return 'textValue'; // button 也会返回值
    default:
      assertNever(ft, 'unknown field type');
  }
};

function getPropNameForValue(ctx: QOCalcChainContext, sheetId: string, fieldId: string) {
  const field = ctx.model.getSheet(sheetId)?.getField(fieldId);
  if (!field) {
    return '';
  }
  const { type } = field;

  switch (type) {
    case 'formula': {
      let ft: UserFieldTypes | undefined = getFxResultFormat(field)?.type;
      if (!ft) {
        ft = (getFormulaLookupResultFieldType(field) ?? 'text') as UserFieldTypes;
      }
      return getPropNameForUserFieldType(ft);
    }
    case 'filter-up':
    case 'lookup': {
      const ft = (getFormulaLookupResultFieldType(field) ?? 'text') as UserFieldTypes;
      const aggregator = field.config.renderFieldConfig?.props?.aggregator ?? 'VALUES';
      switch (aggregator) {
        case 'VALUES':
        case 'UNIQUE':
          return getPropNameForValueLookupOrigin(ft);
        case 'AND':
        case 'OR':
        case 'XOR':
          return 'boolValue';
        case 'AVERAGE':
        case 'SUM':
        case 'MIN':
        case 'MAX':
        case 'COUNT':
        case 'COUNTA':
        case 'COUNTA.UNIQUE':
        case 'COUNTRECORDS':
          return 'numValue';
        case 'CONCATENATE':
          return 'textValue';
        default:
          assertNever(aggregator, 'unknown lookup aggregator');
      }
      break;
    }
    default:
      return getPropNameForValueLookupOrigin(type);
  }
}

function getSymbol(ctx: QOLookupCalcChainContext, sheetId: string, leftFieldId: string, symbol: FilterUpCondition['symbol']) {
  switch (symbol) {
    case 'DATE_EQ':
    case 'BEFORE':
    case 'AFTER':
    case 'EXIST':
    case 'UN_EXIST':
    case 'GT':
    case 'GTE':
    case 'LT':
    case 'LTE':
      return symbol;
    case 'ANY_OF':
      return 'ANY_OF';
    case 'NONE_OF':
      return 'NOT_ANY_OF';
    case 'CONTAIN':
      return 'TEXTCONTAIN';
    case 'EXCLUSIVE':
      return 'NOT_TEXTCONTAIN';
    case 'EQ':
    case 'NE': {
      const leftFieldPropName = getPropNameForLeftInLookup(ctx, sheetId, leftFieldId);
      switch (leftFieldPropName) {
        case 'numValue$list':
        case 'fullname$list':
        case 'boolValue$list':
        case 'id$list':
        case 'cid$list':
        case 'orgId$list':
        case 'name$list':
        case 'optionId$list':
        case 'oadate$list':
        case 'optionTitle$list':
        case 'plain$list':
        case 'stageName$list':
        case 'text$list':
        case 'textValue$list':
        case 'title$list':
          return symbol === 'EQ' ? 'ALL_OF' : 'NOT_ALL_OF';
        case 'boolValue':
          return symbol === 'EQ' ? 'EQ_STRICT' : 'NOT_EQ_STRICT';
        case 'numValue':
          return symbol === 'EQ' ? 'EQ' : 'NE';
        default:
          return symbol === 'EQ' ? 'EQ_JOINTEXT' : 'NOT_EQ_JOINTEXT';
      }
    }
    default:
      return 'INVALID';
  }
}

function getPropNameForLeftInLookup(ctx: QOLookupCalcChainContext, sheetId: string, fieldId: string) {
  const { criteriaType = 'field' } = ctx;
  if (criteriaType !== 'rawValue') {
    return getPropNameForFormula(ctx, sheetId, fieldId);
  }
  return getPropNameForValue(ctx, sheetId, fieldId);
}

function getPropNameForRightInLookup(ctx: QOLookupCalcChainContext, sheetId: string, fieldId: string): string {
  return getPropNameForFormula(ctx, sheetId, fieldId);
}

function getPropNameForSelectInLookup(ctx: QOCalcChainContext, sheetId: string, fieldId: string): string {
  return getPropNameForFormula(ctx, sheetId, fieldId);
}

function getPropNameForAssociationInLookup(ctx: QOCalcChainContext, sheetId: string, fieldId: string): string {
  return 'rowId$list';
}

function getPropNameForTitleInAssociation(ctx: QOCalcChainContext, sheetId: string, fieldId: string): string {
  return getPropNameForFormula(ctx, sheetId, fieldId);
}

/**
 * 双向关联单元格也是计算结果，但仅在合法的时才会落库，需满足以下所有情形：
 * 1. 关联字段不为空
 * 2. 关联字段有至少 1 个选项
 */
export function isValidAssociationCalcResult(cell: null | RowCell) {
  if (!cell || cell.dataType !== 'OBJECT') return false;
  const filterFactor = cell.value?.filterFactor;
  return Array.isArray(filterFactor) && !!filterFactor?.length;
}
