/* eslint-disable max-lines */
/* eslint-disable max-len */
/* eslint-disable @typescript-eslint/consistent-type-definitions */
import debug from 'debug';
import { groupBy, isEqual } from 'lodash-es';
import { ConstArray, ConstArrayItem, evaluateRpn } from '@ali/zongheng-formula';
import type { CurrentValueContext } from '@ali/zongheng-formula';
import {
  isEqualCellValue,
  getCellExtractorByField,
  LocalOperationActon,
  logger,
  MetaOperationAction,
  OperationTarget,
  RawDataOperationAction,
  WidgetTypeEnums,
  getValueOfAssociationsCell,
  isAutoNumValue,
  AutoNumUtils,
  createAssociationsCell,
  assertNever,
  FormulaRpn,
  CellDataType,
  NestedMap2,
  isCalcField,
} from '@ali/notable-common';
import type { SheetId } from '../../../sheet/types';
import type { Model } from '../../../model';
import type { Row } from '../../../row';
import type { WorkbookDTO } from '../../../workbook/types';
import type { Operation, UpdateFieldPayload } from '../../../operations/types';
import type { AssociationsCellValue, ObjectCellValue, RecordDTO, RecordId, RowCell, RowId, UpdateCells } from '../../../row/types';
import { renderAssociationsTitle } from '../../../public/opCreator/helper';
import type { FieldDTO, FieldId, IModelField } from '../../../field';
import type { RowData, SheetModel } from '../../../sheet';
import { getPrimaryField } from '../../../utils';
import type { AssoCalcField, CalcField, FilterUpCalcField, LookupCalcField } from '../calc-field';
import { FormulaCalcField, isFilterUpCalcField, isLookupAssoCalcField } from '../calc-field';
import type { CalcFieldManager } from '../calc-field-manager';
import type { ExternalFxEngine } from '../../formula-engine';
import { getFilteredRows, findAssoRowsIn1_ByAsso1AndRowsIn2, GetFieldFunc, makeVirtualFormulaField, getCalcValuesField } from '../../utils';
import { EvaluateCtxForNotable, FnGetSheet, HookGetJoinResult, HookGetLookupValues, JoinResult, LookupContext, ValuesTarget } from '../../functions';
import { ensureLookupValuesIndexes } from '../../utils/filter-up';
import { EventType } from '../../../types';
import { CompositeFilterUpCalcField } from '../calc-field/lookup/composite-filter-up';
import { isCompositeFilterUpCalcField } from '../calc-field/lookup';
import { topoSortCalcChain } from '../calc-field-manager/base-mgr';
import * as FxValue from '../../utils/FxValueUtils';
import { splitFormulaCalcField } from '../../formula-parser/rpn-analyzer';
import { isUpdateFieldPropsMakingDirty } from '../is-delta-making-dirty';
import { DEFAULT_SETTINGS, validateCalculatorSettings } from './settings';
import { getFxCellValueConvertor, cellValueOfFxError_CIRCULAR, cellValueOfFxError_ERROR, NotableConstArray, toCellValue, cellValueOfFxError_REF, toQOCalcChain, isValidAssociationCalcResult } from './utils';
import { CALC_INIT_TIMESTAMP, CalcTask, Evaluate, FxCalcContext, FxCalcTaskManager, HookCalcSliceDone } from './fx-task-scheduler';
import { evaluateCalcField } from './evaluate';
import { uniqueValuesByRows } from './unique';

const SLOW_EVALUATION_MS = 50;

let FX_ARRAY_RESULT_LOGGED = false;

export { toQOCalcChain, toCellValue } from './utils';
export type { QOCalcChain, QOCalcNode, QOCalcNodeExtra, QOCalcDirtyInfo } from './utils';

export type { Evaluate, FxCalcContext, HookCalcSliceDone } from './fx-task-scheduler';

export type CalcResultChangeData = {
  sheet: SheetModel;
  rowId: RowId;
  value: UpdateCells;
  origin: UpdateCells;
};

export interface QOCalcChainOptions {
  compositeFilterUp?: boolean;
}

export type FnCallbackCalcResult = (data: CalcResultChangeData) => void;

const debugLog = debug('calc:rows_calc');
const dagDebugLog = debug('calc:dag_calc');

const OPT_UPDATE_ROW = {
  skipOperationStamp: true,
};

const ERR_NO_ASSO_ROW = {
  error: FxValue.makeError('#UNCALC!').value,
  no_asso_row: 1,
};

class CalcResultCollector {
  timestamp: number;
  result: RowData[] = [];
  reconciliation: RowId[] = [];

  constructor(
    private readonly onCollect: FnCallbackCalcResult = updateResultToRow,
    private readonly bizLogger?: IBizLogger,
  ) {
    this.timestamp = Date.now();
  }

  collect(data: CalcResultChangeData) {
    this.result.push({ ...data, timestamp: this.timestamp });
    if (this.onCollect !== updateResultToRow) {
      this.onCollect(data);
    }
  }

  collectReconciliation(diffRecordId: RowId) {
    this.reconciliation.push(diffRecordId);
  }
}

export interface CalcCalcFieldsOptions {
  calcChainType: 'normal' | 'circular' | 'refError';
  onFieldCalculated?: () => Promise<void>;
}

const isErrNoAssoRow = (err: unknown) => (err && (typeof err === 'object') && ('no_asso_row' in err) && err?.no_asso_row === ERR_NO_ASSO_ROW.no_asso_row);

const updateResultToRow: FnCallbackCalcResult = ({ sheet, rowId, value, origin }) => {
  const timestamp = Date.now();
  sheet.updateRow({ rowId, origin, value, timestamp }, OPT_UPDATE_ROW);
};

export type DirtyInfoMap = Map<SheetId, { rows: true | Set<RecordId>; fieldIds: Set<FieldId> }>;

/**
 * - 异步分片计算：按 ROW 分片计算；
 */
export class RowsCalculator {
  private readonly taskScheduler: FxCalcTaskManager;
  private calcSheetIds: string[] = [];
  readonly onCalcSliceDone = (hook: HookCalcSliceDone) => this.taskScheduler.onSlicingDone(hook);

  constructor(
    private readonly model: Model,
    private readonly calcFields: CalcFieldManager,
    private readonly externalFxEngine?: ExternalFxEngine | undefined,
    private readonly bizLogger?: IBizLogger,
    private readonly enableSheetFormulaRef = false,
    private enableSplitAst = false,
    private readonly onlyCalcActiveSheet = false,
  ) {
    this.taskScheduler = new FxCalcTaskManager(model, calcFields, this.bizLogger);
    this.taskScheduler.onSlicingDone(() => {
      this.model.emitDataChange();
    });

    validateCalculatorSettings(DEFAULT_SETTINGS);
  }

  get fxCellValueConvertorManager() {
    return this.model.workbook.fxCellValueConvertorManager;
  }

  // #region
  private _lookupDirtyMode: 'associated-records' | 'field' = 'associated-records';

  /**
   * 该配置决定：「关联引用字段」在 updateRecords 时如何标脏相关记录。
   * - 默认为 `associated-records`，根据变更所在行的「关联字段」具体的关联项、将他们标脏；
   * - 可配置为 `field`，按列维度、将引用了变更字段的「关联引用」整列标脏；
   */
  setLookupDirtyMode(mode: 'associated-records' | 'field') {
    this._lookupDirtyMode = mode;
  }
  // #endregion

  setCalcSheetIds(sheetIds: string[]) {
    const prev = this.calcSheetIds;
    this.calcSheetIds = sheetIds.sort();
    if (this.onlyCalcActiveSheet && !isEqual(prev, this.calcSheetIds)) {
      this.recalculate();
    }
  }

  isAsyncCalc() {
    return this.taskScheduler.isAsyncCalc();
  }

  hasAsyncCalcTasks() {
    return this.taskScheduler.hasTasks();
  }

  toggleAsyncCalc(enable: boolean) {
    this.taskScheduler.updateConfig({
      disableAsync: !enable,
    });
  }

  toggleSplitAst(enable: boolean) {
    this.enableSplitAst = enable;
  }

  setSyncCalcExpireAt(syncCalcExpireAt: number) {
    this.taskScheduler.updateConfig({ syncCalcExpireAt });
  }

  setAsyncCalcDuration(duration: number) {
    this.taskScheduler.updateConfig({ duration });
  }

  collectDirtyInfo(result: DirtyInfoMap, sheetId: SheetId, calcField: CalcField) {
    if (!calcField.isDirty()) return;

    const info = result.get(sheetId);
    if (!info) return;

    // 当前计算字段的标脏信息
    const { addr, dirtyRows } = calcField;
    info.fieldIds.add(addr.fieldId);
    if (dirtyRows === true || info.rows === true) {
      info.rows = true;
    } else if (dirtyRows) {
      for (const row of dirtyRows) info.rows.add(row);
    }

    // 被计算字段引用的普通字段也要拉取
    const currentSheetDirty = result.get(calcField.addr.sheetId);
    if (!currentSheetDirty) return;

    switch (calcField.type) {
      case 'formula': {
        for (const ref of calcField.getAllRefs()) {
          if (ref.isEntireField) {
            const refSheet = result.get(ref.sheetId);
            if (refSheet) {
              refSheet.rows = true;
              refSheet.fieldIds.add(ref.fieldId);
            }
          } else {
            currentSheetDirty.fieldIds.add(ref.fieldId);
          }
        }
        break;
      }
      case 'associate': {
        const { targetSheetId, titleFieldId } = calcField;
        const assoSheetDirty = result.get(targetSheetId);
        if (assoSheetDirty) {
          assoSheetDirty.rows = true; // 计算前不知道具体引用了哪些行、需要全量行记录
          assoSheetDirty.fieldIds.add(titleFieldId);
        }
        break;
      }
      case 'lookup': {
        const { valuesField } = calcField;

        // 存在 sheet 被删除但查找引用字段中仍然使用该 sheetId 的情况。
        // 会导致服务端计算拉取数据失败，因此需要过滤
        const sheet = this.model.getSheet(valuesField.sheetId);
        if (!sheet) return;

        // 查找引用、关联引用字段，其 查找/关联的 sheet 需要全量行记录
        const valuesSheetDirty = result.get(valuesField.sheetId);
        if (!valuesSheetDirty) return;

        valuesSheetDirty.rows = true;
        for (const ref of calcField.getRefsInValuesSheet()) {
          valuesSheetDirty.fieldIds.add(ref);
        }

        if (isLookupAssoCalcField(calcField)) {
          const { valuesSource } = calcField;
          currentSheetDirty.fieldIds.add(valuesSource.associateField);
        } else if (isFilterUpCalcField(calcField)) {
          // 查找引用，「条件值」引用的字段
          for (const ref of calcField.getRefsOfFiltersValue()) {
            currentSheetDirty.fieldIds.add(ref);
          }
          // 查找引用，「条件值」为固定值时，获取待计算的右值
          calcField.getRefOfConstantFiltersValue(
            result,
            (id: SheetId, fieldId: FieldId) => this.model.getSheet(id)?.getField(fieldId),
            (id: SheetId) => {
              const targetSheet = this.model.getSheet(id);
              return targetSheet && getPrimaryField(targetSheet.fieldMap)?.id;
            },
          );
        }
        break;
      }
      default:
        assertNever(calcField, 'unknown calcField');
    }
  }

  /** 取得当前标脏/待重算的记录，及其计算引用的记录 */
  getDirtyAndReferedRowsInfo() {
    const result: DirtyInfoMap = new Map();

    this.model.workbook.sheets.forEach((sheetId) => {
      result.set(sheetId, { rows: new Set(), fieldIds: new Set() });
    });

    // 根据「直接标记重算的 CalcFields」，将所有「被影响的 CalcFields」标记重算
    this.setDirtyRecursively();

    this.calcFields.iterAllCalcField((calcField, sheet) => {
      this.collectDirtyInfo(result, sheet.id, calcField);
    });

    return result;
  }

  // #region 响应 Model 编辑，标记待计算的 Rows
  handleFromJSON({ sheetMap }: WorkbookDTO) {
    debugLog('[respond_fromjson]');

    if (this.model.hasFullRawData()) {
      Object.values(sheetMap).forEach((s) => this.handleSheetFullRawData(s.id));
    }
  }

  handleApplyOP(ops: Operation[]) {
    for (let idx = 0; idx < ops.length; idx++) {
      const op = ops[idx];
      switch (op.target) {
        case OperationTarget.META: this.handleApplyMetaOP(op); break;
        case OperationTarget.RAW_DATA: this.handleApplyRawDataOP(op); break;
        case OperationTarget.LOCAL: this.handleApplyLocalOP(op); break;
        default: break;
      }
    }
  }

  private handleApplyLocalOP(op: Operation) {
    if (op.target !== OperationTarget.LOCAL) return;

    if (op.action === LocalOperationActon.SET_FULL_RAW_DATA && !op.payload.skipInitialFullCalc) {
      Object.entries(op.payload.data).forEach(([sheetId]) => {
        this.handleSheetFullRawData(sheetId);
      });
      return;
    }
    if (op.action === LocalOperationActon.MERGE_SLICE_DATA) {
      const { sheetId, value } = op.payload;
      const rows = value.rawData?.rows;
      if (rows?.length) {
        this.handleSheetFullRawData(sheetId);
      }
      return;
    }
    if (op.action === LocalOperationActon.LOCAL_UPDATE_RECORDS) {
      this.handleUpdateRecords(op.payload.sheetId, op.payload.records.map((row) => [row.id, row]));
    }
  }

  private handleApplyMetaOP(op: Operation) {
    if (op.target !== OperationTarget.META) return;

    switch (op.action) {
      case MetaOperationAction.INSERT_SHEET: {
        if (this.model.hasFullRawData()) {
          const { id } = op.payload.value;
          this.handleSheetFullRawData(id);
        }
        break;
      }
      case MetaOperationAction.DELETE_SHEET: {
        this.calcFields.getCalcFieldsInSheet(op.payload.sheetId)?.forEach((calcField) => {
          calcField.remove(false);
        });
        break;
      }
      case MetaOperationAction.UPDATE_FIELD: {
        this.handleUpdateField(op.payload);
        break;
      }
      default: return;
    }
    debugLog('[apply_meta] %s', op.action);
  }

  private handleApplyRawDataOP(op: Operation) {
    if (op.target !== OperationTarget.RAW_DATA) return;

    switch (op.action) {
      case RawDataOperationAction.INSERT_RECORDS: {
        this.handleInsertRecords(op.payload.sheetId, Object.keys(op.payload.records));
        break;
      }
      case RawDataOperationAction.INSERT_ROW:
      case RawDataOperationAction.APPEND_ROW: {
        this.handleInsertRecords(op.payload.sheetId, [op.payload.rowId]);
        break;
      }

      case RawDataOperationAction.DELETE_RECORDS:
      case RawDataOperationAction.DELETE_ROW: {
        // don't need to remove the deleted row from CalcFields' dirty

        // fx: 无需处理；
        // lookup: 删除行时，会有对应的 OP 处理 asso-cell，因此这里无需处理

        // filter-up: 引用了被删除行的 filter-up 需要标脏
        this.setFilterUpDirtyOnChangingRow(op.payload.sheetId, true);
        break;
      }

      case RawDataOperationAction.UPDATE_RECORDS: {
        this.handleUpdateRecords(op.payload.sheetId, Object.entries(op.payload.records));
        break;
      }
      case RawDataOperationAction.UPDATE_ROW: {
        this.handleUpdateRecords(op.payload.sheetId, [
          [op.payload.rowId, { cells: op.payload.value }],
        ]);
        break;
      }
      case RawDataOperationAction.MODIFY_ASSOCIATIONS_CELL: {
        const { sheetId, rowId, fieldId } = op.payload;
        const fakeCells = { [fieldId]: null };
        this.handleUpdateRecords(sheetId, [
          [rowId, { cells: fakeCells }],
        ]);
        break;
      }
      default: return;
    }
    debugLog('[apply_rawdata] %s', op.action);
  }

  /** 将新 sheet 的所有 rows 创建计算任务 */
  private handleSheetFullRawData(sheetId: SheetId) {
    const currentSheet = this.model.getSheet(sheetId);
    if (!currentSheet) return;

    // 将本表内所有 CalcFields 标脏
    this.calcFields.getCalcFieldsInSheet(sheetId)?.forEach((calcField) => {
      calcField.overwriteDirtyRows(true);
    });

    // 遍历其它 sheet 内所有 lookup, filter-up 字段
    this.calcFields.iterAllCalcField((calcField) => {
      if (
        calcField.type === 'lookup'
        && calcField.valuesField.sheetId === currentSheet.id
      ) {
        calcField.appendDirtyRows(true);
      }
    });
  }

  private handleInsertRecords(
    sheetId: SheetId,
    newRows: RowId[],
  ) {
    // 其中的所有 CalcFields 都要算
    this.calcFields.getCalcFieldsInSheet(sheetId)?.forEach((calcField) => {
      calcField.appendDirtyRows(newRows);
    });
    // 引用当前 sheet 的 filter-up 都需要重算
    this.setFilterUpDirtyOnChangingRow(sheetId, true);
  }

  private handleUpdateRecords(
    changedSheetId: SheetId,
    changedEntries: Array<[RowId, { cells: UpdateCells }]>,
  ) {
    const changedSheet = this.model.getSheet(changedSheetId);
    if (!changedSheet) return;

    const changedFieldSet = new Set<FieldId>(); // updateRecords OP 内不会有计算字段
    changedEntries.forEach(([, { cells }]) => {
      for (const fieldId of Object.keys(cells)) {
        changedFieldSet.add(fieldId);
      }
    });
    const changedFields = Array.from(changedFieldSet);
    const changedRows = changedEntries.map(([rowId]) => rowId);

    // 需要找到：直接引用到 changedFields 的 CalcField 进行标脏
    this.calcFields.iterAllCalcField((calcField) => {
      // 普通公式字段
      if (calcField.type === 'formula') {
        // formula-field, 若引用了 changedFields，写入 changedRows
        const refs = calcField.getAllRefs().filter((ref) => ref.sheetId === changedSheet.id);
        if (refs.some((ref) => ref.isEntireField && changedFields.includes(ref.fieldId))) {
          calcField.appendDirtyRows(true);
        } else if (refs.some((ref) => changedFields.includes(ref.fieldId))) {
          calcField.appendDirtyRows(changedRows);
        }
        return;
      }

      // 查找引用字段
      if (isFilterUpCalcField(calcField)) {
        if (calcField.isFieldsReferencedAsConditionOrValues(changedSheet.id, changedFields)) {
          calcField.appendDirtyRows(true); // 整列标脏
        } else if (calcField.isFieldsReferencedAsConditionsValue(changedSheet.id, changedFields)) {
          calcField.appendDirtyRows(changedRows);
        }
        return;
      }

      // 关联引用字段
      if (isLookupAssoCalcField(calcField)) {
        const { valuesField, valuesSource } = calcField;
        const assoField = valuesSource.associateField;
        const assoSheet = calcField.addr.sheetId;

        if (changedFields.includes(assoField)) {
          // lookup 的 asso 在 changedFields 中
          calcField.appendDirtyRows(changedRows);
        }

        if (
          valuesField.sheetId === changedSheet.id
          && changedFields.includes(valuesField.fieldId)
        ) {
          if (this._lookupDirtyMode === 'associated-records') {
            // calcField 的 VALUES 字段引用到了 changedFields => 精确地找到 changedRows 对应的关联行进行标脏
            findAssoRowsIn1_ByAsso1AndRowsIn2(changedRows, changedSheet, assoField, assoSheet, (assoRows) => {
              calcField.appendDirtyRows(assoRows);
            });
          } else {
            // 整列标脏
            calcField.appendDirtyRows(true);
          }
        }
        return;
      }

      // 单向关联/双向关联
      if (calcField.type === 'associate') {
        const { addr, targetSheetId, titleFieldId } = calcField;
        // 关联字段的关联表的主字段 有 cell 变更，本关联字段的 cell 要更新 title
        if (
          targetSheetId === changedSheet.id
          && changedFields.includes(titleFieldId)
        ) {
          if (this._lookupDirtyMode === 'associated-records') {
            findAssoRowsIn1_ByAsso1AndRowsIn2(changedRows, changedSheet, addr.fieldId, addr.sheetId, (assoRows) => {
              calcField.appendDirtyRows(assoRows);
            });
          } else {
            // 整列标脏
            calcField.appendDirtyRows(true);
          }
          return;
        }
        // 关联字段本身有 cell 发生变更（增删关联项），此 cell 要更新 title
        if (changedFields.includes(addr.fieldId)) {
          calcField.appendDirtyRows(changedRows);
        }
      }
    });
  }

  private handleUpdateField(payload: UpdateFieldPayload) {
    const changePath = payload.paths.join('.');
    if (
      changePath !== 'type' && !changePath.startsWith('config.renderFieldConfig')
    ) {
      return;
    }

    const field = this.model.getSheet(payload.sheetId)?.getMetaField(payload.fieldId);
    if (!field) return;

    const setImpactedFieldsDirty = () => this.calcFields.iterCalcFieldsByRefField(
      payload,
      (_sheetId, fields) => fields.forEach((calcField) => calcField.appendDirtyRows(true)),
    );

    if (changePath === 'type') {
      // 如果改动前后是 创建人/时间、更新人/时间，则输入整列变动，此时不一定有完整的 updateRecords
      if (isCreateOrUpdateFields(field.type) || isCreateOrUpdateFields(payload.origin)) {
        setImpactedFieldsDirty();
      }
    } else if (isUpdateFieldPropsMakingDirty(payload, field)) {
      setImpactedFieldsDirty();
    }
  }

  /** 增/删/编辑 row 时，引用相关字段的 FilterUp 字段要重算 */
  private setFilterUpDirtyOnChangingRow(
    changedSheet: SheetId,
    changedFieldsOrAll: true | FieldId[],
  ) {
    this.calcFields.iterCalcFieldsBySheet((calcFields) => {
      for (const field of calcFields) {
        if (isFilterUpCalcField(field)) {
          if (
            changedFieldsOrAll === true
              ? (field.valuesField.sheetId === changedSheet)
              : (field.isFieldsReferencedAsConditionOrValues(changedSheet, changedFieldsOrAll))
          ) {
            field.appendDirtyRows(true);
          }
        }
      }
    });
  }

  clearAllDirtyInfo() {
    this.calcFields.iterAllCalcField((cf) => {
      cf.clearDirtyRows();
    });
  }

  /** 根据已有的标脏 (CalcField.dirtyRows) 将受影响的 CalcFields 都标脏 (递归) */
  setDirtyRecursively() {
    let fieldsToSolve: CalcField[] = [];
    this.calcFields.iterAllCalcField((calcField) => {
      fieldsToSolve.push(calcField);
    });
    this.calcFields.buildAllRelations();

    while (fieldsToSolve.length) {
      const nextFields: CalcField[] = [];
      for (let idx = 0; idx < fieldsToSolve.length; idx++) {
        const calcField = fieldsToSolve[idx];
        const nextFieldHandler = makeDealImpactedCalcFieldFn(calcField, this.model, this._lookupDirtyMode);
        nextFieldHandler && calcField.next.forEach((item) => {
          if (item.dirtyRows !== true && nextFieldHandler(item)) {
            nextFields.push(item);
          }
        });
      }
      fieldsToSolve = nextFields;
    }
  }
  // #endregion

  // #region Calc
  private suspendCount = 0;

  isCalculationSuspended() {
    return this.suspendCount > 0;
  }

  suspendCalc() {
    this.suspendCount++;
  }

  resumeCalc() {
    this.suspendCount--;
    if (this.suspendCount <= 0) {
      this.suspendCount = 0;
      this.recalculate();
    }
  }

  /**
   * 仅计算 sheet 内不涉及引用其它行记录的 formula 字段。
   * - 不受 suspend, async 配置影响，必定同步执行
   */
  calcBasicFxFieldsInSheet(
    sheetId: SheetId,
    callbackCalcResult: FnCallbackCalcResult = updateResultToRow,
  ) {
    const calcFieldsInSheet = this.calcFields.getCalcFieldsInSheet(sheetId);
    if (!calcFieldsInSheet) return;
    const { model } = this;
    const sheet = model.getSheet(sheetId);
    if (!sheet) return;
    const calculator = (fxFieldIds: FieldId[], fnCalcCell: typeof calculateFieldRpn) => {
      fxFieldIds.forEach((fxFieldId) => {
        const collector = new CalcResultCollector(callbackCalcResult);
        const fxField = calcFieldsInSheet.get(fxFieldId);
        const field = sheet.public.getField(fxFieldId);
        if (fxField && field) {
          const calcFn = this.makeCalcFieldRowsEvalFn(fxField, fnCalcCell, collector);
          if (isFilterUpCalcField(fxField)) {
            ensureLookupValuesIndexes(model, fxField);
          }
          const ctx: FxCalcContext = { model, sheet, field, fxCellValueConvertorManager: this.fxCellValueConvertorManager };
          const rows = sheet.rowDB.getAllRows_UnOrdered();
          calcFn(ctx, rows);
          sheet.updateRows(collector.result, OPT_UPDATE_ROW);
          fxField.clearCache();
        }
      });
    };
    const [circularFxFields, basicFxFields] = this.calcFields.getCalcChainInSheet_FormulaOnly(sheetId);
    calculator(circularFxFields, () => cellValueOfFxError_CIRCULAR);
    calculator(basicFxFields, calculateFieldRpn);
  }

  /** 忽略 suspend 状态，强制执行全量计算 */
  forceFullCalc(
    callbackCalcResult: FnCallbackCalcResult = updateResultToRow,
  ) {
    this.makeAllDirty();
    this.recalculate(true, callbackCalcResult);
  }

  getQOCalcChain(options?: QOCalcChainOptions) {
    this.setDirtyRecursively();
    // 根据引用关系取得字段计算顺序
    const [calcChain, circular, refError] = this.calcFields.getAllCalcChain();
    let chain = calcChain;
    if (this.enableSheetFormulaRef && this.enableSplitAst) {
      chain = spiltFormulaNode(calcChain, this.model);
    }
    const normal = options?.compositeFilterUp
      ? qo(this.model, chain)
      : chain;
    return toQOCalcChain({ model: this.model }, normal, circular, refError);
  }

  getFullQOCalcChain(options?: QOCalcChainOptions) {
    this.makeAllDirty();
    // 根据引用关系取得字段计算顺序
    const [calcChain, circular, refError] = this.calcFields.getAllCalcChain();
    let chain = calcChain;
    if (this.enableSheetFormulaRef && this.enableSplitAst) {
      chain = spiltFormulaNode(calcChain, this.model);
    }
    const normal = options?.compositeFilterUp
      ? qo(this.model, chain)
      : chain;
    return toQOCalcChain({ model: this.model }, normal, circular, refError);
  }

  private makeEvalFn(calcField: CalcField, collector: CalcResultCollector) {
    const isFilterUp = isFilterUpCalcField(calcField);
    const isComposite = isCompositeFilterUpCalcField(calcField);
    if (isFilterUp || isComposite) {
      const evalFn = this.makeExternalEvalFn(calcField, collector);
      if (evalFn) {
        return evalFn;
      }
    }

    return isComposite
      ? this.makeEvalFnOfDAG(calcField, calculateCompositeLookup, collector, true)
      : this.makeCalcFieldRowsEvalFn(calcField, calculateFieldRpn, collector, true);
  }

  private makeExternalEvalFn(calcField: FilterUpCalcField | CompositeFilterUpCalcField, collector: CalcResultCollector) {
    const extEngine = this.externalFxEngine;
    if (!extEngine) {
      return;
    }
    return (ctx: FxCalcContext, rows: Row[]) => {
      try {
        const result = extEngine.evaluate(calcField, ctx, rows);

        result?.forEach((r) => collector.collect(r));
        calcField.overwriteDirtyRows([]);
        if (calcField.isVirtual) {
          this.calcFields.getCalcField(calcField.addr)?.overwriteDirtyRows([]);
        }
      } catch (e) {
        this.bizLogger?.error('[PolarsEngine] error', e);
        const evaluate = this.makeCalcFieldRowsEvalFn(calcField, () => cellValueOfFxError_ERROR, collector);
        return evaluate(ctx, rows);
      }
    };
  }

  /**
     * 从重算任务中计算指定 CalcField，并将结果更新到模型。
     * - 参数传入具体计算方法；
     * - 这里不关心引用先后关系；
     */
  private makeCalcFieldRowsEvalFn(
    calcField: CalcField,
    fnCalcCell: typeof calculateFieldRpn,
    collector: CalcResultCollector,
    ensureLokiIndex = false,
  ) {
    return (ctx: FxCalcContext, rows: Row[], expireTime = Infinity) => {
      const { id: fieldId, name: fieldName } = ctx.field;
      const initialRowCount = rows.length;
      const getCellFn = getCellExtractorByField(ctx.field);
      if (ensureLokiIndex && isFilterUpCalcField(calcField)) {
        ensureLookupValuesIndexes(this.model, calcField);
      }
      do {
        if (!rows.length) break;
        const row = rows.pop();
        if (row) {
          const oriVal = getCellFn(row);
          const newVal = fnCalcCell(ctx, calcField, row, this.enableSheetFormulaRef);
          if (isEqualCellValue(newVal, oriVal)) {
            // 如果计算结果和原始值相同，不纳入 changeset 中
            continue;
          }
          // 观察到有计算导致关联字段为空，在这里先做一下拦截，避免用户数据丢失
          // 双向关联只会计算 title，不应该清空单元格。在真正 bug 修复之前需要保留
          if (calcField.type === 'associate' && !isValidAssociationCalcResult(newVal)) {
            this.bizLogger?.warn('[calc-asso-error]', { newVal, oriVal, addr: calcField.addr, baseRow: row });
            continue;
          }
          const result = {
            sheet: ctx.sheet,
            rowId: row.id,
            value: { [fieldId]: newVal },
            origin: { [fieldId]: oriVal },
          };
          // 如果外面传入的方法不是默认的，才需要调用，以便于收集结果
          collector.collect(result);

          // 考虑阻止 formula 字段的数组结果（除了 直接引用其他字段 和 特定函数），先统计数组结果的使用情况
          if (
            calcField.type === 'formula'
            && !FX_ARRAY_RESULT_LOGGED
            && newVal?.dataType === CellDataType.OBJECT && newVal.value
            && Array.isArray(newVal.value.filterFactor) && newVal.value.filterFactor.length > 1
          ) {
            FX_ARRAY_RESULT_LOGGED = true; // 控制上报次数
            logger.info('fx_field_array_result', 'log', { rpn: calcField.rpn });
          }

          // 对于ObjectCellValue，服务端和前端的结果只有在filterFilter不一致的时候上报。
          if (newVal?.dataType === CellDataType.OBJECT && oriVal?.dataType === CellDataType.OBJECT) {
            if (isEqual(newVal.value?.filterFactor, oriVal.value?.filterFactor)) {
              continue;
            }
          }

          collector.collectReconciliation(row.id);
        }
      } while (Date.now() < expireTime);

      debugLog('finished calculating field: %s, %s; %d rows calc-ed', fieldId, fieldName, initialRowCount - rows.length);

      calcField.overwriteDirtyRows(rows.map((r) => r.id));
      if (calcField.isVirtual) {
        this.calcFields.getCalcField(calcField.addr)?.overwriteDirtyRows(rows.map((r) => r.id));
      }
    };
  }

  private makeEvalFnOfDAG(
    calcField: CompositeFilterUpCalcField,
    fnCalcCell: typeof calculateCompositeLookup,
    collector: CalcResultCollector,
    ensureLokiIndex = false,
  ) {
    return (ctx: FxCalcContext, rows: Row[], expireTime = Infinity) => {
      const { id: fieldId, name: fieldName } = ctx.field;
      const initialRowCount = rows.length;
      const isComposite = isCompositeFilterUpCalcField(calcField);
      if (!isComposite) {
        throw new Error('use makeEvalFnOfDAG for composite calcField');
      }
      if (ensureLokiIndex) {
        const sampleField = calcField.getAllBuddyFields()[0];
        ensureLookupValuesIndexes(this.model, sampleField);
      }
      do {
        if (!rows.length) break;
        const row = rows.pop();
        if (row) {
          const newVal = fnCalcCell(ctx, calcField, row, this.enableSheetFormulaRef);

          if (isComposite) {
            const origin: UpdateCells = {};
            const value: UpdateCells = {};
            let changed = false;
            calcField.getAllBuddyFields().forEach((cf, index) => {
              const oriVal = calcField.buddyFieldsExtractors[index](row);
              if (!isEqualCellValue(newVal?.[index], oriVal)) {
                origin[cf.addr.fieldId] = oriVal;
                value[cf.addr.fieldId] = newVal?.[index] ?? null;
                changed = true;
              }
            });
            if (changed) {
              const result = { sheet: ctx.sheet, rowId: row.id, value, origin };
              collector.collect(result);
              // collector.collectReconciliation(row.id);
            }
          }
        }
      } while (Date.now() < expireTime);

      debugLog('finished calculating field: %s, %s; %d rows calc-ed', fieldId, fieldName, initialRowCount - rows.length);

      calcField.overwriteDirtyRows(rows.map((r) => r.id));
      if (calcField.isVirtual) {
        this.calcFields.getCalcField(calcField.addr)?.overwriteDirtyRows(rows.map((r) => r.id));
      }
    };
  }

  /**
   * 计算给定的calc fields
   *  - 同步计算
   *  - 查找引用字段仅支持 externalEvaluate
   *  - 忽略 suspend 状态
   *  - 忽略 dirty 态
   */
  async calcCalcFields(inputCalcFields: CalcField[], callbackCalcResult: FnCallbackCalcResult = updateResultToRow, options: CalcCalcFieldsOptions) {
    const calcFields = qo(this.model, inputCalcFields);
    const { onFieldCalculated } = options || {};

    for (const calcField of calcFields) {
      const collector = new CalcResultCollector(callbackCalcResult);
      const sheet = this.model.getSheet(calcField.addr.sheetId);
      if (!sheet) {
        this.bizLogger?.warn(`[calcCalcFields] error. fail to find the sheet '${calcField.addr.sheetId}'`);
        return;
      }

      let evaluate: Evaluate;
      switch (options.calcChainType) {
        case 'normal':
          evaluate = this.makeEvalFn(calcField, collector);
          break;
        case 'circular':
          evaluate = this.makeCalcFieldRowsEvalFn(calcField, () => cellValueOfFxError_CIRCULAR, collector);
          break;
        case 'refError':
          evaluate = this.makeCalcFieldRowsEvalFn(calcField, () => cellValueOfFxError_REF, collector);
          break;
        default:
          throw new Error(`unsupported calc chain type: ${options.calcChainType}`);
      }

      const start = Date.now();
      evaluateCalcField({
        model: this.model,
        fxCellValueConvertorManager: this.fxCellValueConvertorManager,
        evaluate,
      }, calcField);
      const duration = Date.now() - start;
      if (duration > SLOW_EVALUATION_MS) {
        this.bizLogger?.warn(`[calcCalcFields] SLOW_EVAL [${duration}ms]. calc field: ${JSON.stringify(calcField.addr)} ${calcField.rpn}`);
      }

      await onFieldCalculated?.();

      sheet.updateRows(collector.result, OPT_UPDATE_ROW);
      calcField.clearCache();
    }
  }


  /**
   * 根据标脏进行计算
   * @param force 指定为 true 时忽略 suspend 状态、强制执行计算
   */
  recalculate(
    force = false,
    callbackCalcResult: FnCallbackCalcResult = updateResultToRow,
  ) {
    if (!force && this.isCalculationSuspended()) return;

    this.fxCellValueConvertorManager.init();

    // 如果已经有异步任务，本次不会强制同步计算
    const hasAsyncCalcAlready = this.taskScheduler.hasTasks();

    // 根据「直接标记重算的 CalcFields」，将所有「被影响的 CalcFields」标记重算
    this.setDirtyRecursively();

    // 根据引用关系取得字段计算顺序
    const [calcChain, circular, refError] = this.calcFields.getAllCalcChain();
    const shouldAsyncChecker = getShouldAsyncCalcChecker(this.model);

    const calcNodeNeedCalc = new Set<string>();
    const markPrevNode = (node: CalcField) => {
      const key = `${node.addr.sheetId}_${node.addr.fieldId}`;
      if (calcNodeNeedCalc.has(key)) return;
      calcNodeNeedCalc.add(key);
      node.prev.forEach((prevField) => {
        markPrevNode(prevField);
      });
    };

    if (this.onlyCalcActiveSheet) {
      this.calcSheetIds.forEach((sheetId) => {
        this.calcFields.getCalcFieldsInSheet(sheetId)?.forEach((calcField) => {
          if (calcField.isDirty()) {
            markPrevNode(calcField);
          }
        });
      });
    }

    // 推入任务：循环引用的 CalcField 都置为循环引用错误
    circular.forEach((calcField) => {
      const collector = new CalcResultCollector(callbackCalcResult);
      const sheet = this.model.getSheet(calcField.addr.sheetId);
      if (!sheet || !sheet.hasFullRawData()) return;
      if (calcField.isDirty()) {
        this.taskScheduler.addTask({
          startTime: CALC_INIT_TIMESTAMP,
          calcField,
          isCircular: true,
          evaluate: this.makeCalcFieldRowsEvalFn(calcField, () => cellValueOfFxError_CIRCULAR, collector),
          done() {
            sheet.updateRows(collector.result, OPT_UPDATE_ROW);
            calcField.clearCache();
          },
        });
        shouldAsyncChecker.checkCalcField(calcField);
      }
    });
    refError.forEach((calcField) => {
      const collector = new CalcResultCollector(callbackCalcResult);
      const sheet = this.model.getSheet(calcField.addr.sheetId);
      if (!sheet || !sheet.hasFullRawData()) return;
      if (calcField.isDirty()) {
        this.taskScheduler.addTask({
          startTime: CALC_INIT_TIMESTAMP,
          calcField,
          isCircular: true,
          evaluate: this.makeCalcFieldRowsEvalFn(calcField, () => cellValueOfFxError_REF, collector),
          done() {
            sheet.updateRows(collector.result, OPT_UPDATE_ROW);
            calcField.clearCache();
          },
        });
        shouldAsyncChecker.checkCalcField(calcField);
      }
    });

    const splited = this.enableSplitAst ? spiltFormulaNode(calcChain, this.model) : calcChain;
    const optimizedCalcChain = qo(this.model, splited);

    const nodeIds: string[] = [];
    // 推入任务：计算每个正常的 CalcField
    optimizedCalcChain.forEach((calcField) => {
      const sheet = this.model.getSheet(calcField.addr.sheetId);
      if (!sheet || !sheet.hasFullRawData()) return;
      if (this.onlyCalcActiveSheet && !calcNodeNeedCalc.has(`${calcField.addr.sheetId}_${calcField.addr.fieldId}`)) {
        return;
      }
      if (calcField.isDirty() && refSheetsLoaded(calcField, this.model)) {
        const collector = new CalcResultCollector(callbackCalcResult);
        const evaluate = this.makeEvalFn(calcField, collector);

        nodeIds.push(`${calcField.addr.sheetId}_${calcField.addr.fieldId}`);

        const threshold = this.model.workbook.calcReportThreshold;
        const { model } = this;
        this.taskScheduler.addTask({
          startTime: CALC_INIT_TIMESTAMP,
          calcField,
          isCircular: false,
          evaluate,
          done() {
            sheet.updateRows(collector.result, OPT_UPDATE_ROW);
            calcField.clearCache();
            reportStat(sheet, this, threshold);
            // 触发变更事件
            model.emit(EventType.END_COLUMN_CALC, {
              sheetId: sheet.id,
              column: { id: calcField.addr.fieldId, type: calcField.type },
              changedRecordLength: collector.reconciliation.length,
              sampleIds: collector.reconciliation.slice(0, 30),
            });
          },
        });
        shouldAsyncChecker.checkCalcField(calcField);
      }
    });

    if (this.onlyCalcActiveSheet) {
      dagDebugLog(`${this.calcSheetIds} 触发了 ${nodeIds.length} 个字段计算: ${nodeIds}`);
    }
    const forceSync = !hasAsyncCalcAlready && shouldAsyncChecker.getCalcType() === 'sync';
    this.taskScheduler.start(forceSync);
  }

  hasTask() {
    return this.taskScheduler.hasTasks();
  }

  calculateTempRecordFormulaChangeset(sheetId: string, record: RecordDTO): Record<string, RowCell | null> {
    const sheet = this.model.getSheet(sheetId);
    if (!sheet) return {};
    const [calcChain, circular, refError] = this.calcFields.getAllCalcChain();

    const changeset: Record<string, RowCell | null> = {};
    const cells = record.cells ? { ...record.cells } : {};
    const afterCalcCircularRecord = circular.reduce((acc, calcField) => {
      if (!isEqualCellValue(acc.cells[calcField.addr.fieldId], cellValueOfFxError_CIRCULAR)) {
        changeset[calcField.addr.fieldId] = cellValueOfFxError_CIRCULAR;
      }
      acc.cells[calcField.addr.fieldId] = cellValueOfFxError_CIRCULAR;
      return acc;
    }, { ...record, cells });

    const afterCalcRefErrorRecord = refError.reduce((acc, calcField) => {
      if (!isEqualCellValue(acc.cells[calcField.addr.fieldId], cellValueOfFxError_REF)) {
        changeset[calcField.addr.fieldId] = cellValueOfFxError_REF;
      }
      acc.cells[calcField.addr.fieldId] = cellValueOfFxError_REF;
      return acc;
    }, afterCalcCircularRecord);

    calcChain.reduce((acc, calcField) => {
      if (calcField.addr.sheetId !== sheetId) return acc;
      const field = sheet?.getField(calcField.addr.fieldId);
      if (!field) return acc;
      if (field.type !== 'formula') return acc;
      const ctx: FxCalcContext = { model: this.model, sheet, field, fxCellValueConvertorManager: this.fxCellValueConvertorManager };
      const val = calculateFieldRpn(ctx, calcField, acc, this.enableSheetFormulaRef);

      if (!isEqualCellValue(val, acc.cells[field.id])) {
        changeset[field.id] = val;
      }

      if (val === null) {
        delete acc.cells[field.id];
      } else {
        acc.cells[field.id] = val;
      }

      return acc;
    }, afterCalcRefErrorRecord);

    return changeset;
  }

  private makeAllDirty() {
    this.calcFields.iterAllCalcField((calcField) => {
      calcField.appendDirtyRows(true);
    });
  }

  /**
   * 对某一行内容计算 RPN 格式的公式
   */
  calcRpnOnRow(rpn: FormulaRpn, sheet: SheetModel, field: FieldDTO, row: RecordDTO) {
    const calcField = new FormulaCalcField({ sheetId: sheet.id, fieldId: field.id }, rpn);
    const ctx: FxCalcContext = { model: this.model, sheet, field, fxCellValueConvertorManager: this.fxCellValueConvertorManager };
    return calculateFieldRpn(ctx, calcField, row, this.enableSheetFormulaRef);
  }

  destroy() {
    this.taskScheduler.destroy();
  }
}

/**
 * 上报计算耗时超过阈值（默认 10s） 的慢查询（仅查找引用）
 */
function reportStat(sheet: SheetModel, calcTask: CalcTask, threshold = 10000) {
  const timeMs = Date.now() - calcTask.startTime;
  if (timeMs > threshold) {
    const { calcField } = calcTask;
    const field = sheet.getField(calcField.addr.fieldId);
    if (field && calcField.type === 'lookup' && calcField.valuesSource.type === 'filter') {
      if (field) {
        const content = {
          sheet_id: sheet.id,
          field_id: field.id,
          filters: JSON.stringify(calcField.valuesSource.filters),
          time_ms: timeMs,
        };
        logger.log('fx_calc_stat', content);
      }
    }
  }
}

const refSheetsLoaded = (calcField: CalcField, model: Model) => {
  const refSheetIds = calcField.getAllRefSheetIds();
  return refSheetIds.every((sheetId) => model.getSheet(sheetId)?.hasFullRawData());
};

const shouldAddCellItemFn = (calcField: CalcField) => {
  // 先支持引用字段，公式需要额外增加对 rpn 的解析逻辑
  if (calcField.type !== 'lookup') return false;
  if (calcField.aggregator !== 'VALUES' && calcField.aggregator !== 'UNIQUE') return false;
  if (!calcField.getProps().resultFieldType) return false;
  return true;
};

const getExtraInfo = (cellRefData: ObjectCellValue['refData']) => {
  if (!cellRefData) return undefined;
  if (cellRefData?.value.length > 0) return cellRefData;
  return undefined;
};

const makeGetNameResultByIndexFn = (
  ctx: FxCalcContext,
  calcField: CalcField,
  row: RecordDTO,
  enableSheetFormulaRef: boolean,
): EvaluateCtxForNotable['getNameResultByIndex'] => {
  return (_, refIdx) => {
    const { sheet, fxCellValueConvertorManager, model } = ctx;
    const [refFieldId, refSheetId] = calcField.rpnRefs[refIdx];
    if (refSheetId) {
      if (!enableSheetFormulaRef) {
        return FxValue.makeError('#UNSUPPORT!');
      }
      const currentSheet = model.getSheet(refSheetId);
      if (!currentSheet) {
        return FxValue.makeError('#REF!');
      }
      const refField = currentSheet.fieldMap[refFieldId];
      if ((!refField || refField.deleted) && !calcField.isVirtual) {
        return FxValue.makeError('#REF!');
      }

      const convertor = fxCellValueConvertorManager.getConvertor(refSheetId, refFieldId, calcField.isVirtual);
      if (!convertor) {
        return FxValue.makeError('#REF!');
      }
      return convertor.getFieldNotableArray();
    }
    const refField = sheet.fieldMap[refFieldId];
    if ((!refField || refField.deleted) && !calcField.isVirtual) {
      return FxValue.makeError('#REF!');
    }
    const convertor = fxCellValueConvertorManager.getConvertor(sheet.id, refFieldId, calcField.isVirtual);
    if (enableSheetFormulaRef && convertor) {
      return convertor.getFieldNotableArray(row);
    } else {
      return convertor?.fromCellValue(row) ?? FxValue.makeNullFromRef();
    }
  };
};

/** 计算指定 row 指定 CalcField 的结果。 */
export const calculateFieldRpn = (
  ctx: FxCalcContext,
  calcField: CalcField,
  row: RecordDTO,
  enableSheetFormulaRef: boolean,
): null | RowCell => {
  if (calcField.type === 'associate') {
    return calculateAssoFieldTitles(ctx, calcField, row);
  }

  if (!calcField.rpn) return cellValueOfFxError_ERROR;

  if (calcField.cachedResult !== undefined) {
    return calcField.cachedResult;
  }

  let result: null | RowCell = null;
  try {
    const lookupContext: LookupContext = {
      values: createValueTarget(calcField),
      cache: undefined,
      fxCellValueConvertor: getFxCellValueConvertor(ctx, calcField),
    };

    let cellRefData: ObjectCellValue['refData'];
    const evalCtx = createEvaluateContext(
      calcField.addr.sheetId,
      row.id,
      calcField,
      makeGetNameResultByIndexFn(ctx, calcField, row, enableSheetFormulaRef),
      (id: string) => ctx.model.getSheet(id),
      (cell: RowCell) => {
        if (!cellRefData) {
          cellRefData = {
            dataType: cell.dataType,
            value: [],
          };
        }
        cellRefData.value.push(cell.value);
      },
      () => shouldAddCellItemFn(calcField),
      lookupContext,
    );

    const formulaValue = evaluateRpn(calcField.rpn, evalCtx);
    const getField = (sheetId: string, fieldId: string) => {
      const field = ctx.model.getSheet(sheetId)?.getField(fieldId);
      if (field) return field;
      if (calcField.isVirtual) {
        return makeVirtualFormulaField(fieldId);
      }
      return null;
    };

    result = toCellValue(formulaValue, getField, getExtraInfo(cellRefData), calcField.isVirtual);
  } catch (err) {
    if (!isErrNoAssoRow(err)) {
      result = cellValueOfFxError_ERROR;
    }
  }
  calcField.setCachedResult(result);
  return result;
};

const calculateCompositeLookup = (
  ctx: FxCalcContext,
  compositeCalcField: CompositeFilterUpCalcField,
  row: RecordDTO,
  enableSheetFormulaRef: boolean,
): Array<null | RowCell> => {
  const buddyCalcField = compositeCalcField.getAllBuddyFields();
  let resultCache: JoinResult;

  const allResults = buddyCalcField.map((calcField) => {
    if (!calcField.rpn) return cellValueOfFxError_ERROR;

    if (calcField.cachedResult !== undefined) {
      return calcField.cachedResult;
    }

    let result: null | RowCell = null;
    try {
      const lookupContext: LookupContext = {
        values: createValueTarget(calcField),
        cache: undefined,
        joinResultCache: () => resultCache,
        setResultCache: (c) => { resultCache = c; },
        fxCellValueConvertor: getFxCellValueConvertor(ctx, calcField),
      };

      let cellRefData: ObjectCellValue['refData'];
      const evalCtx = createEvaluateContext(
        calcField.addr.sheetId,
        row.id,
        calcField,
        makeGetNameResultByIndexFn(ctx, calcField, row, enableSheetFormulaRef),
        (id: string) => ctx.model.getSheet(id),
        (cell: RowCell) => {
          if (!cellRefData) {
            cellRefData = {
              dataType: cell.dataType,
              value: [],
            };
          }
          cellRefData.value.push(cell.value);
        },
        () => shouldAddCellItemFn(calcField),
        lookupContext,
      );

      const formulaValue = evaluateRpn(calcField.rpn, evalCtx);
      const getField = (sheetId: string, fieldId: string) => {
        const field = ctx.model.getSheet(sheetId)?.getField(fieldId);
        if (field) return field;
        if (compositeCalcField.isVirtual) {
          return makeVirtualFormulaField(fieldId);
        }
        return null;
      };

      result = toCellValue(formulaValue, getField, getExtraInfo(cellRefData), calcField.isVirtual);
    } catch (err) {
      if (!isErrNoAssoRow(err)) {
        result = cellValueOfFxError_ERROR;
      }
    }
    calcField.setCachedResult(result);
    return result;
  });

  return allResults;
};

const calculateAssoFieldTitles = (
  { model, sheet }: FxCalcContext,
  { addr, targetSheetId, titleFieldId }: AssoCalcField,
  baseRow: RecordDTO,
): null | RowCell => {
  const oldAssoCell = getCellExtractorByField({ id: addr.fieldId, type: 'association' })(baseRow);
  const oldAssoData = getValueOfAssociationsCell(oldAssoCell);
  if (!oldAssoData.length) return null;

  const targetSheet = model.getSheet(targetSheetId);
  if (!targetSheet) return null;

  const titleField = targetSheet.getField(titleFieldId);
  if (!titleField) return null;

  const getTitleCell = getCellExtractorByField(titleField);

  const associatedRows = getAssociatedRows(sheet, baseRow, targetSheet, addr.fieldId);
  const assoRowId2TitleMap = associatedRows.reduce((acc, assoRow) => {
    const titleCell = getTitleCell(assoRow);
    if (titleCell) {
      let title = '';
      if (titleField.type === 'autoNumber' && isAutoNumValue(titleCell.value)) {
        title = AutoNumUtils.getResultWithField(titleField, titleCell.value, assoRow.createdTime);
      } else {
        title = renderAssociationsTitle(titleCell, titleField);
      }
      acc.set(assoRow.id, title);
    }
    return acc;
  }, new Map<RowId, string>());

  // 保持已有关联项不变，仅更新 title
  let foundChange = false;
  const newAssoVal = oldAssoData.map<AssociationsCellValue[number]>((item) => {
    const newTitle = assoRowId2TitleMap.get(item.rowId);
    if (!newTitle || newTitle === item.title) {
      return item;
    }
    foundChange = true;
    return { rowId: item.rowId, title: newTitle };
  });

  return foundChange ? createAssociationsCell(newAssoVal) : oldAssoCell;
};

const createEvaluateContext = (
  sheetId: SheetId,
  rowId: RowId,
  baseField: CalcField,
  getNameResultByIndex: EvaluateCtxForNotable['getNameResultByIndex'],
  getSheet: FnGetSheet,
  addCellItem: EvaluateCtxForNotable['addCellItem'],
  shouldAddCellItem: EvaluateCtxForNotable['shouldAddCellItem'],
  lookupContext: LookupContext,
): EvaluateCtxForNotable => {
  let currentValueContext: CurrentValueContext | undefined;

  return {
    sheetId,
    rowId,
    baseField,
    getJoinResult,
    getLookupValues,
    getLookupUniqueValues,
    getNameResultByIndex,
    getSheet,
    acceptArray: baseField.acceptArrayResult,
    row: 0,
    col: 0,
    addCellItem,
    shouldAddCellItem,
    lookupContext,
    setCurrentValueContext: (v) => { currentValueContext = v; },
    getCurrentValueContext: () => currentValueContext,
    getNotableArrayByCurrentValue: (ctx) => {
      const { fieldId: refFieldId, sheetId: refSheetId, rowId: refRowId } = ctx;
      if (!refFieldId || !refRowId || !refSheetId) return FxValue.makeError('#REF!');
      const refSheet = getSheet(refSheetId);
      if (!refSheet) return FxValue.makeError('#REF!');
      const refField = refSheet.fieldMap[refFieldId];
      // 如果 baseField 是virtual，不检查 refField 是否存在。后面的 converter 可以正常取到 virtualField 的结果
      if ((!refField || refField.deleted) && !baseField.isVirtual) {
        return FxValue.makeError('#REF!');
      }
      const convertor = refSheet.parent.fxCellValueConvertorManager.getConvertor(refSheetId, refFieldId, baseField.isVirtual);
      if (!convertor) {
        return FxValue.makeError('#REF!');
      }
      return convertor.getFieldNotableArray(refSheet.getRow(refRowId));
    },
  };
};

const makeMismatchTypeError = () => {
  const constArr: NotableConstArray = [[{ type: 'ERROR', value: { type: '#MISMATCH_TYPE' } }]];
  return FxValue.makeConstArray(constArr as ConstArray);
};

const checkLookupError = (
  ctx: Pick<EvaluateCtxForNotable, 'sheetId' | 'baseField' | 'getSheet'>,
  baseSheet: SheetModel,
  valuesField: IModelField,
  getField: GetFieldFunc,
) => {
  const baseFieldConfig = (ctx.baseField as LookupCalcField).getProps();
  const { aggregator, resultFieldType } = baseFieldConfig;
  if (resultFieldType && (aggregator === 'VALUES' || aggregator === 'UNIQUE')) {
    // 原值
    if (isCalcField(valuesField.type)) {
      const { valuesField: calcValuesField } = getCalcValuesField(baseSheet.id, baseFieldConfig, getField) || {};
      if (calcValuesField && resultFieldType !== calcValuesField.type) {
        return makeMismatchTypeError();
      }
    } else if (resultFieldType !== valuesField.type) {
      return makeMismatchTypeError();
    }
  }
  return null;
};

const checkLookupParams = (
  ctx: Pick<EvaluateCtxForNotable, 'sheetId' | 'baseField' | 'getSheet'>,
  lookupContext: LookupContext,
) => {
  const { values: valuesTarget } = lookupContext;
  if (!valuesTarget) return null;
  const { valuesField: valuesAddr } = valuesTarget;
  const baseSheet = ctx.getSheet(ctx.sheetId);
  if (!baseSheet) return null;
  const valuesSheet = ctx.getSheet(valuesAddr.sheetId);
  const valuesField = valuesSheet?.getField(valuesAddr.fieldId);
  if (!valuesSheet || !valuesField || valuesField.deleted) return null;
  return { baseSheet, valuesSheet, valuesField };
};

/** 根据 LookupField 配置，查询指定 row 下的 VALUES 结果。 */
const getJoinResult: HookGetJoinResult = (
  ctx,
  rowId,
  lookupContext: LookupContext,
) => {
  const resultNull = FxValue.makeNullFromRef();
  const res = checkLookupParams(ctx, lookupContext);
  if (!res) return resultNull;
  const { baseSheet, valuesSheet } = res;

  let valuesRows: RecordDTO[] = [];
  const calcField = ctx.baseField;
  if (isLookupAssoCalcField(calcField)) {
    const baseRow = baseSheet.getRow(rowId);
    if (baseRow) {
      valuesRows = getAssociatedRows(
        baseSheet, baseRow, valuesSheet,
        calcField.valuesSource.associateField,
      );
    }
    // 无关联记录时，结果显示为空 —— 抛出错误、结束计算
    if (!valuesRows.length) throw ERR_NO_ASSO_ROW;
  } else if (isFilterUpCalcField(calcField) || isCompositeFilterUpCalcField(calcField)) {
    const baseRow = baseSheet.getRow(rowId);
    const shouldSortValues = calcField.shouldSortResult?.();
    valuesRows = getFilteredRows(
      baseSheet, baseRow, valuesSheet,
      calcField.reorderedFilters(), shouldSortValues,
    );
  } else if (calcField.type === 'formula') {
    const baseRow = baseSheet.getRow(rowId);
    if (lookupContext.values?.valuesSource.type === 'filter') {
      valuesRows = getFilteredRows(
        baseSheet, baseRow, valuesSheet,
        lookupContext.values.valuesSource.filters, true,
      );
    }
  }

  if (!valuesRows.length) {
    return resultNull;
  }

  // 直接传递引用，避免过多的转换，消费的地方搜索【避免过多的转换】
  return FxValue.makeConstArray(valuesRows as unknown as ConstArray, valuesRows.length);
};

/** 根据 LookupField 配置，查询指定 row 下的 VALUES 结果。 */
const getLookupValues: HookGetLookupValues = (
  ctx,
  rowId,
  lookupContext,
  matchedRecords,
  forceAddCellItem = false,
) => {
  const resultNull = FxValue.makeNullFromRef();
  const res = checkLookupParams(ctx, lookupContext);
  if (!res) return resultNull;
  const { baseSheet, valuesField } = res;

  const getField = (sheetId: string, fieldId: string) => ctx.getSheet(sheetId)?.getField(fieldId) ?? null;

  const error = checkLookupError(ctx, baseSheet, valuesField, getField);
  if (error) return error;

  const valuesRows = matchedRecords || [];
  const { fxCellValueConvertor } = lookupContext;

  const shouldAddCellItem = forceAddCellItem || ctx.shouldAddCellItem();
  const values: ConstArrayItem[] = [];

  for (let i = 0; i < valuesRows.length; i++) {
    const row = valuesRows[i];

    const val = row && fxCellValueConvertor?.fromCellValue(row);

    if (val != null) {
      if (shouldAddCellItem) {
        const cell = fxCellValueConvertor?.extractCellValue(row);
        if (cell) {
          ctx.addCellItem(cell);
        }
      }
      if (FxValue.isConstArray(val)) {
        FxValue.iterateConstArray(val.value, (item) => {
          values.push(item);
        });
      } else if (FxValue.isNotableArray(val)) {
        FxValue.iterateNotableArray(val.value, (item) => {
          values.push(item.getConstValue());
        });
      } else {
        values.push(val);
      }
    }
  }
  // VALUES agg 忽略 FxError
  const valuesNoFxErr = values.filter((val) => !FxValue.isError(val));
  if (valuesNoFxErr.length) {
    // 转换为 多行单列的 ConstArray 进入下一步计算
    return FxValue.makeConstArray(valuesNoFxErr.map((vals) => ([vals])), valuesRows.length);
  }
  return resultNull;
};

const getLookupUniqueValues: HookGetLookupValues = (
  ctx,
  rowId,
  lookupContext,
  matchedRecords,
) => {
  const resultNull = FxValue.makeNullFromRef();
  const res = checkLookupParams(ctx, lookupContext);
  if (!res) return resultNull;
  const { baseSheet, valuesField } = res;

  const getField = (sheetId: string, fieldId: string) => ctx.getSheet(sheetId)?.getField(fieldId) ?? null;

  const error = checkLookupError(ctx, baseSheet, valuesField, getField);
  if (error) return error;

  const valuesRows = matchedRecords || [];
  const { fxCellValueConvertor } = lookupContext;

  const [values, cells] = uniqueValuesByRows(valuesRows, valuesField, fxCellValueConvertor);

  if (cells && ctx.shouldAddCellItem()) {
    cells.forEach((cell) => {
      ctx.addCellItem(cell);
    });
  }

  if (values.length) {
    // 转换为 多行单列的 ConstArray 进入下一步计算
    return FxValue.makeConstArray(values.map((vals) => ([vals])), valuesRows.length);
  }

  return resultNull;
};

const getAssociatedRows = (
  baseSheet: SheetModel,
  baseRow: RecordDTO,
  associateSheet: SheetModel,
  associateFieldId: FieldId,
): RecordDTO[] => {
  const assoField = baseSheet.getField(associateFieldId);
  if (!assoField || assoField.deleted) return [];

  const associateCell = baseRow.cells?.[associateFieldId];
  const result = getValueOfAssociationsCell(associateCell).map((i) => i.rowId);
  if (result.length) {
    return result.reduce<RecordDTO[]>((acc, rowId) => {
      const row = associateSheet.getRow(rowId);
      if (row) {
        acc.push(row);
      }
      return acc;
    }, []);
  }
  return [];
};

const createValueTarget = (field: CalcField): undefined | ValuesTarget => {
  if (field.type === 'lookup' && field.valuesField) {
    const { valuesSource, valuesField } = field;
    return { valuesSource, valuesField };
  }
};

const CreateOrUpdateFields: string[] = [
  WidgetTypeEnums.creator,
  WidgetTypeEnums.createdTime,
  WidgetTypeEnums.updater,
  WidgetTypeEnums.updatedTime,
];
const isCreateOrUpdateFields = (fieldType: unknown) => (
  typeof fieldType === 'string'
  && CreateOrUpdateFields.includes(fieldType)
);


const ASYNC_CALC_DIRTY_THRESHOLD = 2000;

const getShouldAsyncCalcChecker = (model: Model) => {
  let shouldAsync = false;
  let countOfCellToCalc = 0;

  return {
    checkCalcField({ addr, dirtyRows }: CalcField) {
      if (shouldAsync) return;

      if (dirtyRows) {
        const dirtyCount = dirtyRows === true
          ? model.getSheet(addr.sheetId)?.getRowCount()
          : dirtyRows.size;
        if (dirtyCount) {
          countOfCellToCalc += dirtyCount;
          if (countOfCellToCalc > ASYNC_CALC_DIRTY_THRESHOLD) {
            shouldAsync = true;
          }
        }
      }
    },
    /**
     * @returns 'none' - 没有待计算项；'sync' - 计算量少、可以同步计算；`async` - 建议异步计算
     */
    getCalcType() {
      if (countOfCellToCalc > 0) {
        return shouldAsync ? 'async' : 'sync';
      }
      return 'none';
    },
  };
};

const makeDealImpactedCalcFieldFn = (
  dirtyField: CalcField,
  model: Pick<Model, 'getSheet'>,
  lookupDirtyMode: 'associated-records' | 'field',
) => {
  const { dirtyRows } = dirtyField;
  if (!dirtyRows) return;

  if (dirtyRows === true) {
    return (impacted: CalcField) => {
      impacted.overwriteDirtyRows(true);
      return true;
    };
  }

  return (impacted: CalcField) => {
    // - formula-field 被影响，formula 相同行标脏
    if (impacted.type === 'formula') {
      return impacted.appendDirtyRows(dirtyRows);
    }
    if (impacted.type !== 'lookup') return;

    // 是 filter-up-field 被影响
    if (isFilterUpCalcField(impacted)) {
      const { addr } = dirtyField;
      if (impacted.isFieldsReferencedAsConditionOrValues(addr.sheetId, [addr.fieldId])) {
        // 被影响的是 查找条件的条件字段 或 ValuesField，直接标脏
        return impacted.appendDirtyRows(true);
      }
      if (impacted.isFieldsReferencedAsConditionsValue(addr.sheetId, [addr.fieldId])) {
        // 影响的是 查找条件的条件值部分，相同行标脏
        return impacted.appendDirtyRows(dirtyRows);
      }
      return;
    }

    // 是 lookup-field 被影响
    if (isLookupAssoCalcField(impacted)) {
      const {
        addr: { sheetId: sheet1 },
        valuesSource: { associateField: asso1 },
      } = impacted;

      // 按具体关联项标脏
      if (lookupDirtyMode === 'associated-records') {
        const sheet2 = model.getSheet(dirtyField.addr.sheetId);
        let foundNextDirty = false;

        findAssoRowsIn1_ByAsso1AndRowsIn2(dirtyRows, sheet2, asso1, sheet1, (dirtyRowsOfLookupA) => {
          if (impacted.appendDirtyRows(dirtyRowsOfLookupA)) {
            foundNextDirty = true;
          }
        });

        return foundNextDirty;
      }
      // 整列标脏
      return impacted.appendDirtyRows(true);
    }
  };
};

function hasInternalDependency(targetNodes: CalcField[]): boolean {
  const ids = new Set<string>(targetNodes.map((node) => node.addr.fieldId));
  const visit = (node: CalcField) => {
    return Array.from(node.prev.values()).some((n) => {
      if (ids.has(n.addr.fieldId)) {
        return true;
      } else {
        return visit(n);
      }
    });
  };
  return targetNodes.some(visit);
}

const mergeCalcField = (ctx: Model, group: FilterUpCalcField[]) => {
  const sample = group[0];

  const compositeField = new CompositeFilterUpCalcField(
    sample.addr,
    sample.valuesSource,
    sample.valuesField,
    sample.aggregator,
    sample.resultFieldType,
    sample.formatter,
    sample.valuesVersion,
  );
  compositeField.isVirtual = sample.isVirtual;
  group.forEach((calcField) => {
    compositeField.addIdenticalBuddyField(calcField);
    let field: FieldDTO | undefined = ctx.getSheet(calcField.addr.sheetId)?.getField(calcField.addr.fieldId);
    if (calcField.isVirtual) {
      field = makeVirtualFormulaField(calcField.addr.fieldId);
    }
    if (!field) {
      return;
    }
    compositeField.buddyFieldsExtractors.push(getCellExtractorByField(field));
    calcField.prev.forEach((p) => {
      compositeField.prev.add(p);
      p.next.delete(calcField);
      p.next.add(compositeField);
    });
    calcField.next.forEach((p) => {
      compositeField.next.add(p);
      p.prev.delete(calcField);
      p.prev.add(compositeField);
    });
  });

  return compositeField;
};

function spiltFormulaNode(calcChainWithoutError: CalcField[], model: Model): CalcField[] {
  const splitedCalcChain: CalcField[] = [];
  calcChainWithoutError.forEach((calcNode) => {
    if (calcNode.type === 'formula' && calcNode.rpnRefs.some((ref) => !!ref[1])) {
      // 判断包含整列引用，才开始转换
      const splited = splitFormulaCalcField(calcNode, model);
      splitedCalcChain.push(...splited);
    } else {
      splitedCalcChain.push(calcNode);
    }
  });

  // 产生了新的node，重建构建一次依赖关系
  const calcFieldMap: NestedMap2<SheetId, FieldId, CalcField> = new NestedMap2();
  splitedCalcChain.forEach((f) => {
    calcFieldMap.addItem(f.addr.sheetId, f.addr.fieldId, f);
  });
  splitedCalcChain.forEach((f) => f.buildRelation(calcFieldMap));

  const [normal] = topoSortCalcChain(splitedCalcChain);
  return normal;
}

function qo(ctx: Model, calcChainWithoutError: CalcField[]): CalcField[] {
  // const graph = printCalcChainInMermaidFormat(ctx, calcChainWithoutError);
  // console.log(graph);
  // 一个可行但不严谨的优化
  const grouped = groupBy(
    calcChainWithoutError,
    (calcField) => {
      if (!calcField.isDirty()) {
        return '';
      }
      if (calcField.type !== 'lookup') {
        return '';
      }
      if (calcField.valuesSource.type !== 'filter') {
        return '';
      }
      return `${calcField.addr.sheetId}-${calcField.valuesField.sheetId}-${JSON.stringify(calcField.valuesSource.filters)}`;
    },
  );

  const groupedKeys = Object.keys(grouped);

  if (groupedKeys.length < 2) {
    return calcChainWithoutError;
  }

  const qoMeta = groupedKeys.reduce((m, k) => {
    m.push({ key: k, count: grouped[k].length, canMerge: !hasInternalDependency(grouped[k]) });
    return m;
  }, [] as Array<{ key: string; count: number; canMerge: boolean }>);

  const shouldMerge = qoMeta
    .filter((m) => m.key)
    .filter((m) => m.canMerge)
    .filter((m) => m.count > 1);

  if (!shouldMerge.length) {
    return calcChainWithoutError;
  }

  const compositeFields = shouldMerge.map((m) => mergeCalcField(ctx, grouped[m.key] as FilterUpCalcField[]));

  const shouldRemovedFilterUpFields = shouldMerge.reduce((shouldRemoved, { key }) => {
    grouped[key].forEach((c) => shouldRemoved.add(`${c.addr.sheetId}_${c.addr.fieldId}`));
    return shouldRemoved;
  }, new Set<string>());

  const newCalcChain = calcChainWithoutError.filter((c) => !shouldRemovedFilterUpFields.has(`${c.addr.sheetId}_${c.addr.fieldId}`)).concat(compositeFields);

  if (newCalcChain.length === calcChainWithoutError.length) {
    return calcChainWithoutError;
  }

  const [normal, circular] = topoSortCalcChain(newCalcChain);

  if (circular?.length) {
    return calcChainWithoutError;
  }

  return normal;
}
