import { CellDataType, FxRefData, generateFieldId, RowCell } from '@ali/notable-common';
import { formulaToRpn, makeGetFieldIdByName } from '../../../../formula-parser';
import { Model } from '../../../../../model';
import cpWith<PERSON>llField from '../../../../formula-parser/__tests__/data/cpWithAllField.json';
import { initFuncsForNotable } from '../../../../functions';
import { FormulaEngine } from '../../../../formula-engine';

type DeepPartial<T> = Partial<{ [P in keyof T]: DeepPartial<T[P]> }>;

const SHEET_ID = 'hERWDMS';
const SINGLE_SELECT_FIELD_ID = 'mHe1U1b';
const MULTI_SELECT_FIELD_ID = 'Qo4Fh46';
const PERSON_FIELD_ID = '9XFXz5d';
const DATE_FIELD_ID = 'mKUEya0';
const NUMBER_FIELD_ID = 'cog1ki7';
const TEXT_FIELD_ID = '01ZM8y7';
const ASSO_FIELD_ID = 'hBhe3TE';

describe('detect-fx-result-field-type', () => {
  let model: Model = new Model();
  initFuncsForNotable(true);

  beforeEach(() => {
    model = Model.createFromJSON(cpWithAllField as any, { keepExistingRows: true, usingInMemoryDB: true });
    model.setFxEngine(new FormulaEngine(model, { asyncCalc: false, enableIncrementalCalc: 'manual', skipInitialFullCalc: true, enableSheetFormulaRef: true, enableSplitAst: true }));
  });

  const makeRef = (fieldId: string, sheetId?: string) => {
    if (sheetId) {
      const sheet = model.getSheet(sheetId);
      const field = sheet?.getField(fieldId);
      return `[${sheet?.name}].[${field?.name}]`;
    }

    const sheet = model.getSheet(SHEET_ID);
    const field = sheet?.getField(fieldId);
    return `[${field?.name}]`;
  };

  const testFieldResult = (fieldId: string, result: Array<DeepPartial<RowCell> | undefined>, matchAll = false) => {
    model.fxEngine?.forceFullCalc();
    const rows = model.getSheet(SHEET_ID)?.getAllRows_Ordered();
    rows?.forEach((r, index) => {
      if (result[index] === undefined) {
        if (matchAll) {
          expect(r.cells?.[fieldId]).toBeUndefined();
        }
        return;
      }
      expect(r.cells?.[fieldId]).toMatchObject(result[index] as any);
    });
  };

  const insertField = (formulaStr: string, fieldId = generateFieldId()) => {
    const op = model.getSheet(SHEET_ID)?.public.utils.insertFormulaFieldOP(
      fieldId,
      {
        name: 'test',
        config: {
          renderFieldConfig: {
            props: {
              formula: formulaToRpn(formulaStr, makeGetFieldIdByName(model, SHEET_ID)).formula ?? '',
            }
          }
        },
      },
      undefined,
      false,
    );

    op && model.apply(op);
    return fieldId;
  }

  const testFormula = (formulaStr: string, result: Array<DeepPartial<RowCell> | undefined>, matchAll = false) => {
    const fieldId = insertField(formulaStr);

    // 打开拆分AST算一遍
    model.fxEngine?.toggleSplitAst(true);
    testFieldResult(fieldId, result, matchAll);

    // 关闭拆分AST再算一遍
    model.fxEngine?.toggleSplitAst(false);
    testFieldResult(fieldId, result, matchAll);
  };

  const testOriginValue = (formulaStr: string, result: Array<DeepPartial<FxRefData['value']> | undefined>, matchAll = false) => {
    const originResult = result.map((r) => ({ value: { refData: { value: r } } }));
    testFormula(formulaStr, originResult, matchAll);
  };

  test('简单引用#返回引用的字段类型', () => {
    testOriginValue(`=${makeRef(SINGLE_SELECT_FIELD_ID, SHEET_ID)}`, [[
      { data: 'f7cf3493-835a-41aa-b9aa-8e41f2c14261' },
      { data: 'b8c1f338-aefd-4755-a088-7ddaa9eb9e6f' },
    ]]);

    testOriginValue(`=${makeRef(SINGLE_SELECT_FIELD_ID)}`, [
      [{ data: 'f7cf3493-835a-41aa-b9aa-8e41f2c14261' }],
      [{ data: 'b8c1f338-aefd-4755-a088-7ddaa9eb9e6f' }],
    ]);

    testOriginValue(`=${makeRef(DATE_FIELD_ID, SHEET_ID)}`, [[
      1745769600000,
      1743436800000,
    ]]);

    testOriginValue(`=${makeRef(DATE_FIELD_ID)}`, [
      [1745769600000],
      [1743436800000],
    ]);
  });

  test('包一个SUM/COUNT#固定返回number', () => {
    testFormula(`=SUM(${makeRef(SINGLE_SELECT_FIELD_ID, SHEET_ID)})`, [
      { value: '0' },
      { value: '0' },
    ]);

    testFormula(`=SUM(${makeRef(SINGLE_SELECT_FIELD_ID)})`, [
      { value: '0' },
      { value: '0' },
    ]);

    testFormula(`=COUNT(${makeRef(DATE_FIELD_ID, SHEET_ID)})`, [
      { value: '2' },
      { value: '2' },
    ]);

    testFormula(`=COUNT(${makeRef(DATE_FIELD_ID)})`, [
      { value: '1' },
      { value: '1' },
    ]);
  });

  test('四则运算#固定返回number', () => {
    testFormula(`=${makeRef(DATE_FIELD_ID)}+${makeRef(DATE_FIELD_ID)}`, [
      { value: '91550' },
      { value: '91496' },
    ]);

    testFormula(`=${makeRef(DATE_FIELD_ID)}/2`, [
      { value: '22887.5' },
      { value: '22874' },
    ]);
  });

  // test('date和number间的特殊四则运算#返回date', () => {
  //   // date + number => date
  //   testOriginData(`=${makeRef(DATE_FIELD_ID)}+${makeRef(NUMBER_FIELD_ID)}`, [
  //     [1745769600000],
  //     [1743436800000],
  //   ]);

  //   // number + date => date
  //   testOriginData(`=${makeRef(DATE_FIELD_ID)}+${makeRef(NUMBER_FIELD_ID)}`, [
  //     [1745769600000],
  //     [1743436800000],
  //   ]);
  //   // date + date => number
  //   testOriginData(`=${makeRef(DATE_FIELD_ID)}+${makeRef(NUMBER_FIELD_ID)}`, [
  //     [1745769600000],
  //     [1743436800000],
  //   ]);
  //   // date * number => number
  //   testOriginData(`=${makeRef(DATE_FIELD_ID)}+${makeRef(NUMBER_FIELD_ID)}`, [
  //     [1745769600000],
  //     [1743436800000],
  //   ]);
  // });

  test('包一个UNIQUE#返回引用的字段类型', () => {
    testOriginValue(`=UNIQUE(${makeRef(MULTI_SELECT_FIELD_ID, SHEET_ID)})`, [[
      { data: `["f7cf3493-835a-41aa-b9aa-8e41f2c14261"]` },
      { data: `["b8c1f338-aefd-4755-a088-7ddaa9eb9e6f"]` },
    ]]);

    testOriginValue(`=UNIQUE(${makeRef(MULTI_SELECT_FIELD_ID, SHEET_ID)},${makeRef(MULTI_SELECT_FIELD_ID, SHEET_ID)})`, [[
      { data: `["f7cf3493-835a-41aa-b9aa-8e41f2c14261"]` },
      { data: `["b8c1f338-aefd-4755-a088-7ddaa9eb9e6f"]` },
    ]]);

    // 两边类型不一致，失去原值
    testFormula(`=UNIQUE(${makeRef(SINGLE_SELECT_FIELD_ID, SHEET_ID)},${makeRef(MULTI_SELECT_FIELD_ID, SHEET_ID)})`, [
      { value: { data: `["选项一","选项二"]` } },
      { value: { data: `["选项一","选项二"]` } },
    ]);

    testFormula(`=UNIQUE(${makeRef(DATE_FIELD_ID, SHEET_ID)},${makeRef(MULTI_SELECT_FIELD_ID, SHEET_ID)})`, [
      { value: { data: `[45775,45748,"选项一","选项二"]` } },
      { value: { data: `[45775,45748,"选项一","选项二"]` } },
    ]);
  });

  test('包一个FILTER#返回引用的字段类型', () => {
    testOriginValue(`=FILTER(${makeRef(MULTI_SELECT_FIELD_ID, SHEET_ID)},currentValue="选项一")`, [[
      { data: `["f7cf3493-835a-41aa-b9aa-8e41f2c14261"]` },
      { data: `["f7cf3493-835a-41aa-b9aa-8e41f2c14261"]` },
    ]]);
  });

  test('UNIQUE和FILTER混合#返回引用的字段类型', () => {
    testOriginValue(`=UNIQUE(FILTER(${makeRef(MULTI_SELECT_FIELD_ID, SHEET_ID)},currentValue="选项一"))`, [[
      { data: `["f7cf3493-835a-41aa-b9aa-8e41f2c14261"]` },
    ]]);

    testOriginValue(`=FILTER(UNIQUE(${makeRef(MULTI_SELECT_FIELD_ID, SHEET_ID)}),currentValue="选项一")`, [[
      { data: `["f7cf3493-835a-41aa-b9aa-8e41f2c14261"]` },
    ]]);
  });

  test('LOOKUP#返回第3个参数的字段类型', () => {
    testOriginValue(`=LOOKUP("选项一",${makeRef(TEXT_FIELD_ID, SHEET_ID)},${makeRef(MULTI_SELECT_FIELD_ID, SHEET_ID)})`, [[
      { data: `["f7cf3493-835a-41aa-b9aa-8e41f2c14261"]` },
    ]]);

    testOriginValue(`=LOOKUP("选项二",${makeRef(TEXT_FIELD_ID, SHEET_ID)},${makeRef(MULTI_SELECT_FIELD_ID, SHEET_ID)})`, [[
      { data: `["f7cf3493-835a-41aa-b9aa-8e41f2c14261","b8c1f338-aefd-4755-a088-7ddaa9eb9e6f"]` },
    ]]);

    testOriginValue(`=LOOKUP("选项一",${makeRef(MULTI_SELECT_FIELD_ID, SHEET_ID)},${makeRef(DATE_FIELD_ID, SHEET_ID)})`, [[
      1745769600000,
      1743436800000,
    ]]);

    testOriginValue(`=LOOKUP("选项一",${makeRef(MULTI_SELECT_FIELD_ID, SHEET_ID)},${makeRef(DATE_FIELD_ID, SHEET_ID)},0)`, [[
      1745769600000,
    ]]);
  });

  test('LOOKUP+UNIQUE', () => {
    testOriginValue(`=UNIQUE(LOOKUP("选项一",${makeRef(TEXT_FIELD_ID, SHEET_ID)},${makeRef(MULTI_SELECT_FIELD_ID, SHEET_ID)}))`, [[
      { data: `["f7cf3493-835a-41aa-b9aa-8e41f2c14261"]` },
    ]]);

    testOriginValue(`=UNIQUE(LOOKUP("选项一",${makeRef(MULTI_SELECT_FIELD_ID, SHEET_ID)},${makeRef(DATE_FIELD_ID, SHEET_ID)}))`, [[
      1745769600000,
      1743436800000,
    ]]);
  });

  test('IF特判', () => {
    // 2、3参数类型不一致，结果非原值
    testFormula(`=IF(${makeRef(NUMBER_FIELD_ID)}>50,${makeRef(PERSON_FIELD_ID, SHEET_ID)},${makeRef(SINGLE_SELECT_FIELD_ID, SHEET_ID)})`, [
      { value: { data: `["选项一","选项二"]` } },
      { value: { data: `["钉三多","淘小二"]` } },
    ]);

    // 2、3参数类型一致，是原值
    testOriginValue(`=IF(${makeRef(NUMBER_FIELD_ID)}>50,${makeRef(PERSON_FIELD_ID, SHEET_ID)},${makeRef(PERSON_FIELD_ID, SHEET_ID)})`, [[
      { identifier: 'MOCK10000' },
      { identifier: 'MOCK10001' },
    ]]);

    // 其中一个参数为空，取另外一个参数的原值
    testFormula(`=IF(${makeRef(NUMBER_FIELD_ID)}>50,${makeRef(PERSON_FIELD_ID, SHEET_ID)},"")`, [
      { value: '' },
      { value: { data: `["钉三多","淘小二"]`, refData: { value: [{ identifier: 'MOCK10000' }, { identifier: 'MOCK10001' }] } } },
    ]);

    testFormula(`=IF(${makeRef(NUMBER_FIELD_ID)}>50,"",${makeRef(PERSON_FIELD_ID, SHEET_ID)})`, [
      { value: { data: `["钉三多","淘小二"]`, refData: { value: [{ identifier: 'MOCK10000' }, { identifier: 'MOCK10001' }] } } },
      { value: '' },
    ]);

    testFormula(`=IF(${makeRef(NUMBER_FIELD_ID)}>50,,${makeRef(PERSON_FIELD_ID, SHEET_ID)})`, [
      { value: { data: `["钉三多","淘小二"]`, refData: { value: [{ identifier: 'MOCK10000' }, { identifier: 'MOCK10001' }] } } },
      undefined,
    ]);
  });

  test('TEXTJOIN', () => {
    testFormula(`=TEXTJOIN(";;",true,${makeRef(SINGLE_SELECT_FIELD_ID, SHEET_ID)})`, [
      { value: "选项一;;选项二" },
    ]);
  });

  test('association', () => {
    testFormula(`=${makeRef(ASSO_FIELD_ID)}&"abc"`, [
      { value: "选项一abc" },
      { value: { data: "[\"选项一abc\",\"选项二abc\"]" } },
    ]);
  });
});
