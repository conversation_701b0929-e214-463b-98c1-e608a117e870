/**
 * @fileoverview 模型层 OP 定义
 * 三大类 OP： meta、rawData、local
 */
import {
  AggregateAction,
  CellDataType,
  Decorator,
  FeatAction,
  FieldTypes,
  LocalOperationActon,
  MetaOperationAction,
  OperationTarget,
  RawDataOperationAction,
  RecordWithHierarchyInfo,
  SheetType,
  FieldDecoratorExtensionType,
  type CellVisibility,
  type FoundResult,
  ConditionalFormat,
  AutoNumCell,
  AutoNumValue,
} from '@ali/notable-common';
import type {
  AggregateResult,
  Filter,
  Group,
  GroupResult,
  ViewDTO,
  Sort,
  ViewId,
} from '../view/types';
import type { FieldId, FieldDTO } from '../field/types';
import type { SheetDTO, SheetId, SheetRecordConfig, SheetFieldGroupsConfig } from '../sheet/types';
import type { Cells, RecordDTO, ObjectCellValue, PreRowId, RowId, RecordId, UpdateCells } from '../row/types';
import { NSheet } from '../nsheet/types';
import type { FeatOperation, UpdateAppLockOp, UpdateDevMetaOp, UpdateDocumentOp, UpdatePartitionOp, UpdateWorkflowOP } from '../feats';
import { UpdateSchemaVersionOp } from '../feats/schemaVersion/type';

/**
 * 产生OP的用户原始意图
 *  * formSubmit: 表单提交
 *  * formRevision: 表单二次编辑
 *  * fieldUpdate: 字段更新
 */
export type OpIntent = 'formSubmit' | 'formRevision' | 'fieldUpdate';

/**
 * OP 来源
 *  - action: 自动化中的执行动作产生的OP
 *  - setup: 初始化脚本产生的OP
 *  - form: 表单提交
 *  - sync: 数据同步
 *  - import: 导入
 *  - fldDec: 字段模版 field decorator
 *  - rest: OpenAPI，不包含酷应用
 */
export type OpSource = 'action' | 'setup' | 'form' | 'sync' | 'import' | 'fldDec' | 'rest';

export interface BasePayload {
  /** 任何 OP 都需要携带时间戳 */
  timestamp: number;

  /**
   * 标识OP来源
   */
  source?: OpSource;

  /**
   * 标识产生OP的原始意图
   */
  intent?: OpIntent;
}

/**
 * Update OP Payload 泛型
 * 返回结构为
 * (Extra & {paths: Config[K][0]; value: Config[K][1]; origin: Config[K][1]})
 * | (Extra & {paths: Config[K][0]; value: Config[K][1]; origin: Config[K][1]})
 * | ...
 * paths 更新路径，如期望更新 workbook.name 的值，那 paths 就是 ['name'];
 * value 跟新的值
 * origin 原始值
 */
export type GenerateUpdatePayload<
  Extra extends {},
  Config extends Record<string, [Array<string | number>, unknown]>
  > = Config extends Record<infer Keys, unknown> ? {
    [K in Keys]: Extra & {
      paths: Config[K][0];
      value: Config[K][1];
      origin: Config[K][1];
    }
  }[Keys] : never;

export type UpdateWorkbookPayload = BasePayload & GenerateUpdatePayload<{}, {
  metaName: [['name'], string];
}>;

export interface InsertNSheetPayload extends BasePayload {
  index: number;
  value: NSheet;
}

export interface MoveNSheetPayload extends BasePayload {
  id: string;
  index: number;
  target: number;
}

export interface DeleteNSheetPayload extends BasePayload {
  id: string;
  index: number;
  origin: NSheet;
}

export interface UpdateNSheetPayload extends BasePayload {
  id: string;
  paths: 'name' | 'parentId';
  value: string | null;
  origin: string | null;
}

export interface InsertSheetPayload extends BasePayload {
  index: number;
  value: SheetDTO;
  /**
   * insertSheet OP的产生方式
   * 1. 插入sheet
   * 2. 导入电子表
   *    导入的电子表产生的insert sheet需要一个标识，交给应用层区分感知
   * */
  from?: 'import' | 'undo' | 'copy' | 'form_partition';
  /** from 为 copy 时需要，指示复制的 source sheet id */
  copySheetId?: SheetId;
  /** from 为 copy 时有效，为 true 时表示同时复制 source sheet 内的所有记录 */
  copyAllRecords?: boolean;
}

export interface DeleteSheetPayload extends BasePayload {
  sheetId: SheetId;
  index: number;
  origin: SheetDTO;
}

export interface MoveSheetPayload extends BasePayload {
  sheetId: SheetId;
  index: number;
  target: number;
}

export type UpdateSheetPayload = BasePayload & GenerateUpdatePayload<{
  sheetId: SheetId;
}, {
  metaName: [['name'], string];
  metaDesc: [['description'], SheetDTO['description'] | undefined];
  config: [['config'], SheetDTO['config'] | undefined];
  recordConfig: [['config', 'recordConfig'], SheetRecordConfig | undefined];
  fieldGroups: [['config', 'fieldGroups'], SheetFieldGroupsConfig | undefined];
  sheetType: [['sheetType'], SheetType | undefined];
  targetSheetId: [['targetSheetId'], SheetId | undefined];
  synced: [['synced'], boolean | undefined];
}>;

export interface InsertViewPayload extends BasePayload {
  sheetId: SheetId;
  index: number;
  value: ViewDTO;
}

export interface DeleteViewPayload extends BasePayload {
  sheetId: SheetId;
  viewId: ViewId;
  index: number;
  origin: ViewDTO;
}

export interface MoveViewPayload extends BasePayload {
  sheetId: SheetId;
  viewId: ViewId;
  index: number;
  target: number;
}

export type UpdateViewPayload<T extends string[] = string[]> = BasePayload & GenerateUpdatePayload<{
  sheetId: SheetId;
  viewId: ViewId;
}, {
  metaName: [['name'], string];
  filter: [['filter'], Filter[]];
  sort: [['sort'], Sort[]];
  group: [['group'], Group[]];
  conditionalFormats: [['conditionalFormats'], ConditionalFormat[]];
  custom: [['custom', ...T], unknown];
  aggregate: [['aggregate', FieldId], AggregateAction | null];
  subType: [['subType'], string];
  flags: [['flags'], number];
  creator: [['creator'], string];
  description: [['description'], ViewDTO['description'] | undefined];
  config: [['config'], SheetDTO['config'] | undefined];
  recordOpenMethod: [['recordOpenMethod'], ViewDTO['recordOpenMethod']];
}>;

export type UpdateTempViewPayload<T extends string[] = string[]> = BasePayload & GenerateUpdatePayload<{
  sheetId: SheetId;
  viewId: ViewId;
}, {
  filter: [['filter'], Filter[]];
  sort: [['sort'], Sort[]];
  group: [['group'], Group[]];
  conditionalFormats: [['conditionalFormats'], ConditionalFormat[]];
  custom: [['custom', ...T], unknown];
  aggregate: [['aggregate', FieldId], AggregateAction | null];
}>;

export interface FieldPosition { [viewId: string]: number }

/**
 * 显示标记 op 需要触发的后续流程，服务端消费
 */
export enum SubsequentActionsType {
  // 更新自动编号（暂未消费，暂时还使用原 fieldsNeedNumber）
  autoNumber = 1,
  // 更新扩展字段
  extendField = 2,
  // 更新字段模版
  fieldDecorator = 4,
}

export interface InsertFieldPayload extends BasePayload {
  sheetId: SheetId;
  fieldId: FieldId;
  value: FieldDTO;
  position: FieldPosition;
  subsequentActions?: number;
}

export type UpdateFieldPayload<T extends FieldTypes = FieldTypes> = BasePayload & GenerateUpdatePayload<{
  sheetId: SheetId;
  viewId: ViewId;
  fieldId: FieldId;
  /** 在其他字段修改为编号字段时，通过该标记、服务端重置最新编号值 */
  resetAutoNum?: boolean;
  subsequentActions?: number;
}, {
  metaName: [['name'], string];
  type: [['type'], T];
  dataType: [['dataType'], CellDataType];
  config: [['config', string], unknown];
  editable: [['editable'], boolean];
  isPrimary: [['isPrimary'], boolean];
  hidden: [['hidden'], boolean];
  props: [['config', 'renderFieldConfig', 'props'], unknown];
  extendInfo: [['config', 'extendInfo'], unknown];
  description: [['description'], FieldDTO['description']];
  synced: [['config', 'renderFieldConfig', 'synced'], boolean];
  decorator: [['config', 'decorator'], Decorator];
  extensionType: [['config', 'extensionType'], FieldDecoratorExtensionType];
}>;

/**
 * 对于 Field 数据为软删除
 * 在视图数列数组中真实删除
 */
export interface DeleteFieldPayload extends BasePayload {
  sheetId: SheetId;
  fieldId: FieldId;
  position: FieldPosition;
  origin: FieldDTO;
}

export interface MoveColumnPayload extends BasePayload {
  sheetId: SheetId;
  viewId: ViewId;
  fieldId: FieldId;
  index: number;
  target: number;
}

/**
 * 数据行操作作用于模型层 apply
 * operator 操作者，会更新 author 或者 updater
 * timestamp 会更新 createTime 或者 updateTime
 */
export interface BaseRowPayload extends BasePayload {
  operator: ObjectCellValue;
  sheetId: SheetId;
  rowId: RowId;
  preRowId: PreRowId;
  /** 视图 id */
  viewId?: ViewId;
}

export type InsertRowPayload = BaseRowPayload & {
  /** 当 cells 为空的时候传 null */
  value: Cells | null;
  preRank?: string;
  rank?: string;
  origin: null;
};

export type DeleteRowPayload = BaseRowPayload & {
  value: null;
  preRank?: string;
  rank?: string;
  origin: RecordDTO;
};

export type MoveRowPayload = BaseRowPayload & {
  targetPreRowId: PreRowId;
  preRank?: string;
  targetPreRank?: string;
  rank?: string;
  sheetType: SheetType | undefined;
  originRank?: string;
};

export type TypeChangeBaseFieldData = Pick<FieldDTO, 'config' | 'name' | 'type' | 'hidden' | 'refId' | 'isPrimary'>;
export type TypeChangedFields = Record<FieldId, {
  before: TypeChangeBaseFieldData;
  after: TypeChangeBaseFieldData;
}>;

export type UpdateRowPayload = BaseRowPayload & {
  value: UpdateCells;
  origin: UpdateCells;
  typeChangedFields?: TypeChangedFields;
};

export type AppendRowPayload = Omit<BaseRowPayload, 'preRowId'> & {
  value: Cells | null;
  /** 标记本次新增的记录要进行编号的字段 */
  fieldsNeedNumber?: FieldId[];
  subsequentActions?: number;
  sheetType: SheetType | undefined;
};

export type AutoNumResultPayload = BasePayload & {
  operator: ObjectCellValue;
  sheetId: SheetId;
  /** 若 field 对应的 cells 为空，表示需要前端拉取 */
  autoNumValues: Record<
    FieldId,
    Record<RowId, AutoNumValue>
  >;
  /** 对应做了编号的 OP version，前端将拉取此版本 */
  autoNumVersion: number;
};

export interface TypeTransformInfo {
  source: FieldDTO;
  target: FieldDTO;
}

/**
 * 列类型转换
 * 该 OP 会被服务端消费，基于转换行拷贝并转换为新列数据
 */
export type TypeTransformPayload = BasePayload & {
  operator: ObjectCellValue;
  sheetId: SheetId;
  transform: TypeTransformInfo[];
};

export type ModifyAssociationsCellPayload = BasePayload & {
  operator: ObjectCellValue;
  /** 需要更新的表 id */
  sheetId: SheetId;
  /** 需要更新的表类型 */
  sheetType: SheetType | undefined;
  /** 需要更新的行 id */
  rowId: RowId;
  /** 需要更新的字段 id */
  fieldId: FieldId;
  /** 关联表的主键列 Id 用于 OT 冲突判断（该关联列所关联的表的主键列 id） */
  associationsPrimaryId: FieldId;
  /** rowId 是当前行关联上的另外张表的被关联的行的 id */
  value: Array<{ rowId: RowId; title: string }>;
  /** insert/delete 不需要 origin, 仅 update 需要 */
  origin?: Array<{ rowId: RowId; title: string }>;
};

export type Uid = string;

/** 当某一 client 进行 Cell 编辑时，其它 client 不允许编辑 */
export interface LockCellPayload {
  sheetId: SheetId;
  rowId: RowId;
  fieldId: FieldId;
  operator: Uid;
}

/**
 * 合并分片数据到内存模型
 */
export interface MergeSliceDataToViewPayload {
  sheetId: SheetId;
  viewId: ViewId;
  value: {
    /** 行数据 */
    rawData?: {
      limit: number;
      offset: number;
      rows: RecordDTO[];
      /** 是否合并列数据 */
      mergeSliceField?: boolean;
    };

    /**
     * 其它信息
     * 比如 group、聚合信息
     */
    totalCount?: number;
    aggregate?: AggregateResult;
    group?: GroupResult;
    foundResult?: FoundResult;
  };
}
export interface OverrideViewContentPayload {
  sheetId: SheetId;
  viewId: ViewId;
  value: {
    group?: GroupResult;
    rows?: RecordWithHierarchyInfo[];
    foundResult?: FoundResult;
  };
}

export interface UpdateWorkbookOP {
  target: OperationTarget.META;
  action: MetaOperationAction.UPDATE_WORKBOOK;
  payload: UpdateWorkbookPayload;
}

export interface InsertNSheetOP {
  target: OperationTarget.META;
  action: MetaOperationAction.INSERT_N_SHEET;
  payload: InsertNSheetPayload;
}

export interface MoveNSheetOP {
  target: OperationTarget.META;
  action: MetaOperationAction.MOVE_N_SHEET;
  payload: MoveNSheetPayload;
}

export interface DeleteNSheetOP {
  target: OperationTarget.META;
  action: MetaOperationAction.DELETE_N_SHEET;
  payload: DeleteNSheetPayload;
}

export interface UpdateNSheetOP {
  target: OperationTarget.META;
  action: MetaOperationAction.UPDATE_N_SHEET;
  payload: UpdateNSheetPayload;
}

export interface InsertSheetOP {
  target: OperationTarget.META;
  action: MetaOperationAction.INSERT_SHEET;
  payload: InsertSheetPayload;
}

export interface DeleteSheetOP {
  target: OperationTarget.META;
  action: MetaOperationAction.DELETE_SHEET;
  payload: DeleteSheetPayload;
}

export interface MoveSheetOP {
  target: OperationTarget.META;
  action: MetaOperationAction.MOVE_SHEET;
  payload: MoveSheetPayload;
}

export interface UpdateSheetOP {
  target: OperationTarget.META;
  action: MetaOperationAction.UPDATE_SHEET;
  payload: UpdateSheetPayload;
}

export interface InsertViewOP {
  target: OperationTarget.META;
  action: MetaOperationAction.INSERT_VIEW;
  payload: InsertViewPayload;
}

export interface DeleteViewOP {
  target: OperationTarget.META;
  action: MetaOperationAction.DELETE_VIEW;
  payload: DeleteViewPayload;
}

export interface MoveViewOP {
  target: OperationTarget.META;
  action: MetaOperationAction.MOVE_VIEW;
  payload: MoveViewPayload;
}

export interface UpdateViewOP {
  target: OperationTarget.META;
  action: MetaOperationAction.UPDATE_VIEW;
  payload: UpdateViewPayload;
}

export interface UpdateTempViewOP {
  target: OperationTarget.META;
  action: MetaOperationAction.UPDATE_TEMP_VIEW;
  payload: UpdateTempViewPayload;
}

export interface DeleteTempViewOP {
  target: OperationTarget.META;
  action: MetaOperationAction.DELETE_TEMP_VIEW;
  payload: DeleteViewPayload;
}

export interface InsertFieldOP {
  target: OperationTarget.META;
  action: MetaOperationAction.INSERT_FIELD;
  payload: InsertFieldPayload;
}

export interface UpdateFieldOP {
  target: OperationTarget.META;
  action: MetaOperationAction.UPDATE_FIELD;
  payload: UpdateFieldPayload;
}

export interface DeleteFieldOP {
  target: OperationTarget.META;
  action: MetaOperationAction.DELETE_FIELD;
  payload: DeleteFieldPayload;
}

export interface MoveColumnOP {
  target: OperationTarget.META;
  action: MetaOperationAction.MOVE_COLUMN;
  payload: MoveColumnPayload;
}

export interface MoveTempViewFieldOP {
  target: OperationTarget.META;
  action: MetaOperationAction.MOVE_TEMP_VIEW_FILED;
  payload: MoveColumnPayload;
}


export type OPPath = `${string}[${number}]` | `${string}.${string}`;

export interface StorageInsertOP {
  target: OperationTarget.META;
  action: MetaOperationAction.STORAGE_INSERT;
  payload: BasePayload & {
    // 例如 applications.dashboard.list[0] /
    // 或 applications.dashboard.list[0].list[0]
    path: OPPath;
    value: unknown;
  };
}

export interface StorageUpdateOP {
  target: OperationTarget.META;
  action: MetaOperationAction.STORAGE_UPDATE;
  payload: BasePayload & {
    path: OPPath;
    // null 表示将 paths 对应属性置为不存在
    value: unknown | null;
    origin: unknown | null;
  };
}

export interface StorageDeleteOP {
  target: OperationTarget.META;
  action: MetaOperationAction.STORAGE_DELETE;
  payload: BasePayload & {
    path: OPPath;
    origin: unknown;
  };
}

export interface StorageMoveOP {
  target: OperationTarget.META;
  action: MetaOperationAction.STORAGE_MOVE;
  payload: BasePayload & {
    path: OPPath;
    // target 位置取值为不删除本身情况下要移动到的目标位置
    // 例如有 [a, b, c, d] 要移动 b 到 c 后面 origin 应该为 3
    target: number;
  };
}

/** @deprecated */
export interface InsertRowOP {
  target: OperationTarget.RAW_DATA;
  action: RawDataOperationAction.INSERT_ROW;
  subAction: 'create' | 'restore';
  payload: InsertRowPayload;
}

export interface AppendRowOP {
  target: OperationTarget.RAW_DATA;
  action: RawDataOperationAction.APPEND_ROW;
  payload: AppendRowPayload;
}

/** @deprecated */
export interface UpdateRowOP {
  target: OperationTarget.RAW_DATA;
  action: RawDataOperationAction.UPDATE_ROW;
  payload: UpdateRowPayload;
}

/** @deprecated */
export interface DeleteRowOP {
  target: OperationTarget.RAW_DATA;
  action: RawDataOperationAction.DELETE_ROW;
  payload: DeleteRowPayload;
}

export interface InsertRecordData {
  cells: Cells;
  preRowId: PreRowId;
  preRank?: string;
  rank?: string;
  // 若是由 DeleteRecords 产生的反操作，会携带原 create 信息
  creator?: ObjectCellValue;
  createdTime?: number;
}

// #region Rows 批量操作
export interface InsertRecords {
  target: OperationTarget.RAW_DATA;
  action: RawDataOperationAction.INSERT_RECORDS;
  // 只有当删除 undo 的时候才能携带 subAction
  subAction?: 'restore';
  payload: BasePayload & {
    operator: ObjectCellValue;
    sheetId: SheetId;
    sheetType: SheetType | undefined;
    records: Record<RecordId, InsertRecordData>;
    // 添加场景，自动化或者 openAPI 存在批量插入记录，插入位置都是末尾，现在没有一个 appendRecords OP ，新增改动范围较大
    // 因此添加一个标记来临时处理这样的 case 如果携带这个标记表示是批量插入到末尾，对于 rank 生成逻辑就可以自动识别了
    insertOrder?: string[];
    /** 视图 id */
    viewId?: ViewId;
    /** 标记本次新增的记录要进行编号的字段 */
    fieldsNeedNumber?: FieldId[];
    subsequentActions?: number;
  };
}
export type InsertRecordsPayload = InsertRecords['payload'];

export type CalcResultRecordBucketItem = [start: string, end: string];

export interface CalcResultOP {
  target: OperationTarget.RAW_DATA;
  action: RawDataOperationAction.CALC_RESULT;
  payload: BasePayload & {
    calcVersion: number;
    sheetId: SheetId;
    buckets: CalcResultRecordBucketItem[];
    ossKeys: string[]; // 根据 ossKeys 也可以换取 ossUrls
    ossUrls: string[]; // 真实地址
    expireTime: number;
  };
}

export interface DeleteRecords {
  target: OperationTarget.RAW_DATA;
  action: RawDataOperationAction.DELETE_RECORDS;
  payload: BasePayload & {
    operator: ObjectCellValue;
    sheetId: SheetId;
    sheetType: SheetType | undefined;
    records: Record<RecordId, {
      cells: Cells;
      preRowId: PreRowId;
      preRank?: string;
      rank?: string;
      // 删除 & undo 后，保留原 create 信息
      creator: ObjectCellValue;
      createdTime: number;
    }>;
    /** 视图 id */
    viewId?: ViewId;
    /**
     * 为使 InsertRecords 取反为 DeleteRecords 再取反回 InsertRecords 时保留该属性。
     * 没有其它逻辑使用。
     */
    fieldsNeedNumber?: FieldId[];
  };
}
export type DeleteRecordsPayload = DeleteRecords['payload'];

export interface UpdateRecords {
  target: OperationTarget.RAW_DATA;
  action: RawDataOperationAction.UPDATE_RECORDS;
  payload: BasePayload & {
    operator: ObjectCellValue;
    sheetId: SheetId;
    sheetType: SheetType | undefined;
    records: Record<RecordId, {
      cells: UpdateCells;
      origin: UpdateCells;
      // updater: 为支持「修改单选/多选字段的选项时更新 cells 的 text、但要保持原 updater」，
      // todo: 待 单选/多选单元格 改造后删除
      updater?: ObjectCellValue;
    }>;
    // 标记 rows 变化中哪些字段是关联字段 cell
    // 用于 OT 时判断这些 cell 是新增还是删除关联记录
    // 目前存在 null 的情况是因为表单二次填写引入了 bug 导致 op 的 assoFields 预期是数组
    assoFields: Array<{
      id: FieldId;
      assoSheet: SheetId;
    }> | null;
    typeChangedFields?: TypeChangedFields;
    /** 视图 id */
    viewId?: ViewId;
    /**
     * 标记本 OP 中的记录要进行编号的字段。
     * - 目前只有 新增编号字段(insertField) 和 其它字段修改为编号字段(updateField) 时才会跟随要编号的 updateRecords OP
     */
    fieldsNeedNumber?: FieldId[];
    subsequentActions?: number;
  };
}
export type UpdateRecordsPayload = UpdateRecords['payload'];
// #endregion

/** 仅用于前端同步编号结果；仅 notable-biz/auto-num/index 消费，其他逻辑不应该关心 */
export interface AutoNumResultOP {
  target: OperationTarget.RAW_DATA;
  action: RawDataOperationAction.AUTO_NUM_RESULT;
  payload: AutoNumResultPayload;
}

export interface MoveRowOP {
  target: OperationTarget.RAW_DATA;
  action: RawDataOperationAction.MOVE_ROW;
  payload: MoveRowPayload;
}

export interface TypeTransformOP {
  target: OperationTarget.RAW_DATA;
  action: RawDataOperationAction.TYPE_TRANSFORM;
  subAction: 'transform' | 'cancel';
  payload: TypeTransformPayload;
}

export interface ModifyAssociationsCellOP {
  target: OperationTarget.RAW_DATA;
  action: RawDataOperationAction.MODIFY_ASSOCIATIONS_CELL;
  subAction: 'insert' | 'delete' | 'update';
  payload: ModifyAssociationsCellPayload;
}

export interface LockCellOP {
  target: OperationTarget.LOCAL;
  action: LocalOperationActon.LOCK_CELL;
  payload: LockCellPayload;
}

export interface UnlockCellOP {
  target: OperationTarget.LOCAL;
  action: LocalOperationActon.UNLOCK_CELL;
  payload: LockCellPayload;
}

export interface MergeSliceDataOP {
  target: OperationTarget.LOCAL;
  action: LocalOperationActon.MERGE_SLICE_DATA;
  payload: MergeSliceDataToViewPayload;
}

export interface SetFullRawDataOP {
  target: OperationTarget.LOCAL;
  action: LocalOperationActon.SET_FULL_RAW_DATA;
  payload: {
    regenerateIndex?: boolean;
    skipInitialFullCalc?: boolean;
    data: Record<SheetId, RecordDTO[]>;
    fullRankDataMap?: Record<string, Array<{ id: string; rank: string }>>;
  };
}

export interface SetCellVisibilityOP {
  target: OperationTarget.LOCAL;
  action: LocalOperationActon.SET_CELL_VISIBILITY;
  payload: {
    sheetId: SheetId;
    visilibity: CellVisibility | undefined;
  };
}

export interface FetchRecordsOP {
  target: OperationTarget.LOCAL;
  action: LocalOperationActon.FETCH_RECORDS;
  payload: {
    sheetId: SheetId;
    records: RecordDTO[];
  };
}

export interface LocalUpdateRecordsOP {
  target: OperationTarget.LOCAL;
  action: LocalOperationActon.LOCAL_UPDATE_RECORDS;
  payload: {
    sheetId: SheetId;
    records: Array<{
      id: string;
      cells: UpdateCells;
      origin: UpdateCells;
    }>;
    /** 视图 id */
    viewId?: ViewId;
    // 没有作用，只是一些通用逻辑中需要
    operator: ObjectCellValue;
  };
}

export type MetaNSheetOp = InsertNSheetOP | MoveNSheetOP | DeleteNSheetOP | UpdateNSheetOP;
export type MetaSheetOP = InsertSheetOP | DeleteSheetOP | MoveSheetOP | UpdateSheetOP;
export type MetaViewOP = InsertViewOP | DeleteViewOP | MoveViewOP | UpdateViewOP | UpdateTempViewOP | DeleteTempViewOP | MoveTempViewFieldOP;
export type MetaFieldOP = InsertFieldOP | UpdateFieldOP | DeleteFieldOP | MoveColumnOP;
export type MetaStorageOP = StorageInsertOP | StorageDeleteOP | StorageUpdateOP | StorageMoveOP;

export type MetaOperation = UpdateWorkbookOP | MetaNSheetOp | MetaSheetOP | MetaViewOP | MetaFieldOP
  | MetaStorageOP;

export type RawDataOperation = TypeTransformOP
  | InsertRowOP | UpdateRowOP | DeleteRowOP | MoveRowOP | AppendRowOP | ModifyAssociationsCellOP
  | InsertRecords | DeleteRecords | UpdateRecords | CalcResultOP | AutoNumResultOP;

export type VersionedRawDataOperation = RawDataOperation & {
  version?: number;
};

export type LocalOperation = LockCellOP | UnlockCellOP | MergeSliceDataOP
 | SetFullRawDataOP | SetCellVisibilityOP | FetchRecordsOP | LocalUpdateRecordsOP;


/**
 * 存在 OT 产出为一个不需要做任何操作的 OP 结果
 * no operation 描述该操作
 */
export interface NoOperation {
  target: OperationTarget.META | OperationTarget.RAW_DATA | OperationTarget.LOCAL | OperationTarget.FEAT;
  action: 'noop';
  payload: null;
}

/** 临时给大 OP 拆包标做标记，表示这是一个分片包 */
export interface DANGEROUS_SLICE_OP_Operation {
  target: OperationTarget.LOCAL;
  action: 'noop';
  payload: {
    type: 'sliceOP';
    action: 'undo' | 'do';
    index: number;
    total: number;
  };
}

// TODO：@默牛 后续用 FeatOperation 代替
export type CustomizedOperationPayload = Record<string, unknown> & { type: string };
export interface CustomizedOperation {
  target: OperationTarget.CUSTOMIZED;
  action: 'noop';
  payload: CustomizedOperationPayload;
}
export type Operation = MetaOperation | RawDataOperation | LocalOperation | NoOperation
| CustomizedOperation | FeatOperation | DANGEROUS_SLICE_OP_Operation;

export interface OperationCollabActionMap {
  [MetaOperationAction.DELETE_FIELD]: DeleteFieldOP;
  [MetaOperationAction.INSERT_FIELD]: InsertFieldOP;
  [MetaOperationAction.MOVE_COLUMN]: MoveColumnOP;
  [MetaOperationAction.UPDATE_FIELD]: UpdateFieldOP;

  [MetaOperationAction.UPDATE_VIEW]: UpdateViewOP;
  [MetaOperationAction.MOVE_VIEW]: MoveViewOP;
  [MetaOperationAction.INSERT_VIEW]: InsertViewOP;
  [MetaOperationAction.DELETE_VIEW]: DeleteViewOP;

  [MetaOperationAction.STORAGE_INSERT]: StorageInsertOP;
  [MetaOperationAction.STORAGE_DELETE]: StorageDeleteOP;
  [MetaOperationAction.STORAGE_UPDATE]: StorageUpdateOP;
  [MetaOperationAction.STORAGE_MOVE]: StorageMoveOP;

  [MetaOperationAction.INSERT_SHEET]: InsertSheetOP;
  [MetaOperationAction.DELETE_SHEET]: DeleteSheetOP;
  [MetaOperationAction.MOVE_SHEET]: MoveSheetOP;
  [MetaOperationAction.UPDATE_SHEET]: UpdateSheetOP;

  [MetaOperationAction.UPDATE_WORKBOOK]: UpdateWorkbookOP;
  [MetaOperationAction.INSERT_N_SHEET]: InsertNSheetOP;
  [MetaOperationAction.DELETE_N_SHEET]: DeleteNSheetOP;
  [MetaOperationAction.MOVE_N_SHEET]: MoveNSheetOP;
  [MetaOperationAction.UPDATE_N_SHEET]: UpdateNSheetOP;

  [RawDataOperationAction.INSERT_ROW]: InsertRowOP;
  [RawDataOperationAction.APPEND_ROW]: AppendRowOP;
  [RawDataOperationAction.UPDATE_ROW]: UpdateRowOP;
  [RawDataOperationAction.DELETE_ROW]: DeleteRowOP;
  [RawDataOperationAction.MOVE_ROW]: MoveRowOP;
  [RawDataOperationAction.TYPE_TRANSFORM]: TypeTransformOP;
  [RawDataOperationAction.MODIFY_ASSOCIATIONS_CELL]: ModifyAssociationsCellOP;
  [RawDataOperationAction.INSERT_RECORDS]: InsertRecords;
  [RawDataOperationAction.DELETE_RECORDS]: DeleteRecords;
  [RawDataOperationAction.UPDATE_RECORDS]: UpdateRecords;
  [RawDataOperationAction.CALC_RESULT]: CalcResultOP;
  [RawDataOperationAction.AUTO_NUM_RESULT]: AutoNumResultOP;

  [FeatAction.UPDATE_FEAT]: UpdateWorkflowOP | UpdateDocumentOp | UpdateAppLockOp | UpdateDevMetaOp | UpdatePartitionOp | UpdateSchemaVersionOp;
}
