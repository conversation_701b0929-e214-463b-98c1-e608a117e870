const resource: {[key: string]: string} = {
  "we_notable_learn_more": "Learn more",
  "ai_analyze_recommend_creation": "AI is analyzing and recommending new content",
  "new_content_generated_by_ai": "The following new content is generated by AI model",
  "rename_not_supported": "Rename not supported",
  "moved_to": "Moved to",
  "expand_sidebar": "Expand Sidebar",
  "notable_recommend": "Recommend",
  "create_from_empty": "New from Blank",
  "table": "Table",
  "notable_notableViewFramework_TheCurrentVersionOfThe": "The current version of the view is not supported.",
  "notable_notableViewFramework_PleaseRefreshProcessing": "Please refresh processing",
  "notable_notableViewFramework_TheDataHasBeenSaved": "Data saved, please refresh and try again",
  "notable_notableViewFramework_DocumentError": "Document Error",
  "notable_notableViewFramework_Refresh": "Refresh",
  "notable_notableViewFramework_NoViewCurrentlyAvailable": "No current view",
  "notable_notableViewFramework_HiddenFields": " Hidden Fields",
  "notable_notableViewFramework_TheNameCannotExceedCharacters": "Name must not exceed 50 characters",
  "notable_notableViewFramework_RenameFailed": "Rename failed",
  "notable_notableViewFramework_IKnow": "I know.",
  "notable_notableViewFramework_NameAlreadyExistsEnterA": "{{name}} already exists, please enter a different name",
  "notable_notableFormView_FormdataitemnameIsARequiredField": "{{formDataItemName}} is a required field, please fill in",
  "notable_catalog_no_result": "No matching results",
  "notable_catalog_search_change_keyword_message": "Try another keyword",
  "notable_notableViewFramework_DataTable": "Data Sheet",
  "notable_notableViewFramework_View": "View",
  "notable_notableViewFramework_ConfirmDeletion": "Are you sure you want to delete the {{typeName}} {{name}}?",
  "notable_notableViewFramework_DeleteTypename": "Delete {{typeName}}",
  "notable_catalog_copy_failed": "Copy failed, please refresh the page and try again",
  "notable_catalog_copy_success": "Copy success",
  "notable_component_copy_success": "Replication successful",
  "notable_notableGridView_CopyRecordUrl": "Copy Record Link",
  "we_notable_share_record_to_chat": "Share {{recordName}} to chat",
  "notable_layout_show_siderbar": "Show sidebar",
  "notable_notableViewFramework_CreateAView": "New View",
  "notable_layout_hide_siderbar": "Hide sidebar",
  "notable_common_search": "Search {{typeName}}",
  "notable_sheet_type_name": "Sheet",
  "notable_dashboard_type_name": "Dashboard",
  "notable_view_type_name": "View",
  "notable_notableComponents_RenameATable": "Rename",
  "notable_notableViewFramework_CopyName": "Copy {{name}}",
  "notable_notableViewFramework_CopyView": "Copy view copy ",
  "notable_notableViewFramework_DeleteName": "Delete {{name}}",
  "notable_common_add": "New {{typeName}}",
  "notable_common_more_action": "More operations",
  "notable_biz_advanced_permission_sheet_no_permission_tip": "Advanced permissions have been enabled. You do not have permission to view the current data table.",
  "notable_notableViewFramework_TheDataTableHasBeen": "Data table has been deleted!",
  "notable_biz_advanced_permission_record_no_permission_tip": "Advanced permissions have been enabled for this multi-dimensional table, and you do not have permission to view the current record.",
  "notable_notableViewFramework_TheRecordHasBeenDeleted": "The record does not exist or has been deleted",
  "notable_notableViewFramework_NoDataTableIsCurrently": "No data table currently available",
  "notable_notableViewFramework_CreateADataTable": "New Data Table",
  "notable_notableViewFramework_EditThisFieldOnThe": "Please edit this field on the PC side",
  "notable_notableViewFramework_Cancel": "Cancel",
  "notable_notableViewFramework_Ok": "Determine",
  "notable_notableViewFramework_DeletedSuccessfully": "Deleted successfully",
  "notable_notableViewFramework_FailedToDelete": "Delete failed",
  "notable_notableViewFramework_EditFieldsColumns": "Edit Field/Column",
  "notable_notableViewFramework_CopyFieldsColumns": "Copy Fields/Columns",
  "notable_notableViewFramework_SortByAToZ": "Sort by A to Z",
  "notable_notableViewFramework_SortByZToA": "Sort by Z to A",
  "notable_notableViewFramework_Press": "Press",
  "notable_notableViewFramework_Filter": "Filter",
  "notable_notableViewFramework_InsertFieldsColumnsToThe": "Insert Field/Column Right",
  "notable_notableViewFramework_ModifyStatisticalMethods": "Modify Statistics Method",
  "notable_notableViewFramework_DeleteFieldsColumns": "Delete Field/Column",
  "notable_notableViewFramework_PreviewIsNotSupportedFor": "Preview is not supported for this field.",
  "notable_notableViewFramework_Dashboard": "Instrument cluster",
  "notable_notableViewFramework_Rename": "Rename",
  "notable_notableViewFramework_EnterAName": "Please enter a name",
  "notable_notableViewFramework_CopiedSuccessfully": "Copy success",
  "notable_notableViewFramework_CreatedSuccessfully": "Successfully created",
  "notable_notableViewFramework_CopyTypename": "Copy {{typeName}}",
  "notable_notableViewFramework_Delete": "Delete",
  "notable_notableViewFramework_SelectView": "Select View",
  "notable_notableViewFramework_DataTableCount": "Data Sheet {{count}}",
  "notable_notableViewFramework_DeleteData": "Delete data",
  "notable_notableViewFramework_ConfirmDeleteData": "Are you sure you want to delete the data?",
  "notable_notableViewFramework_MoreOperations": "More operations",
  "notable_notableViewFramework_DeleteARecord": "Delete Record",
  "notable_notableViewFramework_FieldManagement": "Field Management",
  "notable_notableViewFramework_AddField": "Add Field",
  "notable_notableViewFramework_TheFirstColumnIsThe": "The first column of fields is the index of each piece of data and cannot be deleted, moved, or hidden.",
  "notable_notableViewFramework_TheAssociatedDataTableCannot": "Associated data table cannot be empty",
  "notable_notableViewFramework_YouDoNotHavePermission": "You do not have edit permissions",
  "notable_notableViewFramework_TheFieldNameCannotExceed": "Field name cannot exceed 150 characters",
  "notable_notableViewFramework_TheFieldNameAlreadyExists": "Field name already exists and no name is allowed",
  "notable_notableViewFramework_OperationSucceeded": "Operation succeeded",
  "notable_notableViewFramework_ColumnTypeConversion": "Column type conversion",
  "notable_notableComponents_ChangeFieldTypeToPerson": "Convert to person Field",
  "notable_notableComponents_ChangeFieldTypeToPerson_desc": "This action will convert the person's name, phone number, and mailbox information in the field to a person, and may clear mismatched data in some cells.",
  "notable_notableComponents_ChangeFieldType_Converting": "Converting",
  "notable_notableComponents_ChangeFieldType_LimitFailed": "Conversion failed, the number of conversions exceeds the upper limit",
  "notable_notableComponents_extendField_converting_tip": "Field conversion, please complete the conversion before setting",
  "notable_notableComponents_ChangeFieldType_selectNewField": "New field to save converted person",
  "notable_notableComponents_ChangeFieldType_canNotCreateNewField": "No New Field",
  "notable_notableComponents_ChangeFieldType_ConvertFailed": "Conversion failed",
  "notable_notableComponents_ChangeFieldTypeToPerson_PartialFailed": "The personnel field conversion is completed, and a total of {{num}} records failed to be converted.",
  "notable_notableViewFramework_ThisOperationMayClearExisting": "This operation may clear the existing data in some cells, if there is a problem with the conversion, you can undo the operation",
  "notable_notableViewFramework_ModifyFields": "Modify Fields",
  "notable_notableViewFramework_Title": "Title",
  "notable_notableViewFramework_EnterAFieldTitle": "Please enter a field title",
  "notable_notableViewFramework_FieldType": "Field Type",
  "notable_notableViewFramework_ThisFieldIsNotSupported": "This field is not supported.",
  "notable_notableViewFramework_Conventional": "General",
  "notable_notableViewFramework_Advanced": "Advanced",
  "notable_notableViewFramework_TheSystemAutomaticallyFillsIn": "The system automatically fills in and does not support modification.",
  "notable_notableViewFramework_SubmitForm": "Submit Form",
  "notable_notableViewFramework_Format": "Format",
  "notable_notableViewFramework_DisplayFormat": "Display Format",
  "notable_notableViewFramework_SelectDisplayFormat": "Select Display Format",
  "notable_notableViewFramework_DoNotFilter": "Don't Filter",
  "notable_notableViewFramework_AssociatedDataTable": "Associated Data Table",
  "notable_notableViewFramework_SelectDataTable": "Select Data Table",
  "notable_notableViewFramework_SelectATableToAssociate": "Please select a data table to associate",
  "notable_notableViewFramework_NumberOfRecords": "Number of records",
  "notable_notableViewFramework_AllowMultipleRecordsToBe": "Allow multiple records to be associated",
  "notable_components_allowSelectMultiDepartment": "Allow selection of multiple departments",
  "notable_notableViewFramework_NumberOfDepartments": "Number of departments",
  "notable_notableViewFramework_FilterRecordsFromView": "Filter records from a view",
  "notable_notableViewFramework_SelectFilterView": "Please select a filter view",
  "notable_notableViewFramework_Options": "Options",
  "notable_notableViewFramework_AddOptions": "Add Options",
  "notable_notableViewFramework_EnterOptions": "Please enter option content",
  "notable_notableGridView_NotDisplayed": "Hide",
  "notable_notableGridView_NotSpecified": "Empty",
  "notable_notableGridView_Entered": "Filled",
  "notable_notableGridView_UniqueValue": "Unique",
  "notable_notableGridView_PercentageNotSpecified": "Percentage not specified",
  "notable_notableGridView_CompletedPerCent": "Completed per cent",
  "notable_notableGridView_PercentageOfUniqueValues": "Percentage of unique values",
  "notable_notableGridView_TotalRecords": "Count",
  "notable_notableGridView_Summation": "Summation",
  "notable_notableGridView_Average": "Average",
  "notable_notableGridView_MaximumValue": "Maximum value",
  "notable_notableGridView_MinimumValue": "Minimum value",
  "notable_notableGridView_GouXuan": "Gou Xuan",
  "notable_notableGridView_NotGouXuan": "Not Gou Xuan",
  "we_notable_confirm_deletion_of_field_with_extra_info": "Are you sure you want to delete the field “{{fieldName}}”? After deletion, {{extraInfo}}",
  "we_notable_restore_original_field_by_undoing": "You can restore the original field by undoing{{shortcut}}.",
  "notable_notableComponents_DeleteAFieldOrColumn": "Delete a field or column",
  "notable_notableViewFramework_NumberOfMentions": "Number of mentions",
  "notable_notableViewFramework_Remove": "Remove {{name}}",
  "notable_notableViewFramework_ConfirmRemove": "Are you sure you want to move {{typeName}} {{name}} out of SmartTable?",
  "notable_notableViewFramework_DocumentCreateSuccess": "The description document has been synchronously created under the current multi-dimensional table, and the permission can be set separately.",
  "notable_notableViewFramework_DocumentCreateLoading": "Creating new document",
  "notable_notableViewFramework_NetworkError": "Network exception, please try again later",
  "notable_notableViewFramework_DocumentCreateLimit": "You can create a maximum of 5 documents. ",
  "notable_notableViewFramework_noMoreTip": "No more prompting",
  "notable_components_allowSelectMultiMention": "Allow multiple people to be selected",
  "we_notable_partition_sheet_edit_disabled": "Historical period data table does not support editing",
  "notable_NotableReact_InvalidNameDetail": "The name does not conform to the rules, please ensure that its length is less than 100 words, is not empty and does not contain the following characters",
  "notable_sheet_name_info_title": "Invalid name",
  "notable_notableFields_MultiSelect": "Multiple select",
  "notable_components_attachmentConfigTitle": "Attachment Upload method",
  "notable_components_attachmentOnlyCamera": "Can only be uploaded via mobile shooting",
  "notable_notableComponents_progress_color": "Color",
  "notable_notableComponents_progress_customize": "Custom progress bar value",
  "notable_notableComponents_progress_min": "Start Value",
  "notable_notableComponents_progress_max": "Target Value",
  "notable_notableComponents_NumberFormat": "Number Format",
  "notable_notableComponents_progress_precision": "Decimal Places",
  "notable_notableComponents_AddressType1": "Province, City District",
  "notable_notableComponents_AddressType2": "Provincial City District-Street",
  "notable_components_format": "Format",
  "notable_notableFormView_Privacy_Field_Tip": "*This question involves private information, please pay attention to verify the identity of the collector",
  "notable_notableFormView_FieldIsRequired": "This Title must answer",
  "notable_form_setting_partition_historySheet": "Historical Cycle Data Table",
  "notable_form_setting_partition_help_link": "Learn More",
  "notable_view_flag": "View Protection",
  "notable_view_flag_collab_tip": "All collaborators can modify the configuration",
  "notable_view_flag_lock": "Lock View",
  "notable_view_flag_lock_tip": "When switched on, editing of view configurations is disabled for all collaborators.",
  "collection": "New Form",
  "new": "New",
  "notable_biz_advanced_permission_not_auth_tips": "Advanced permissions are enabled. Please contact the document administrator to assign roles",
  "we_notable_titleEditor_empty_tip": "Not Allow Empty Name",
  "the_name_exist": "Name already exists",
  "the_sheet_name": "Data table name",
  "notable_notableComponents_Enter": "Enter",
  "the_copy_range": "Copy Range",
  "only_copy_meta": "Data table structure only",
  "copy_all_data": "Data table structure and all records",
  "notable_notableApplicationPlugins_WnameCopy": "{{wName}} (copy)",
  "del_sheet_has_dep_tip": "This sheet has been associated to other sheet. After deletion, the associated records in other sheet will be cleared. Confirm deletion?",
  "notable_notableFormView_FieldInvalid": "Some questions have not passed the verification, please check",
  "we_notable_submitted_records_hidden": "The new record is hidden",
  "notable_biz_advanced_permission_need_submit_new_record": "Advanced Permission is enabled in this file, and after submitting new records you may not be allowed to modify them,  please confirm data before submitting.",
  "we_notable_common_submit": "Submit",
  "we_notable_schedule_remind_disabled_mouse_tip": "No editing permission for the time being",
  "notable_notableViewFramework_NumberOfGroups": "Number of Groups",
  "notable_components_allowSelectMultiGroup": "Allow multiple groups to be selected",
  "notable_notableViewPlugins_TheRecordHasBeenDeleted": "This record has been deleted",
  "notable_notableComponents_OpenInSplitView": "Open in split view",
  "notable_notableComponents_CopyViewUrl": "Copy View Link",
  "notable_notableViewFramework_CopyThisFieldOnThe": "Please copy this field on the PC side",
  "we_notable_export_as": "Export as ",
  "we_notable_export_ad_excel": "Export to Excel (.xlsx) ",
  "we_notable_export_ad_zip": "Export attachments as a compressed package ",
  "sync_setting": "Sync Setup",
  "sync_sync": "Update data",
  "sync_last_update": "Last sync",
  "sync_unsync": "Remove Sync",
  "we_notable_cancel": "Cancel",
  "notable_view_flag_current_private": "This view is only visible to yourself",
  "notable_view_flag_current_public": "This view is visible to all collaborators",
  "notable_please_enter_view_name": "Please enter the view name",
  "notable_who_can_see_this_view": "Who can see this view",
  "notable_visible_to_only_me": "Visible to only me",
  "notable_create_view": "Create View",
  "notable_view_name": "View Name",
  "notable_field_barcode_enter_mode": " Input method for barcode",
  "notable_field_barcode_only_mobile_scan": "Only mobile scanning is allowed for entry",
  "we_notable_field_flow_cancel_tip": "Flow terminated",
  "we_notable_field_flow_must_fill_tip": "This item is required",
  "we_notable_field_flow_return_node_state": "Return to this node",
  "we_notable_field_flow_cancel": "Terminate",
  "we_notable_field_flow_cancel_node": "Terminate the flow",
  "we_notable_field_flow_has_cancelled": "This flow has been terminated",
  "we_notable_field_flow_recover": "Resume the flow",
  "we_notable_field_flow_node_has_done": "Completed",
  "we_notable_field_flow_cancel_drawer_title": "Determine to terminate the flow?",
  "we_notable_field_flow_cancel_confirm_tip": "Upon termination, all nodes will change to Not Started status",
  "we_notable_field_flow_done_node": "Complete Node",
  "notable_notableComponents_extendField_multiSelect_tip": "Getting more people information is not supported when multiple people are allowed to be added",
  "notable_notableComponents_Prompt": "Prompt",
  "notable_notableComponents_Delete": "Delete",
  "notable_notableComponents_extendField_source_title": "Data from",
  "notable_notableComponents_extendField_source_invalid_tip": "The synchronized field does not exist and can be converted to a regular field to continue using it.",
  "notable_notableComponents_extendField_no_permission_tip": "You do not have permission to do this, please contact the document administrator if you have any questions.",
  "notable_notableComponents_extendField_source_convert": "Convert immediately",
  "notable_notableComponents_extendField_source_error_multi_person": "Synchronization paused because the synchronized people Field has been changed to allow multiple members to be added",
  "notable_notableComponents_extendField_no_permission_prefix": "Since field expansion is turned on,",
  "notable_form_ref_options_hint": "Option editing is disabled when options reference is enabled.",
  "we_notable_data_synchronizing": "Data is synchronizing...",
  "we_notable_data_synchronization_success": "Synchronization succeeded",
  "we_notable_data_synchronization_fail": "Synchronization failed",
  "notable_notableComponents_extendField_source_refresh": "Refresh Data",
  "view_setting": "View Settings",
  "notable_view_flag_lock_hover_tip": "The view is locked. After unlocking with edit permission, modifying the view configuration will sync to others.",
  "notable_view_flag_private_hover_tip": "This view is a personal view, only visible to yourself",
  "we_notable_field_already_hidden": "Field is hidden",
  "notable_notableComponents_Search": "Search",
  "we_notable_no_search_results": "No search results found",
  "we_notable_view_update_alert_msg": "You changed some view settings. Do you want to save them?",
  "we_notable_view_update_alert_cancel": "Don't save",
  "we_notable_view_update_alert_save": "Save",
  "we_notable_view_update_alert_title": "View settings not saved",
  "we_notable_view_update_alert_success": "View configuration saved",
  "all_view": "All Views",
  "search_view": "Search View",
  "import_or_sync_data": "Import/Synchronize Data",
  "not_allow_the_characters": "Cannot contain special characters",
  "not_allow_empty": "Cannot be empty",
  "name_count_limit": "Cannot exceed {{count}} characters",
  "section": "Section",
  "drag_to_move_position": "Drag to move position",
  "name_duplicated": "Name is duplicated, please enter a different name",
  "move_section_not_allowed": "Adding folders in secondary directories is not allowed",
  "disable_del_only_one_sheet": "There is only one data table currently, it cannot be deleted",
  "drag_here_tip": "Drag content here",
  "connot_move_to_section": "Cannot move into the section anymore",
  "read_only_disable_rename": "Read-only, renaming is not supported",
  "move_to": "Move to",
  "no_section_can_move": "No sections available to move",
  "connot_move_to_sub_section": "Cannot move the section to a sub-section",
  "contain_section_connot_move": "The current section contains sub-sections and cannot be moved to other sections",
  "connot_move_to_self": "Cannot move the section to itself",
  "we_notable_guide_view_menu_btn_guide_title": "Lock, rename, and more actions",
  "we_notable_guide_create_new_doc_guide_title": "Start from a blank data table",
  "we_notable_guide_create_new_form_guide_title": "Collect data, survey",
  "we_notable_guide_view_list_learn_more_guide_title": "Understand what a view is",
  "we_notable_guide_create_new_section_guide_title": "Organize content in an orderly manner using folders, and drag content into folders",
  "notable_view_flag_lock_content": "After setting:\nEveryone will not be able to modify view configurations such as filtering, grouping, etc.\nCollaborators with editing permissions can still edit the content, and can modify the View configuration after unlocking.",
  "notable_view_flag_lock_title": "Lock View",
  "notable_view_lock_success": "View locked successfully",
  "notable_view_flag_unlock_content": "After unlocking, collaborators with editing permissions can modify view configurations such as filtering and grouping.",
  "notable_view_flag_unlock_title": "Unlock View",
  "notable_view_unlock_success": "View unlocked successfully",
  "notable_view_flag_private_content": "View set to be visible to self only",
  "notable_operation_failed_with_refresh": "Operation failed, please refresh and try again",
  "notable_view_flag_public_content": "View set to be visible to all collaborators",
  "notable_view_nav_guide_desc": "{{viewTypeName}} of {{sheetName}}",
  "sheet_desc": "Data table description",
  "sheet_desc_placeholder": "Please enter data table description",
  "notable_notableComponents_EditTableDesc": "Edit data table description",
  "notable_notableViewFramework_EditDesc": "Edit view description",
  "view_desc": "View description",
  "view_desc_placeholder": "Edit view description",
  "update_desc_not_supported": "Updating {{type}} description is not supported",
  "record_alias_name": "Row Name",
  "record_name_alias_tip_title": "Select an identifying name for each row that matches the business scenario",
  "record_unnamed_alias": "Untitled {{recordName}}",
  "read_only_disable_update_desc": "Read-only, updating table descriptions is not supported",
  "notable_record_alias_name_helper": "Example:",
  "notable_record_alias_name_setting_title": "What do you want each row to be called?",
  "read_only_disable_update_sheet": "Read-only status, updating the data table is not supported",
  "update_sheet_not_supported": "Updating the current data table is not supported",
  "pageprint-plugins": "Plug-in",
  "notable_record_alias_name_setting_guide_title": "Edit Row Name",
  "notable_record_alias_name_setting_guide_desc": "Description: You can modify the row name based on the business scenario (e.g., change 'Record' to 'Task'), and all interface displays will be updated accordingly, such as 'Add Record' will become 'Add Task'.",
  "notable_notableFormView_DefaultValue": "Default value",
  "notable_notableComponents_RecordOpenMethod": "Record view mode ",
  "notable_notableComponents_RecordOpenModal": "View records in pop-up ",
  "notable_notableComponents_RecordOpenSide": "View records in the right sidebar ",
  "notable_notableComponents_RecordOpenFullPage": "View the record on a full page ",
  "notable_notableComponents_RecordOpenModal_title": "Pop-up ",
  "notable_notableComponents_RecordOpenSide_title": "Right sidebar ",
  "notable_notableComponents_RecordOpenFullPage_title": "Entire page ",
  "notable_unnamed_document": "Untitled Document ",
  "notable_primary_upgrade_tip_content": "Note:\n1. The title of this record exceeds 255 characters. Only the first 255 characters are retained, and you can still manually modify it.\n2. The title content will be automatically copied to the document body. ",
  "notable_primary_upgrade_tip_title": "This line of records is about to be upgraded to a document, and the title of the document will be limited to 255 characters. ",
  "notable_primary_upgrade_tip_cancelText": "Next time. ",
  "notable_primary_upgrade_tip_confirmText": "Okay, upgrade now. ",
  "we_notable_record_primary_doc_placeholder": "Click to edit "
};

export default resource