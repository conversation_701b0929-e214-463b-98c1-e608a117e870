import { useNotableCore } from '@ali/notable-core';
import { AddM16, Scroll16 } from '@ali/we-icons';
import classNames from 'classnames';
import { observer } from 'mobx-react-lite';
import React, { PropsWithChildren, useEffect, useMemo, useRef, useState } from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { IconButton, Tooltip, TextButton } from '@ali/we-design-next';
import $i18n from '@ali/notable-i18n';
import styled from 'styled-components';
import { themeCss } from '@ali/notable-design';
import { isDingTalk, openLink } from '@ali/notable-jsapi-adaptor';
import { More } from './More';
import {
  Bar,
  SideContainer,
  Scroller,
  Tabs,
  Shadow,
  BottomRadius,
  TabContext,
  ViewTab,
  Tab,
  CreateViewTooltipContent,
} from './styled';
import { NavController, NavControllerProvider } from './NavController';
import { Sortable } from './Sortable';
import { ViewItem } from './ViewItem';
import { CreateViewPopover } from './CreateView';
import { RadiusIcon } from './RadiusIcon';
import { ViewDescEditorModal } from './ViewDescEditor';

interface TabProps {
  id: string;
  active: boolean;
  store: NavController;
  first: boolean;
  nextActive: boolean;
  className?: string;
  disabled?: boolean;
}

const SortableTab = observer((props: PropsWithChildren<TabProps>) => {
  const {
    abilityManager,
    frameworkController: { currentSheetId },
    globalDataController: {
      settings: { readonly },
    },
  } = useNotableCore();
  const canSort = useMemo(() => {
    if (!currentSheetId) return false;
    return abilityManager.can('sheetUpdate', currentSheetId);
  }, [currentSheetId, abilityManager]);
  const { active, store, first, nextActive } = props;
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: props.id,
    resizeObserverConfig: { disabled: true },
    disabled: props.disabled || !canSort,
  });

  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform ? { ...transform, y: 0, scaleX: 1 } : null),
    transition,
    zIndex: isDragging ? 1 : undefined,
  };
  const ref = useRef<HTMLDivElement | null>();
  const { clientWidth, scrollWidth } = store.scrollController;
  useEffect(() => {
    if (active && ref.current) {
      store.scrollController.scrollTo(ref.current.offsetLeft, ref.current.clientWidth);
    }
  }, [active, store.scrollController, clientWidth, scrollWidth]);
  const { overId } = store;

  return (
    <ViewTab
      {...attributes}
      {...listeners}
      ref={(node) => {
        setNodeRef(node);
        ref.current = node;
      }}
      style={style}
      data-role="nav_controller_tab"
      className={classNames(props.className, {
        split: !nextActive && !isDragging && !active,
        active,
      })}
      onClick={() => store.handleTabClick(props.id)}
      onDoubleClick={() => {
        if (readonly) return;
        store.setEditingItemId(props.id);
      }}
    >
      <BottomRadius className={classNames('left')}>
        {((props.active && !first) || isDragging) && <RadiusIcon />}
      </BottomRadius>
      <TabContext
        className={classNames({
          active: props.active || isDragging,
          isDragging,
          noTopLeftRadius: first && active && !isDragging && overId !== props.id,
          readonly,
        })}
      >
        {props.children}
      </TabContext>
      <BottomRadius>{(props.active || isDragging) && <RadiusIcon />}</BottomRadius>
    </ViewTab>
  );
});

export const NavControllerBar = observer(({ prefix }: { prefix?: React.ReactNode }) => {
  const notableCore = useNotableCore();
  const [navController] = useState(() => new NavController(notableCore, 36));
  const {
    tabs,
    scrollController,
    viewMenuStore,
    editingItemId,
    viewCreateStore: { addMenuVisibleId },
    createAble: editable,
  } = navController;
  const { shadowStyle, shadowOffset } = scrollController;
  const createViewText = $i18n.t('notable_common_add', {
    typeName: $i18n.t('notable_view_type_name'),
  });

  useEffect(() => () => navController.destroy(), [navController]);

  useEffect(() => {
    scrollController.updateScrollerRect();
  }, [tabs, scrollController]);

  const handleKnowMoreClick = () => {
    const helpUrl =
      'https://alidocs.dingtalk.com/i/p/Y7kmbokZp3pgGLq2/docs/yZvMRzlLwOAWrxzkGmnAJnjY02pBqGox';
    if (isDingTalk()) {
      openLink({ url: helpUrl });
    } else {
      window.open(helpUrl);
    }
  };

  const renderTooltipTitle = () => {
    return (
      <CreateViewTooltipContent>
        {tabs.length > 1 && <span className="create-view-text">{createViewText}</span>}
        <span className="know-more" onClick={handleKnowMoreClick}>{$i18n.t('we_notable_learn_more')}</span>
      </CreateViewTooltipContent>
    );
  };

  const renderCreateViewBtn = () => {
    const active = addMenuVisibleId === 'tab_create_view';
    if (tabs.length <= 1) {
      return (
        <TextButton icon={<StyledAddM16 />} size="small" active={active}>
          <span className="create-view-btn-text">{createViewText}</span>
        </TextButton>
      );
    }
    return <DefaultCustorIconButton icon={<StyledAddM16 />} active={active} size="extra-small" />;
  };

  return (
    <NavControllerProvider value={navController}>
      <Bar
        data-role="NavBar_ViewNavBar"
        className={classNames({ hasPrefix: !!prefix })}
        ref={scrollController.setNavBarRef}
      >
        {prefix && <Tab className={tabs[0]?.active ? '' : 'split'}>{prefix}</Tab>}
        <Tabs ref={scrollController.setRootRefAndObserveResize}>
          <Shadow
            style={{ width: scrollController.shadowWidth, left: shadowStyle.left }}
            onClick={scrollController.scrollToLeft}
            className={classNames('left', { split: prefix && tabs[0]?.active })}
          >
            <Scroll16 />
          </Shadow>
          <Sortable
            items={tabs}
            onMoveEnd={(...args) => {
              navController.moveTo(...args);
              navController.setOverId(null);
            }}
            onMove={(ev) => {
              viewMenuStore.setPos(null);
              navController.setOverId(ev.over?.id ?? null);
            }}
            horizontalFlowRange={[6, 0]}
          >
            <Scroller
              style={{
                clipPath: `inset(0px ${scrollController.shadowWidth + shadowStyle.right}px 0px ${
                  scrollController.shadowWidth + shadowStyle.left
                }px)`,
              }}
              ref={scrollController.setScrollRef}
              onScroll={() => {
                scrollController.updateScrollerRect();
                viewMenuStore.setPos(null);
              }}
            >
              {tabs.map((item, index) => {
                return (
                  <SortableTab
                    key={item.id}
                    nextActive={tabs[index + 1]?.active === true}
                    id={item.id}
                    active={item.active}
                    store={navController}
                    first={index === 0 && !prefix}
                    disabled={tabs.length === 1 || !!editingItemId}
                  >
                    <ViewItem tab={item} active={item.active} />
                  </SortableTab>
                );
              })}
            </Scroller>
          </Sortable>
          <Shadow
            style={{ width: scrollController.shadowWidth, right: shadowStyle.right }}
            onClick={scrollController.scrollToRight}
            className="split"
          >
            <Scroll16 />
          </Shadow>
        </Tabs>
        <SideContainer
          style={{ left: tabs[tabs.length - 1]?.active && shadowOffset.rightShadow <= 1 ? -6 : 0 }}
        >
          {tabs.length > 1 && <More />}
          {editable && (
            <Tab role="Catalog_New_View">
              <CreateViewPopover store={navController.viewCreateStore} id="tab_create_view">
                <Tooltip placement="bottom" title={renderTooltipTitle()}>
                  {renderCreateViewBtn()}
                </Tooltip>
              </CreateViewPopover>
            </Tab>
          )}
        </SideContainer>
      </Bar>
      <ViewDescEditorModal />
    </NavControllerProvider>
  );
});

const StyledAddM16 = styled(AddM16)`
  color: ${themeCss.color.level2};
`;

const DefaultCustorIconButton = styled(IconButton)`
  cursor: default;
`;
