import { NotableCore } from '@ali/notable-core';
import { Menu, Message, Modal } from '@ali/we-design-next';
import { action, computed, makeObservable, observable } from 'mobx';
import React, { ComponentProps } from 'react';
import $i18n from '@ali/notable-i18n';
import { utils } from '@ali/notable-model';
import { Copy20, Delete20, Edit20, Link20, PersonalViewLine20, Publicview20, Selectcheck16, Info20, Visible20, RightEnterXS12 } from '@ali/we-icons';
import { createEvent, logger, VIEW_LOCKED_MODAL_Z_INDEX, ViewDTO } from '@ali/notable-common';
import { FullWindow16, PopView16, SpliteView16 } from '@ali/we-icons-3';
import { themeCss } from '@ali/notable-design';
import { ViewSwitchMenuItem } from '../../notable-catalog/components/ViewSwitchMenuItem';
import { MoreMenuStore } from '../../../common/MoreMenuBtn/store';

type MenuItems = ComponentProps<typeof Menu>['items'];

interface Pos {
  sheetId: string;
  viewId: string;
  scene: string;
}

export class ViewMenuStore implements MoreMenuStore<Pos> {
  pos: Pos | null = null;
  menuTipMap: Record<string, string> = {};
  dispose: () => void;
  renameEv = createEvent<string>();
  duplicateEv = createEvent<string>();
  openDescEditorEv = createEvent<string>();
  get buttonTip() {
    return $i18n.t('we_notable_guide_view_menu_btn_guide_title');
  }

  get items(): ComponentProps<typeof Menu>['items'] {
    this.menuTipMap = {};
    if (!this.pos) return [];

    const { viewController, abilityManager, globalDataController } = this.core;
    const { sheetId, viewId } = this.pos;
    const items: MenuItems = [];
    const checkAndGenPrivateMenu = () => {
      const canChangeViewPrivatable = viewController.isViewPrivatable(sheetId, viewId);
      if (canChangeViewPrivatable) {
        const [allow, cannotChangeViewPrivateReason] = abilityManager.cannotReason('viewPrivate', sheetId, viewId);
        const isPrivateView = viewController.isPrivateView(sheetId, viewId);
        if (!allow) {
          this.menuTipMap['private-view'] = cannotChangeViewPrivateReason;
        }

        items.push({
          key: 'flags-private',
          title: isPrivateView ? $i18n.t('notable_view_flag_current_private') : $i18n.t('notable_view_flag_current_public'),
          prefix: isPrivateView ? <PersonalViewLine20 /> : <Publicview20 />,
          subMenu: [
            {
              key: 'public-view',
              prefix: <Publicview20 />,
              suffix: !isPrivateView && <Selectcheck16 />,
              title: $i18n.t('notable_view_flag_current_public'),
            },
            {
              key: 'private-view',
              prefix: <PersonalViewLine20 />,
              suffix: isPrivateView && <Selectcheck16 />,
              title: $i18n.t('notable_view_flag_current_private'),
              disabled: !allow,
            },
          ],
        });
        items.push('divider');
      }
    };

    const checkAndGenViewMenu = () => {
      const locked = viewController.isLocked(sheetId, viewId);
      const canChangeProtectionFlags = viewController.isViewProtectable(sheetId, viewId);
      if (canChangeProtectionFlags) {
        items.push({
          key: 'flags-lock',
          content: (
            <ViewSwitchMenuItem
              title={$i18n.t('notable_view_flag_lock')}
              description={$i18n.t('notable_view_flag_lock_tip')}
              first={items.length === 0}
              value={locked}
              onChange={(value: boolean) => {
                if (value) {
                  this.lockView(sheetId, viewId);
                } else {
                  this.unlockView(sheetId, viewId);
                }
              }}
            />
          ),
        });
        items.push('divider');
      }
    };

    const checkAndGenRenameMenu = () => {
      const editable = viewController.isViewRenamable(sheetId, viewId);
      const editDisableTip = editable ? null : abilityManager.cannotReason('viewUpdate', sheetId, viewId)[1];
      if (!editable && editDisableTip) {
        this.menuTipMap.rename = $i18n.t(editDisableTip);
      }

      items.push({
        key: 'rename',
        title: $i18n.t('notable_notableComponents_RenameATable'),
        prefix: <Edit20 />,
        disabled: !editable,
      });
    };

    const checkAndGenDupMenu = () => {
      const canDup = viewController.isViewDuplicable(sheetId, viewId);
      const dupDisableTip = canDup ? null : abilityManager.cannotReason('viewDuplicate', sheetId, viewId)[1];
      if (!canDup && dupDisableTip) {
        this.menuTipMap.duplicate = $i18n.t(dupDisableTip);
      }

      items.push({
        key: 'duplicate',
        title: $i18n.t('notable_notableViewFramework_CopyView'),
        prefix: <Copy20 />,
        disabled: !canDup,
      });
    };

    const checkAndGenDescMenu = () => {
      const editable = viewController.isViewDescEditable(sheetId, viewId);
      const editDisableTip = editable ? null : abilityManager.cannotReason('viewUpdate', sheetId, viewId)[1];
      if (!editable && editDisableTip) {
        this.menuTipMap['edit-description'] = $i18n.t(editDisableTip);
      }

      items.push({
        key: 'edit-description',
        title: $i18n.t('notable_notableViewFramework_EditDesc'),
        prefix: <Info20 />,
        disabled: !editable,
      });
    };

    const checkAndGenDeleteMenu = () => {
      const deletable = viewController.isViewDeletable(sheetId, viewId);
      const deleteDisableTip = deletable ? null : abilityManager.cannotReason('viewDelete', sheetId, viewId)[1];
      if (!deletable && deleteDisableTip) {
        this.menuTipMap.delete = $i18n.t(deleteDisableTip);
      }

      items.push({
        key: 'delete',
        title: $i18n.t('notable_notableViewFramework_DeleteName', { name: $i18n.t('notable_notableViewFramework_View') }),
        prefix: <Delete20 />,
        disabled: !deletable,
      });
    };

    const checkAndGenRecordOpenMethodMenu = () => {
      const { enablePrimaryDoc } = globalDataController.settings;
      if (!enablePrimaryDoc) {
        return;
      }
      const editable = viewController.isViewRecordOpenMethodEditable(sheetId, viewId);
      const editDisableTip = editable ? null : abilityManager.cannotReason('viewUpdate', sheetId, viewId)[1];
      if (!editable && editDisableTip) {
        this.menuTipMap['record-open-method'] = $i18n.t(editDisableTip);
      }
      const currentOpenMethod = viewController.getRecordOpenMethod(sheetId, viewId);
      let currentOpenMethodTitle = $i18n.t('notable_notableComponents_RecordOpenSide_title');
      if (currentOpenMethod === 'modal') {
        currentOpenMethodTitle = $i18n.t('notable_notableComponents_RecordOpenModal_title');
      }
      if (currentOpenMethod === 'full-page') {
        currentOpenMethodTitle = $i18n.t('notable_notableComponents_RecordOpenFullPage_title');
      }
      items.push({
        key: 'record-open-method',
        title: (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{
              textOverflow: 'ellipsis',
              overflow: 'hidden',
              wordBreak: 'keep-all',
              whiteSpace: 'nowrap',
              flex: 1,
            }}
            >{$i18n.t('notable_notableComponents_RecordOpenMethod')}
            </div>
            <div style={{ fontSize: '13px',
              lineHeight: '16px',
              color: themeCss.color.level3,
              left: '16px',
              position: 'relative' }}
            >{currentOpenMethodTitle}
            </div>
          </div>
        ),
        prefix: <Visible20 />,
        subMenu: [
          {
            key: 'record-open-side',
            prefix: <SpliteView16 />,
            suffix: (!currentOpenMethod || currentOpenMethod === 'side') && <Selectcheck16 />,
            title: $i18n.t('notable_notableComponents_RecordOpenSide'),
          },
          {
            key: 'record-open-modal',
            prefix: <PopView16 />,
            suffix: currentOpenMethod === 'modal' && <Selectcheck16 />,
            title: $i18n.t('notable_notableComponents_RecordOpenModal'),
          },
          {
            key: 'record-open-full-page',
            prefix: <FullWindow16 />,
            suffix: currentOpenMethod === 'full-page' && <Selectcheck16 />,
            title: $i18n.t('notable_notableComponents_RecordOpenFullPage'),
          },
        ],
        disabled: !editable,
      });
    };

    checkAndGenPrivateMenu();
    checkAndGenViewMenu();
    checkAndGenRenameMenu();
    checkAndGenDescMenu();
    checkAndGenRecordOpenMethodMenu();
    checkAndGenDupMenu();
    items.push({
      key: 'copy-view-url',
      title: $i18n.t('notable_notableComponents_CopyViewUrl'),
      prefix: <Link20 />,
    });
    items.push('divider');
    checkAndGenDeleteMenu();
    return items;
  }

  constructor(private readonly core: NotableCore) {
    makeObservable(this, {
      items: computed,
      pos: observable.ref,
      setPos: action,
    });
  }

  setPos = (pos: Pos | null) => {
    this.pos = pos;
  };

  handleSelect = (paths: string[]) => {
    if (!this.pos) return;
    const { sheetId, viewId } = this.pos;
    this.setPos(null);
    const key = paths.join();

    if (key === 'delete') return this.delete(sheetId, viewId);
    if (key === 'copy-view-url') return this.copyViewUrl(sheetId, viewId);
    if (key === 'duplicate') return this.duplicate(sheetId, viewId);
    if (key === 'rename') {
      this.renameEv(viewId);
      return;
    }
    if (key === 'flags-private,private-view') return this.toPrivateView(sheetId, viewId);
    if (key === 'flags-private,public-view') return this.toPublicView(sheetId, viewId);
    if (key === 'edit-description') return this.openDescEditorEv(viewId);
    if (key === 'record-open-method,record-open-side') return this.recordOpenMethod(sheetId, viewId, 'side');
    if (key === 'record-open-method,record-open-modal') return this.recordOpenMethod(sheetId, viewId, 'modal');
    if (key === 'record-open-method,record-open-full-page') return this.recordOpenMethod(sheetId, viewId, 'full-page');
  };

  private delete(sheetId: string, viewId: string) {
    if (!this.core.viewController.isViewDeletable(sheetId, viewId)) return;
    const name = this.core.viewController.getName(sheetId, viewId) ?? '';
    const typeName = $i18n.t('notable_notableViewFramework_View');
    const msg = $i18n.t('notable_notableViewFramework_ConfirmDeletion', {
      typeName,
      name,
    });
    const title = $i18n.t('notable_notableViewFramework_DeleteTypename', { typeName });

    Modal.confirm(
      msg,
      {
        mask: true,
        title,
        onConfirm: () => {
          this.core.viewController.delete(sheetId, viewId);
        },
      },
    );
  }

  private copyViewUrl(sheetId: string, viewId: string) {
    const { copyViewUrl } = this.core.services;
    if (copyViewUrl) {
      copyViewUrl({
        sheetId,
        viewId,
      });
    }
  }

  private duplicate(sheetId: string, viewId: string) {
    const result = this.core.viewController.duplicate(sheetId, viewId);
    if (!result) return;
    const op = (Array.isArray(result) ? result : [result]).find(utils.isInsertViewOP);
    if (!op) return;
    const view = op.payload.value;
    logger.info('duplicate_view', 'click', { sheetId, viewId });
    if (view) {
      logger.log('catalog_duplicate', 'view');
      this.duplicateEv(view.id);
    }
  }

  private recordOpenMethod(sheetId: string, viewId: string, method: ViewDTO['recordOpenMethod']) {
    const { viewController } = this.core;
    viewController.setRecordOpenMethod(sheetId, viewId, method);
  }

  private lockView(sheetId: string, viewId: string) {
    const { viewController } = this.core;
    Modal.message(
      $i18n.t('notable_view_flag_lock_content'),
      {
        mask: true,
        zIndex: VIEW_LOCKED_MODAL_Z_INDEX,
        title: $i18n.t('notable_view_flag_lock_title'),
        onConfirm: () => {
          viewController.lock(sheetId, viewId);
          Message.success($i18n.t('notable_view_lock_success'));
        },
      },
    );
  }

  private unlockView(sheetId: string, viewId: string) {
    const { viewController } = this.core;
    Modal.message(
      $i18n.t('notable_view_flag_unlock_content'),
      {
        mask: true,
        zIndex: VIEW_LOCKED_MODAL_Z_INDEX,
        title: $i18n.t('notable_view_flag_unlock_title'),
        onConfirm: () => {
          viewController.unlock(sheetId, viewId);
          Message.success($i18n.t('notable_view_unlock_success'));
        },
      },
    );
  }

  private toPrivateView(sheetId: string, viewId: string) {
    const { globalDataController, viewController } = this.core;
    const { uid } = globalDataController.settings.user || {};
    if (uid) {
      viewController.setPrivateView(sheetId, viewId, uid);
      logger.info('private_view', 'click', { sheetId, viewId });
      Message.success($i18n.t('notable_view_flag_private_content'));
    } else {
      // 仅收集uid为空的情况，早值班不用处理
      logger.warn('private_view', new Error('user uid is empty'));
      Message.error($i18n.t('notable_operation_failed_with_refresh'));
    }
  }

  private toPublicView(sheetId: string, viewId: string) {
    const { globalDataController, viewController } = this.core;
    const { uid } = globalDataController.settings.user || {};
    const { creator } = viewController.getView(sheetId, viewId) || {};
    if (!uid) {
      // 仅收集uid为空的情况，早值班不用处理
      logger.warn('public_view', new Error('user uid is empty'));
      return Message.error($i18n.t('notable_operation_failed_with_refresh'));
    }
    // 历史数据 或 个人视图所有者 才能将视图更改为公开视图
    if (creator === undefined || uid === creator) {
      viewController.setPublicView(sheetId, viewId);
      logger.info('public_view', 'click', { sheetId, viewId });
      Message.success($i18n.t('notable_view_flag_public_content'));
    }
  }
}
