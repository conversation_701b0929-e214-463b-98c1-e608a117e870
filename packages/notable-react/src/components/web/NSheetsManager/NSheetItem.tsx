import { observer } from 'mobx-react-lite';
import React, { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import { TextEllipsis, themeCss } from '@ali/notable-design';
import classNames from 'classnames';
import $i18n from '@ali/notable-i18n';
import { useNotableCore } from '@ali/notable-core';
import { SyncEvents } from '@ali/notable-common';
import { defaultValidator, RenameInput } from '../../common/RenameInput';
import { MoreMenuBtn } from '../../common/MoreMenuBtn';
import { SyncTooltip } from '../notable-catalog/SyncTooltip';
import { Catalog, FlattendCatalog } from './types';
import { NSheetIcon } from './NSheetIcon';
import { indentationWidth, useNSheetsManager } from './store';
import { AddNSheetBtn } from './AddNSheetBtn';
import { DescInfoIcon } from '../../common/DescriptionEditor';

const paddingWidth = 8;

const TitleWraper = styled.div`
  display: flex;
  align-items: center;
  flex: 1 1 auto;
  min-width: 0;
  .info-icon {
    display: flex;
    align-items: center;
    color: ${themeCss.color.level3};
    margin-left: 4px;
  }
`;

const Title = styled(TextEllipsis)`
  margin-left: 8px;
  font-size: 14px;
  color: ${themeCss.color.level1};
  user-select: none;
  &.isEditing {
    width: 100%;
  }
`;

const Container = styled.div`
  display: flex;
  align-items: center;
  border-radius: 6px;
  height: 36px;
  padding: ${paddingWidth}px;
  box-sizing: border-box;
  cursor: default;

  &.grabbing {
    cursor: grabbing;
  }

  &.clone {
    background-color: ${themeCss.color.overlay_light};
    transform: rotate(5deg);
  }

  &.selected {
    color: ${themeCss.color.theme_color_menu_active};
    background: ${themeCss.color.theme_bg_menu_active};
    font-weight: 500;
    .operatorWrap {
      button {
        color: ${themeCss.color.theme_color_menu_active};
      }
    }
    ${Title} {
      color: ${themeCss.color.theme_color_menu_active};
    }
    .hilight {
      color: ${themeCss.color.theme_color_menu_active};
    }
  }

  &.active {
    background-color: ${themeCss.color.overlay_light};
  }

  & .more-menu-btn svg {
    color: ${themeCss.color.level2};
  }
`;

const OperatorWrap = styled.div`
  flex-shrink: 0;
  display: flex;
  align-items: center;
  button {
    color: ${themeCss.color.level3};
  }
`;

const InfoIcon = styled.div`
  display: flex;
  align-items: center;
  color: ${themeCss.color.level3};
  margin-left: 4px;
  cursor: pointer;
`;

interface Props {
  data: FlattendCatalog;
  clone?: boolean;
  onClick?: (data: Catalog) => void;
}

export const NSheetItem = observer((props: Props) => {
  const {
    globalDataController: {
      settings: { readonly },
    },
    event,
    layoutController,
  } = useNotableCore();
  const { data, clone } = props;
  const {
    switchNSheet,
    activeId,
    setEditingData,
    setEditingNameAndRecordData,
    editingNameId,
    rename,
    flattendCatalog,
    nsheetMenuStore,
    setEditingDescData,
  } = useNSheetsManager();
  const { selected, collapsed, synced, type } = data;

  const isEditing = editingNameId === data.id;
  const ref = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (selected || isEditing) {
      ref.current?.scrollIntoView({ block: 'nearest', inline: 'nearest' });
    }
  }, [selected, isEditing]);
  const [hover, setHover] = useState(false);
  const active =
    (nsheetMenuStore.pos === data || nsheetMenuStore.addBtnVisibleId === data.id || hover) &&
    !selected &&
    !activeId;

  useEffect(() => {
    if (ref.current) {
      ref.current.addEventListener('mouseover', () => {
        setHover(true);
      });

      ref.current.addEventListener('mouseleave', () => {
        setHover(false);
      });
    }
  }, []);

  const { sheetStatus } = layoutController;
  const status = sheetStatus[data.id];

  return (
    <Container
      ref={ref}
      data-role={`nsheet_${data.id}`}
      style={{ paddingLeft: data.depth * indentationWidth + paddingWidth }}
      className={classNames({
        clone,
        grabbing: !!activeId,
        selected,
        isEditing,
        active,
      })}
      onClick={() => switchNSheet(data)}
      onDoubleClick={() => {
        if (data.type === 'sheet') {
          setEditingNameAndRecordData(data);
        } else {
          setEditingData(data);
        }
      }}
      onContextMenu={(e) => {
        e.preventDefault();
        nsheetMenuStore.setPos(data);
      }}
    >
      <NSheetIcon
        type={data.type}
        id={data.id}
        collapsed={collapsed}
        className="hilight"
        synced={synced}
      />
      <TitleWraper>
        <Title title={data.title} on={!isEditing} className={classNames({ isEditing })}>
          {isEditing ? (
            <RenameInput
              defaultValue={data.title}
              onCancel={() => setEditingData(null)}
              onSubmit={(value) => {
                rename(data, value);
                setEditingData(null);
              }}
              validator={(value) => {
                if (flattendCatalog.some((item) => item.id !== data.id && item.title === value)) {
                  return $i18n.t('name_duplicated');
                }
                return defaultValidator(value);
              }}
            />
          ) : (
            data.title
          )}
        </Title>
        {type === 'sheet' && data.description && !isEditing && (
          <DescInfoIcon
            title={data.title}
            description={data.description}
            placement="rightTop"
            style={{ padding: 4 }}
            onClick={() => setEditingDescData(data)}
          />
        )}

        {type === 'sheet' && status && !isEditing && (
          <SyncTooltip
            status={status}
            onAction={() => {
              event.emit(SyncEvents.Setting, data.id);
            }}
          />
        )}
      </TitleWraper>
      {(active || selected) && !isEditing && (
        <OperatorWrap className="operatorWrap">
          {data.type === 'section' && !readonly && <AddNSheetBtn data={data} />}
          <MoreMenuBtn
            store={nsheetMenuStore}
            data={data}
            onClick={(e) => e.stopPropagation()}
            tooltipPlacement="right"
            className="more-menu-btn"
          />
        </OperatorWrap>
      )}
    </Container>
  );
});

const StyledTitle = styled(Title)`
  margin-left: 28px;
  margin-bottom: -6px;
  font-size: 12px;
  color: ${themeCss.color.level3};
`;

export const DropTip = (props: { tip: string; depth: number }) => {
  const { tip, depth } = props;
  return (
    <Container style={{ paddingLeft: depth * indentationWidth + paddingWidth, height: 28 }}>
      <StyledTitle title={tip} on>
        {tip}
      </StyledTitle>
    </Container>
  );
};
