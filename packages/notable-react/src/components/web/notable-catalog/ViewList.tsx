import React from 'react';
import { last } from 'lodash-es';
import styled from 'styled-components';
import { observer } from 'mobx-react-lite';
import { themeCss } from '@ali/notable-design';
import { useNotableCore } from '@ali/notable-core';
import i18n from '@ali/notable-i18n';
import type { ViewTypes } from '@ali/notable-common';
import { BadgeTypes, Badge } from '@ali/notable-components';
import { List, Tooltip, Image, Menu } from '@ali/we-design-next';
import { Info16 } from '@ali/we-icons';
import { isDingTalk, openLink } from '@ali/notable-jsapi-adaptor';
import { useCatalogStore } from './CatalogStoreContext';

export interface ViewListProps {
  onAdd: (type: ViewTypes, options?: { subType?: string }) => void;
  sheetId: string;
  onSelect?: () => void;
}

const StyledImage = styled(Image)`
  border-radius: 4px;
  width: 180px;
`;

const HeaderContent = styled.div`
  color: ${themeCss.color.level2};
  margin: 2px 4px;
  padding: 8px 8px 0 8px;
  display: flex;
  cursor: default;
  flex-direction: row;
  line-height: 16px;
  align-items: center;
  gap: 4px;
`;

const LimitFreeWrapper = styled.div`
  display: flex;
  align-items: center;
`;

const openHelpLink = () => {
  const helpUrl =
    'https://alidocs.dingtalk.com/i/p/Y7kmbokZp3pgGLq2/docs/yZvMRzlLwOAWrxzkGmnAJnjY02pBqGox';
  if (isDingTalk()) {
    openLink({ url: helpUrl });
  } else {
    window.open(helpUrl);
  }
};

export const ViewList = observer<ViewListProps>((props) => {
  const { onAdd, sheetId, onSelect } = props;
  const { viewPlugins, core } = useCatalogStore();
  const { globalDataController } = useNotableCore();
  const { enablePrint } = globalDataController.settings;

  const plugins = viewPlugins.filter((vp) => {
    return core.abilityManager.can('viewCreate', sheetId, vp.type) && !vp.disableCreate;
  });

  const items: React.ComponentProps<typeof Menu>['items'] = [
    {
      key: 'header',
      selectable: false,
      content: (
        <Tooltip
          title={i18n.t('we_notable_guide_view_list_learn_more_guide_title')}
          placement="top"
        >
          <HeaderContent data-role={'Catalog_AddView_header_view'} onClick={openHelpLink}>
            <div>{i18n.t('notable_view_type_name')}</div>
            <Info16 />
          </HeaderContent>
        </Tooltip>
      ),
    },
  ];

  for (const viewPlugin of plugins) {
    const Icon = viewPlugin.icon || PlaceholderIcon;
    if (viewPlugin.type !== 'Extension') {
      items.push({
        key: viewPlugin.type,
        title: viewPlugin.typeName,
        prefix: <Icon />,
      });
    }
  }

  const extItems = React.useMemo(() => {
    const extPlugins = plugins.filter((p) => p.type === 'Extension');
    return extPlugins.map((p) => {
      const Icon = p.icon || PlaceholderIcon;
      return {
        key: p.type,
        title: (
          <LimitFreeWrapper>
            {p.typeName}
            <Badge type={BadgeTypes.limitFree} />
          </LimitFreeWrapper>
        ),
        prefix: <Icon />,
      };
    });
  }, [plugins]);

  if (enablePrint && extItems.length) {
    items.push('divider');
    items.push({
      key: 'header',
      selectable: false,
      content: (
        <HeaderContent data-role={'Catalog_AddView_header_extension'} style={{ paddingTop: '4px' }}>
          <div>{i18n.t('pageprint-plugins')}</div>
        </HeaderContent>
      ),
    });
    items.push(...extItems);
  }

  const renderItem = ({ key }: { key: string }) => {
    if (key === 'footer') {
      return (n) => n;
    }
    const viewPlugin = plugins.find((plugin) => plugin.type === key);
    if (!viewPlugin) return (n) => n;
    return (
      <Tooltip
        title={viewPlugin.typeName}
        placement="rightTop"
        description={viewPlugin.description}
        content={viewPlugin.sample ? <StyledImage width="180" src={viewPlugin.sample} /> : null}
      >
        <List.Item data-role={`Catalog_AddView_${viewPlugin.type}`} />
      </Tooltip>
    );
  };

  const handleSelect = (keys: string[]) => {
    const key = last(keys);
    const viewPlugin = plugins.find((plugin) => plugin.type === key);
    if (!viewPlugin) {
      return;
    }
    const { subType } = viewPlugin;
    const options = subType ? { subType } : undefined;
    const goAdd = () => onAdd(viewPlugin.type, options);
    if (viewPlugin.type !== 'Extension') {
      return goAdd();
    }
    const { extensions } = core.viewExtensionController;
    const ext = extensions.find((item) => item.id === subType);
    if (!ext) {
      return goAdd();
    }
    onSelect?.();
    ext.onRegister(goAdd);
  };

  return <Menu items={items} renderItem={renderItem} onSelect={handleSelect} />;
});

const PlaceholderIcon = styled.div`
  display: inline-block;
  width: 20px;
  height: 20px;
  background-color: #eee;
`;
