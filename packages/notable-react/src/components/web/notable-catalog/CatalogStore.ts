import { autorun, makeAutoObservable, runInAction } from 'mobx';
import { get } from 'lodash-es';
import { Datasheet20, DatasheetSynced20 } from '@ali/we-icons';
import $i18n from '@ali/notable-i18n';
import { InsertViewOP } from '@ali/notable-model';
import { Message, Modal } from '@ali/we-design-next';
import {
  Event,
  ApplicationPluginEnum,
  ViewDTO,
  NotableCatalogAppItem,
  NotableCatalogItem,
  NotableCatalogSheetItem,
  NotableCatalogViewItem,
  ViewTypes,
  logger,
  DocumentLimitNum,
  LIMIT_FIELD_NAME_LENGTH,
  ViewFlags,
  VIEW_LOCKED_MODAL_Z_INDEX,
} from '@ali/notable-common';
import { NotableCore, Plugins } from '@ali/notable-core';
import { extractViewId, generateUniqId, generateViewId, safeAsyncExecutor } from './utils';
import { CatalogLayoutStore } from './CatalogLayoutStore';
import { documentCreateSuccessMessage } from './components/documentMessage';

function isView(v: unknown): v is { id: string; name: string; type: string } {
  if (v && typeof v === 'object') {
    return true;
  }
  return false;
}

interface CanDeleteNodeArgs {
  appId?: string;
  viewId?: string;
  sheetId?: string;
}

type CatalogInfo = {
  path: number[];
  item: NotableCatalogViewItem;
  isView: true;
  isApp: false;
  isSheet: false;
} | {
  path: number[];
  item: NotableCatalogAppItem;
  isView: false;
  isApp: true;
  isSheet: false;
} | {
  path: number[];
  item: NotableCatalogSheetItem;
  isView: false;
  isApp: false;
  isSheet: true;
};

export class CatalogStore {
  event = new Event<{ rename: (name: string, id: string) => void }>();
  core: NotableCore<Plugins>;
  layoutStore: CatalogLayoutStore;

  isDragging = false;
  expandedKeyMap: Record<string, boolean> = {};
  editingKey = '';
  editingNameAndRecordId = '';
  searchKeyword = '';

  private readonly disposables: Array<() => void> = [];

  constructor(core: NotableCore<Plugins>, initialExpanded = true) {
    this.core = core;
    this.layoutStore = new CatalogLayoutStore(initialExpanded);
    // 响应 current sheet/view/applicationId 的变化即可，不需要init。
    this.disposables.push(autorun(() => {
      const { currentSheetId: s, currentViewId: v, currentApplicationId: a } = this.core.frameworkController;
      const ids = [s, v, a].filter((id): id is string => !!id);
      ids.forEach((id) => {
        runInAction(() => {
          this.expandedKeyMap[id] = true;
        });
      });
    }));

    makeAutoObservable(this, {
      layoutStore: false,
      core: false,
      event: false,
      dispose: false,
    }, { autoBind: true });
  }

  private addExpandedKey(key: string) {
    this.expandedKeyMap[key] = true;
  }

  private canDelete({ appId, viewId, sheetId }: CanDeleteNodeArgs) {
    if (appId) {
      return this.core.applicationController.getType(appId);
    }
    if (viewId) {
      return this.core.viewController.isViewDeletable(sheetId ?? '', viewId);
    }
    if (sheetId) {
      return this.core.sheetController.isSheetDeletable(sheetId);
    }
  }

  private canRename(id: string, name: string, sheetId?: string) {
    const handleContinue = () => {
      this.setEditingKey(generateUniqId(sheetId, id));
      safeAsyncExecutor(() => {
        this.event.emit('rename', name, id);
      });
    };
    const showSameNameExistsModal = () => {
      Modal.info(
        $i18n.t('notable_notableViewFramework_NameAlreadyExistsEnterA', { name }),
        {
          mask: true,
          title: $i18n.t('notable_notableViewFramework_RenameFailed'),
          onConfirm: handleContinue,
          confirmText: $i18n.t('notable_notableViewFramework_IKnow'),
        },
      );
    };
    // 名称字节长度不得超过 50
    if (name.length > LIMIT_FIELD_NAME_LENGTH) {
      Modal.info($i18n.t('notable_notableViewFramework_TheNameCannotExceedCharacters'), {
        mask: true,
        title: $i18n.t('notable_notableViewFramework_RenameFailed'),
        onConfirm: handleContinue,
        confirmText: $i18n.t('notable_notableViewFramework_IKnow'),
      });
      return false;
    }
    const { catalogItems } = this;
    if (sheetId) {
      const sheet = catalogItems.find((item): item is NotableCatalogSheetItem => item.id === sheetId);
      if (sheet) {
        const sameNameExists = sheet.views.some((item) => item.title === name);
        if (sameNameExists) {
          showSameNameExistsModal();
        }
        return !sameNameExists;
      }
    } else {
      const sameNameExists = catalogItems.some((item) => item.title === name);
      if (sameNameExists) {
        showSameNameExistsModal();
      }
      return !sameNameExists;
    }
    return true;
  }


  get activeKey() {
    const { currentViewId, currentApplicationId } = this.core.frameworkController;
    return currentViewId ?? currentApplicationId ?? '';
  }

  setEditingKey(key: string) {
    this.editingKey = key;
  }

  setEditingNameAndRecordId(key: string) {
    this.editingNameAndRecordId = key;
  }

  initExpandedKeyMap(expandedKeys: string[]) {
    this.expandedKeyMap = expandedKeys.reduce<Record<string, boolean>>((acc, cur) => {
      acc[cur] = true;
      return acc;
    }, {});
  }

  toggleExpandedKey(key: string) {
    this.expandedKeyMap[key] = !this.expandedKeyMap[key];
  }

  setSearchKeyword(kw: string) {
    this.searchKeyword = kw;
  }

  get readonly() {
    return this.core.globalDataController.settings.readonly;
  }

  get isSearching() {
    return !!this.searchKeyword;
  }

  get appPlugins() {
    return this.core.pluginController.applicationPlugins.map((plugin) => {
      return {
        type: plugin.type,
        name: plugin.typeName,
        icon: plugin.icon,
        menuTooltip: plugin.menuTooltip,
      };
    });
  }

  get viewPlugins() {
    return this.core.pluginController.viewPlugins;
  }

  isLoopHistorySheet(type: string, sheetId: string) {
    if (type !== 'sheet') return false;
    return this.core.sheetController.isLoopHistorySheet(sheetId);
  }

  getCatalogIcon(type: string, subType?: string) {
    if (type === 'sheet') {
      if (subType === 'synced') {
        return DatasheetSynced20;
      }
      return Datasheet20;
    }
    const plugin = this.core.pluginController.getViewPlugin(type, subType) ?? this.core.pluginController.getApplicationPlugin(type);
    return plugin?.icon;
  }

  getCatalogInfo(id: string, sheetId?: string): CatalogInfo | undefined {
    const { catalogItems } = this;
    if (sheetId) {
      const sheetIndex = catalogItems.findIndex(
        (item) => item.id === sheetId,
      );
      const sheet = catalogItems[sheetIndex];
      if (sheet?.type === 'sheet') {
        const viewIndex = sheet.views.findIndex((v) => v.id === id);
        if (viewIndex > -1) {
          return {
            path: [sheetIndex, viewIndex],
            item: sheet.views[viewIndex],
            isView: true,
            isApp: false,
            isSheet: false,
          };
        }
      }
    } else {
      const index = catalogItems.findIndex((i) => i.id === id);
      if (index > -1) {
        const item = catalogItems[index];
        if (item.type === 'sheet') {
          return {
            path: [index],
            item,
            isView: false,
            isApp: false,
            isSheet: true,
          };
        } else {
          return {
            path: [index],
            item,
            isView: false,
            isApp: true,
            isSheet: false,
          };
        }
      }
    }
  }

  get searchResultIsEmpty() {
    return this.isSearching && this.filterdCatalogItems.length === 0;
  }

  get filterdCatalogItems() {
    const { catalogItems } = this;
    if (!this.searchKeyword) {
      return catalogItems;
    }
    const regexp = new RegExp(this.searchKeyword, 'i');
    return catalogItems.map((item) => {
      if (item.title.match(regexp)) {
        return item;
      }
      if (item.type === 'sheet') {
        const views = (item as NotableCatalogSheetItem).views.filter((v) => {
          return v.title.match(regexp);
        });
        if (views.length) {
          return { ...item, views };
        }
      }
      return null;
    }).filter((i): i is NotableCatalogItem => !!i);
  }

  setActiveSheet(id: string) {
    const sheet = this.core.sheetController.getSheet(id);
    if (sheet) {
      if (this.core.globalDataController.settings.enableNewIA) {
        this.core.frameworkController.setCurrentSheet(sheet.toLocalJSON());
      } else {
        this.setActiveId(sheet.views[0], id);
      }
    }
  }
  /**
   * 切换当前激活
   */
  setActiveId(id: string, sheetId?: string) {
    const {
      currentSheetId,
      setCurrentView,
      setCurrentApplication,
    } = this.core.frameworkController;
    const { api } = this.core.proxyController;
    if (!sheetId) {
      setCurrentApplication(id);
      return;
    }
    if (sheetId !== currentSheetId) {
      this.setActiveIdToOtherSheet(id, sheetId);
      return;
    }
    const viewId = extractViewId(id);
    const view = api.getSheet(sheetId)?.getView(viewId)?.toLocalJSON();
    if (view) {
      setCurrentView(view);
    }
  }

  private setActiveIdToOtherSheet(id: string, sheetId: string) {
    const {
      currentViewId = '',
      currentSheetId = '',
      setCurrentSheet,
    } = this.core.frameworkController;
    const { api } = this.core.proxyController;
    const sheet = api.getLocalSheet(sheetId);
    if (!sheet) {
      return;
    }
    const viewId = extractViewId(id);
    const doChange = () => setCurrentSheet(sheet, viewId);

    const { viewController, abilityManager } = this.core;
    const localChanges = viewController.localChanges(currentSheetId, currentViewId);
    if (!localChanges.length || !abilityManager.can('viewUpdate', currentSheetId, currentViewId)) {
      doChange();
    } else {
      Modal.message($i18n.t('we_notable_view_update_alert_msg'), {
        mask: true,
        zIndex: VIEW_LOCKED_MODAL_Z_INDEX,
        cancelText: $i18n.t('we_notable_view_update_alert_cancel'),
        confirmText: $i18n.t('we_notable_view_update_alert_save'),
        title: $i18n.t('we_notable_view_update_alert_title'),
        onCancel() {
          viewController.clearTempView(currentSheetId, currentViewId);
          doChange();
        },
        onConfirm() {
          viewController.saveTempView(currentSheetId, currentViewId);
          Message.success($i18n.t('we_notable_view_update_alert_success'));
          doChange();
        },
      });
    }
  }

  get catalogItems() {
    return this.core.frameworkController.catalogItems;
  }

  /**
   * 重命名视图、数据表、仪表盘
   */
  handleRename(id: string, name: string, sheetId?: string) {
    if (this.canRename(id, name, sheetId)) {
      const { catalogItems } = this;
      // 视图重命名
      if (sheetId) {
        const viewId = extractViewId(id);
        this.core.viewController.setName(sheetId, viewId, name);
        logger.log('catalog_rename', 'view');
      } else {
        const index = catalogItems.findIndex((i) => i.id === id);
        if (index !== -1) {
          const item = catalogItems[index];
          if (item.type === 'sheet') {
            this.core.sheetController.setName(id, name);
            logger.log('catalog_rename', 'sheet');
          } else {
            this.core.applicationController.setName(id, name);
            logger.log('catalog_rename', item.appType);
          }
        }
      }
    }
  }

  handleAddView(type: ViewTypes) {
    const { currentSheetId } = this.core.frameworkController;
    if (currentSheetId) {
      const insertViewOP = this.core.viewController.insertTail(currentSheetId, type) as InsertViewOP | undefined;
      const val = insertViewOP?.payload?.value;
      if (val) {
        this.setActiveId(val.id);
      }
    }
  }

  handleDelete(id: string, sheetId?: string) {
    const info = this.getCatalogInfo(id, sheetId);
    if (!info) {
      return;
    }
    const getCanDeleteArgs = (): CanDeleteNodeArgs => {
      if (info.isApp) {
        return { appId: id };
      }
      if (info.isView) {
        return { viewId: id, sheetId };
      }
      if (info.isSheet) {
        return { sheetId: id };
      }
      return {};
    };
    const canDelete = this.canDelete(getCanDeleteArgs());
    if (!canDelete) {
      return;
    }

    const getTypeName = () => {
      if (info.isView) {
        return $i18n.t('notable_notableViewFramework_View');
      } else if (info.isApp) {
        const { item } = info;
        const appPlugin = this.appPlugins.find((p) => p.type === item.appType);
        return appPlugin?.name || '';
      }
      return $i18n.t('notable_notableViewFramework_DataTable');
    };

    const getMsg = (): string => {
      const name = info.item.title;
      if (info.isApp) {
        const { item } = info;
        const appPlugin = this.appPlugins.find((p) => p.type === item.appType);
        if (item.appType === ApplicationPluginEnum.Document) {
          return $i18n.t('notable_notableViewFramework_ConfirmRemove', {
            typeName: appPlugin?.name || '',
            name,
          });
        }
      }
      if (info.isSheet && this.core.sheetController.isAssociated(id)) {
        return $i18n.t('del_sheet_has_dep_tip');
      }
      return $i18n.t('notable_notableViewFramework_ConfirmDeletion', {
        typeName: getTypeName(),
        name,
      });
    };

    const getDeleteTitle = () => {
      if (info.isApp) {
        return $i18n.t('notable_notableViewFramework_Remove', { name: getTypeName() });
      }
      return $i18n.t('notable_notableViewFramework_DeleteTypename', { typeName: getTypeName() });
    };

    Modal.confirm(
      getMsg(),
      {
        mask: true,
        title: getDeleteTitle(),
        onConfirm: () => {
          if (info.isSheet) {
            this.handleDeleteSheet(id, info);
          } else if (info.isView) {
            if (sheetId) {
              this.handleDeleteView(id, sheetId, info);
            }
          } else if (info.isApp) {
            this.handleDeleteApp(id, info);
          }
        },
      },
    );
  }

  /**
   * 删除视图
   */
  private handleDeleteView(id: string, sheetId: string, info: CatalogInfo) {
    const viewId = extractViewId(id);
    this.core.viewController.delete(sheetId, viewId);
    const { currentApplicationId, currentViewId } = this.core.frameworkController;
    const activeId = currentViewId ?? currentApplicationId ?? '';
    if (info.item.id === activeId) {
      const { catalogItems } = this;
      const data = catalogItems[info.path[0]];
      if (data?.type === 'sheet') {
        const { views } = data;
        const newIndex = Math.min(info.path[1], views.length - 1);
        const newViewId = views[newIndex].id;
        this.setActiveId(newViewId, sheetId);
      }
    }
  }

  /**
   * 删除仪表盘
   */
  private handleDeleteApp(id: string, info: CatalogInfo) {
    const { currentApplicationId } = this.core.frameworkController;
    const { catalogItems } = this;
    const { path } = info;
    if (id === currentApplicationId) {
      const newIndex = Math.min(catalogItems.length - 2, path[0]);
      const newActiveItem = catalogItems[newIndex];
      if (newActiveItem.type === 'sheet') {
        const newActiveView = newActiveItem.views[0];
        this.setActiveId(newActiveView.id, newActiveItem.id);
      } else {
        this.setActiveId(newActiveItem.id);
      }
    }
    this.core.applicationController.delete(id);
  }

  /**
   * 删除数据表
   */
  private handleDeleteSheet(id: string, info: CatalogInfo) {
    const { currentSheetId } = this.core.frameworkController;
    const { catalogItems } = this;
    const { path } = info;
    this.core.sheetController.delete(id);
    if (id === currentSheetId) {
      const newIndex = Math.min(catalogItems.length - 2, path[0]);
      const newActiveItem = catalogItems[newIndex];
      if (newActiveItem.type === 'sheet') {
        const newActiveView = newActiveItem.views[0];
        this.setActiveId(newActiveView.id, newActiveItem.id);
      } else {
        this.setActiveId(newActiveItem.id);
      }
    }
  }

  /**
   * 新增数据表
   */
  handleAppendSheet(form = false, parentId?: string) {
    const sheet = form ? this.core.sheetController.insertCollectionSheetToTail(parentId) : this.core.sheetController.insertTail(parentId);
    const ViewItems = sheet.views.reduce<NotableCatalogViewItem[]>((pre, vid) => {
      const view = sheet?.viewMap?.[vid];
      if (view) {
        pre.push({
          sheetId: sheet.id,
          id: vid,
          title: view.name,
          viewType: view.type,
        });
      }
      return pre;
    }, []);
    const sheetItem: NotableCatalogSheetItem = {
      id: sheet.id,
      title: sheet.name,
      type: 'sheet',
      views: ViewItems,
    };

    this.setActiveId(sheetItem.views[0].id, sheetItem.id);
    this.addExpandedKey(sheetItem.id);
    if (form) {
      this.setEditingKey(sheetItem.id);
    } else {
      this.setEditingNameAndRecordId(sheetItem.id);
    }
  }

  /**
   * 新增仪表盘
   */
  handleAppendDashboard(parentId?: string) {
    const dashboard = this.core.applicationController.appendDashboard(parentId);
    if (!dashboard) {
      return null;
    }
    this.setActiveId(dashboard.id);
    this.setEditingKey(dashboard.id);
    logger.log('add_app', 'Dashboard');
  }

  /**
   * 生成视图名称
   */
  handleGenerateViewTitle(sheetId: string, type: ViewTypes) {
    return this.core.viewController.generateViewTitle(sheetId, type);
  }

  /**
   * 新建视图
   */
  handleInsertView(sheetId: string, type: ViewTypes, options?: {
    name?: string;
    flags?: ViewFlags;
    editing?: boolean;
  }) {
    const result = this.core.viewController.insertTail(sheetId, type, options);
    if (result) {
      const value = get(result as InsertViewOP, 'payload.value', null);
      if (value) {
        const view: NotableCatalogViewItem = {
          sheetId,
          id: value.id,
          viewType: type,
          title: value.name,
        };
        this.setActiveId(view.id, sheetId);
        this.addExpandedKey(sheetId);
        options?.editing && requestAnimationFrame(() => {
          this.setEditingKey(generateUniqId(sheetId, view.id));
        });
        logger.log('add_view', type);
      }
    }
    return null;
  }

  handleDuplicate(id: string, sheetId?: string, sheetName?: string, onlyMeta?: boolean) {
    if (sheetId) {
      const view = this.handleDuplicateView(id, sheetId);
      if (view) {
        this.setEditingKey(generateUniqId(sheetId, view.id));
      }
      logger.log('catalog_duplicate', 'view');
      return;
    }
    const { catalogItems } = this;
    const item = catalogItems.find((i) => i.id === id);
    if (!item) {
      logger.warn('catalog_duplicate', new Error('target not found'));
      return;
    }
    if (item.type === 'sheet') {
      if (!sheetName) return;
      const sheet = this.handleDuplicateSheet(id, sheetName, onlyMeta);
      if (sheet) {
        this.setEditingKey(sheet.id);
      }
      logger.log('catalog_duplicate', 'sheet');
      return;
    }
    if (item.type === 'app' && item.appType === ApplicationPluginEnum.Dashboard) {
      this.handleDuplicateDashboard(id);
      logger.log('catalog_duplicate', 'Dashboard');
    }
  }

  /**
   * 复制视图
   */
  handleDuplicateView(id: string, sheetId: string) {
    const viewId = extractViewId(id);
    const result = this.core.viewController.duplicate(sheetId, viewId);
    const payload = (result && ('payload' in result)) ? result.payload : undefined;
    const view = (payload && ('view' in payload)) ? payload.view : undefined;
    logger.info('duplicate_view', 'click', { sheetId, viewId });

    if (isView(view)) {
      const newView: NotableCatalogViewItem = {
        sheetId,
        id: generateViewId(sheetId, view.id),
        title: view.name,
        viewType: (view as ViewDTO).type,
      };
      this.setActiveId(newView.id, sheetId);
      return newView;
    }
    return null;
  }

  /**
   * 复制工作表
   */
  handleDuplicateSheet(id: string, name: string, onlyMeta?: boolean) {
    const result = this.core.sheetController.duplicate(id, name, onlyMeta);
    const sheet = result?.payload?.value;
    if (sheet) {
      this.setActiveId(sheet.views[0], sheet.id);
      const sheetItem: NotableCatalogSheetItem = {
        id: sheet.id,
        type: 'sheet',
        views: (sheet.views || []).reduce((per, cur) => {
          const view = sheet.viewMap[cur];
          if (view) {
            per.push({
              id: generateViewId(sheet.id, view.id),
              title: view.name,
              viewType: view.type,
              sheetId: sheet.id,
            });
          }
          return per;
        }, [] as NotableCatalogViewItem[]),
        title: sheet.name,
      };
      return sheetItem;
    }
    return null;
  }

  /**
   * 复制仪表盘
   */
  handleDuplicateDashboard(id: string) {
    const dashboard = this.core.applicationController.duplicateDashboard(id);
    if (!dashboard) {
      Message.warning($i18n.t('notable_catalog_copy_failed'));
      return null;
    }

    const dashboardItem: NotableCatalogAppItem = {
      type: 'app',
      appType: ApplicationPluginEnum.Dashboard,
      id: dashboard.id,
      title: dashboard.name,
    };

    Message.success($i18n.t('notable_catalog_copy_success'));
    this.setActiveId(dashboard.id);
    return dashboardItem;
  }

  /**
   * 移动视图
   */
  handleMoveView(sheetId: string, id: string, targetIndex: number) {
    const viewId = extractViewId(id);
    this.core.viewController.move(sheetId, viewId, targetIndex);
  }

  /**
   * 移动数据表
   */
  handleMoveSheet(sheetId: string, targetIndex: number) {
    this.core.sheetController.move(sheetId, targetIndex);
  }

  get exceededDocumentLimit() {
    return this.catalogItems.filter((it) => {
      return it.type === 'app' && it.appType === ApplicationPluginEnum.Document;
    }).length >= DocumentLimitNum;
  }

  private loading = false;

  async appendDocument(parentId?: string) {
    logger.log('add_app', 'Document');
    if (this.loading) return;
    this.loading = true;
    const closeLoading = Message.loading($i18n.t('notable_notableViewFramework_DocumentCreateLoading'));
    const { success, docItem, message } = await this.core.applicationController.appendDocument(parentId);
    closeLoading();
    this.loading = false;
    if (!success || !docItem) {
      Message.error($i18n.t(message || ''));
      return;
    }
    documentCreateSuccessMessage();
    this.setActiveId(docItem.id);
  }

  getDocumentItem(id: string) {
    return this.core.applicationController.documentController.getDocumentById(id);
  }

  /**
   * 收起
   */
  handleCollapse() {
    this.layoutStore.setCatalogExpanded(false);
  }

  onDragStart() {
    this.isDragging = true;
  }

  onDragEnd() {
    this.isDragging = false;
  }

  matchedTitle(title: string) {
    if (typeof title !== 'string') return '';
    const keyword = this.searchKeyword.trim();
    if (keyword) {
      const regexp = new RegExp(keyword, 'i');
      const strings = title.split(regexp);
      let result: Array<{ text: string; highlight?: boolean }> = [];
      for (let i = 0; i < strings.length; i++) {
        result.push({ text: strings[i] });
        if (i !== strings.length - 1) {
          result.push({ text: keyword, highlight: true });
        }
      }
      result = result.filter((r) => !!r.text);
      return result;
    }
    return title;
  }

  dispose() {
    this.disposables.forEach((d) => d());
  }
}
