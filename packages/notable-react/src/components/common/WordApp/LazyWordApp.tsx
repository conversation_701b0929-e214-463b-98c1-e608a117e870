/* eslint-disable @typescript-eslint/no-explicit-any */
import loadable, { LoadableComponent } from '@loadable/component';
import { loadRemote } from '@module-federation/runtime';

type PromiseUnknownToAny<T> = Promise<unknown> extends T ? any : T;

type LoadableUnknownToAny<T> = LoadableComponent<unknown> extends T ? LoadableComponent<any> : T;

function wrapRemote<T>(remote: T): PromiseUnknownToAny<T> {
  return remote as PromiseUnknownToAny<T>;
}

function wrapLoadableComponent<T>(loadableComponent: T): LoadableUnknownToAny<T> {
  return loadableComponent as LoadableUnknownToAny<T>;
}

export const LazyH5WordApp = wrapLoadableComponent(loadable(
  () => wrapRemote(loadRemote('we_word_editor/h5Editor')),
));

export const LazyWordApp = wrapLoadableComponent(loadable(
  () => wrapRemote(loadRemote('we_word_editor/pcEditor')),
));

export const LazyHistoryApp = wrapLoadableComponent(loadable(
  () => wrapRemote(loadRemote('we_word_editor/history')),
));

export const LazyWordAppProvider = wrapLoadableComponent(loadable(
  () => wrapRemote(loadRemote('we_word_editor/provider')),
));

let wordUtils;

export const getWordUtils = async (): Promise<any> => {
  if (wordUtils) {
    return wordUtils;
  }
  const utils = await loadRemote('we_word_editor/utils') as any;
  wordUtils = utils;
  return utils;
};

export const LazyCommentSidebarApp = wrapLoadableComponent(loadable(
  () => wrapRemote(loadRemote('we_word_editor/commentSidebar')),
));

export const CommentSidebarProvider = wrapLoadableComponent(loadable(async () => {
  const { CommentSidebarProvider: Provider } = await loadRemote('we_word_editor/commentSidebar') as any;
  return Provider;
}));
