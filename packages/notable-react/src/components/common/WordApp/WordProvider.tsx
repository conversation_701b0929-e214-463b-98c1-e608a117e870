/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { PropsWithChildren, useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { useNotableCore } from '@ali/notable-core';
import { LazyWordAppProvider, getWordUtils } from './LazyWordApp';
import { cache3in1DataStore } from './cache3in1Data';

export const WordProvider = observer((props: PropsWithChildren<any> & { initBroker?: (broker) => void }) => {
  const notableCore = useNotableCore();
  const { initBroker } = props;
  const { globalDataController, services, primaryDocCommentController } = notableCore;
  const { accessToken } = globalDataController.fileInfo;
  const { renewAuthToken, getLwpClient } = services;
  const [broker, setBroker] = useState<any>(null);

  useEffect(() => {
    getWordUtils().then((utils) => {
      const _broker = utils.createHostBroker('host');
      primaryDocCommentController.setBroker(_broker);
      setBroker(_broker);
      initBroker?.(_broker);
    });
  }, [initBroker, primaryDocCommentController]);

  if (!broker) {
    return null;
  }
  return (
    <LazyWordAppProvider
      getToken={() => {
        return accessToken;
      }}
      onTokenExpired={renewAuthToken}
      broker={broker}
      isMobile={false}
      lwp={() => {
        return new Promise((resolve) => {
          const client = getLwpClient?.();
          resolve(client);
        });
      }}
      requestCoreData={async (dentryUuid: string) => {
        const result = await cache3in1DataStore.fetch3in1Data(dentryUuid || '', services);
        return result;
      }}
    >
      {props.children}
    </LazyWordAppProvider>
  );
});
