module.exports = [
  'we_notable_learn_more',
  'ai_analyze_recommend_creation',
  'new_content_generated_by_ai',
  'rename_not_supported',
  'moved_to',
  'expand_sidebar',
  'notable_recommend',
  'create_from_empty',
  'table',
  'notable_notableViewFramework_TheCurrentVersionOfThe',
  'notable_notableViewFramework_PleaseRefreshProcessing',
  'notable_notableViewFramework_TheDataHasBeenSaved',
  'notable_notableViewFramework_DocumentError',
  'notable_notableViewFramework_Refresh',
  'notable_notableViewFramework_NoViewCurrentlyAvailable',
  'notable_notableViewFramework_HiddenFields',
  'notable_notableViewFramework_TheNameCannotExceedCharacters',
  'notable_notableViewFramework_RenameFailed',
  'notable_notableViewFramework_IKnow',
  'notable_notableViewFramework_NameAlreadyExistsEnterA',
  'notable_notableFormView_FormdataitemnameIsARequiredField',
  'notable_catalog_no_result',
  'notable_catalog_search_change_keyword_message',
  'notable_notableViewFramework_DataTable',
  'notable_notableViewFramework_View',
  'notable_notableViewFramework_ConfirmDeletion',
  'notable_notableViewFramework_DeleteTypename',
  'notable_catalog_copy_failed',
  'notable_catalog_copy_success',
  'notable_component_copy_success',
  'notable_notableGridView_CopyRecordUrl',
  'we_notable_share_record_to_chat',
  'notable_layout_show_siderbar',
  'notable_notableViewFramework_CreateAView',
  'notable_layout_hide_siderbar',
  'notable_common_search',
  'notable_sheet_type_name',
  'notable_dashboard_type_name',
  'notable_view_type_name',
  'notable_notableComponents_RenameATable',
  'notable_notableViewFramework_CopyName',
  'notable_notableViewFramework_CopyView',
  'notable_notableViewFramework_DeleteName',
  'notable_common_add',
  'notable_common_more_action',
  'notable_biz_advanced_permission_sheet_no_permission_tip',
  'notable_notableViewFramework_TheDataTableHasBeen',
  'notable_biz_advanced_permission_record_no_permission_tip',
  'notable_notableViewFramework_TheRecordHasBeenDeleted',
  'notable_notableViewFramework_NoDataTableIsCurrently',
  'notable_notableViewFramework_CreateADataTable',
  'notable_notableViewFramework_EditThisFieldOnThe',
  'notable_notableViewFramework_Cancel',
  'notable_notableViewFramework_Ok',
  'notable_notableViewFramework_DeletedSuccessfully',
  'notable_notableViewFramework_FailedToDelete',
  'notable_notableViewFramework_EditFieldsColumns',
  'notable_notableViewFramework_CopyFieldsColumns',
  'notable_notableViewFramework_SortByAToZ',
  'notable_notableViewFramework_SortByZToA',
  'notable_notableViewFramework_Press',
  'notable_notableViewFramework_Filter',
  'notable_notableViewFramework_InsertFieldsColumnsToThe',
  'notable_notableViewFramework_ModifyStatisticalMethods',
  'notable_notableViewFramework_DeleteFieldsColumns',
  'notable_notableViewFramework_PreviewIsNotSupportedFor',
  'notable_notableViewFramework_Dashboard',
  'notable_notableViewFramework_Rename',
  'notable_notableViewFramework_EnterAName',
  'notable_notableViewFramework_CopiedSuccessfully',
  'notable_notableViewFramework_CreatedSuccessfully',
  'notable_notableViewFramework_CopyTypename',
  'notable_notableViewFramework_Delete',
  'notable_notableViewFramework_SelectView',
  'notable_notableViewFramework_DataTableCount',
  'notable_notableViewFramework_DeleteData',
  'notable_notableViewFramework_ConfirmDeleteData',
  'notable_notableViewFramework_MoreOperations',
  'notable_notableViewFramework_DeleteARecord',
  'notable_notableViewFramework_FieldManagement',
  'notable_notableViewFramework_AddField',
  'notable_notableViewFramework_TheFirstColumnIsThe',
  'notable_notableViewFramework_TheAssociatedDataTableCannot',
  'notable_notableViewFramework_YouDoNotHavePermission',
  'notable_notableViewFramework_TheFieldNameCannotExceed',
  'notable_notableViewFramework_TheFieldNameAlreadyExists',
  'notable_notableViewFramework_OperationSucceeded',
  'notable_notableViewFramework_ColumnTypeConversion',
  'notable_notableComponents_ChangeFieldTypeToPerson',
  'notable_notableComponents_ChangeFieldTypeToPerson_desc',
  'notable_notableComponents_ChangeFieldType_Converting',
  'notable_notableComponents_ChangeFieldType_LimitFailed',
  'notable_notableComponents_extendField_converting_tip',
  'notable_notableComponents_ChangeFieldType_selectNewField',
  'notable_notableComponents_ChangeFieldType_canNotCreateNewField',
  'notable_notableComponents_ChangeFieldType_ConvertFailed',
  'notable_notableComponents_ChangeFieldTypeToPerson_PartialFailed',
  'notable_notableViewFramework_ThisOperationMayClearExisting',
  'notable_notableViewFramework_ModifyFields',
  'notable_notableViewFramework_Title',
  'notable_notableViewFramework_EnterAFieldTitle',
  'notable_notableViewFramework_FieldType',
  'notable_notableViewFramework_ThisFieldIsNotSupported',
  'notable_notableViewFramework_Conventional',
  'notable_notableViewFramework_Advanced',
  'notable_notableViewFramework_TheSystemAutomaticallyFillsIn',
  'notable_notableViewFramework_SubmitForm',
  'notable_notableViewFramework_Format',
  'notable_notableViewFramework_DisplayFormat',
  'notable_notableViewFramework_SelectDisplayFormat',
  'notable_notableViewFramework_DoNotFilter',
  'notable_notableViewFramework_AssociatedDataTable',
  'notable_notableViewFramework_SelectDataTable',
  'notable_notableViewFramework_SelectATableToAssociate',
  'notable_notableViewFramework_NumberOfRecords',
  'notable_notableViewFramework_AllowMultipleRecordsToBe',
  'notable_components_allowSelectMultiDepartment',
  'notable_notableViewFramework_NumberOfDepartments',
  'notable_notableViewFramework_FilterRecordsFromView',
  'notable_notableViewFramework_SelectFilterView',
  'notable_notableViewFramework_Options',
  'notable_notableViewFramework_AddOptions',
  'notable_notableViewFramework_EnterOptions',
  'notable_notableGridView_NotDisplayed',
  'notable_notableGridView_NotSpecified',
  'notable_notableGridView_Entered',
  'notable_notableGridView_UniqueValue',
  'notable_notableGridView_PercentageNotSpecified',
  'notable_notableGridView_CompletedPerCent',
  'notable_notableGridView_PercentageOfUniqueValues',
  'notable_notableGridView_TotalRecords',
  'notable_notableGridView_Summation',
  'notable_notableGridView_Average',
  'notable_notableGridView_MaximumValue',
  'notable_notableGridView_MinimumValue',
  'notable_notableGridView_GouXuan',
  'notable_notableGridView_NotGouXuan',
  'we_notable_confirm_deletion_of_field_with_extra_info',
  'we_notable_restore_original_field_by_undoing',
  'notable_notableComponents_DeleteAFieldOrColumn',
  'notable_notableViewFramework_NumberOfMentions',
  'notable_notableViewFramework_Remove',
  'notable_notableViewFramework_ConfirmRemove',
  'notable_notableViewFramework_DocumentCreateSuccess',
  'notable_notableViewFramework_DocumentCreateLoading',
  'notable_notableViewFramework_NetworkError',
  'notable_notableViewFramework_DocumentCreateLimit',
  'notable_notableViewFramework_noMoreTip',
  'notable_components_allowSelectMultiMention',
  'we_notable_partition_sheet_edit_disabled',
  'notable_NotableReact_InvalidNameDetail',
  'notable_sheet_name_info_title',
  'notable_notableFields_MultiSelect',
  'notable_components_attachmentConfigTitle',
  'notable_components_attachmentOnlyCamera',
  'notable_notableComponents_progress_color',
  'notable_notableComponents_progress_customize',
  'notable_notableComponents_progress_min',
  'notable_notableComponents_progress_max',
  'notable_notableComponents_NumberFormat',
  'notable_notableComponents_progress_precision',
  'notable_notableComponents_AddressType1',
  'notable_notableComponents_AddressType2',
  'notable_components_format',
  'notable_notableFormView_Privacy_Field_Tip',
  'notable_notableFormView_FieldIsRequired',
  'notable_form_setting_partition_historySheet',
  'notable_form_setting_partition_help_link',
  'notable_view_flag',
  'notable_view_flag_collab_tip',
  'notable_view_flag_lock',
  'notable_view_flag_lock_tip',
  'collection',
  'new',
  'notable_biz_advanced_permission_not_auth_tips',
  'we_notable_titleEditor_empty_tip',
  'the_name_exist',
  'the_sheet_name',
  'notable_notableComponents_Enter',
  'the_copy_range',
  'only_copy_meta',
  'copy_all_data',
  'notable_notableApplicationPlugins_WnameCopy',
  'del_sheet_has_dep_tip',
  'notable_notableFormView_FieldInvalid',
  'we_notable_submitted_records_hidden',
  'notable_biz_advanced_permission_need_submit_new_record',
  'we_notable_common_submit',
  'we_notable_schedule_remind_disabled_mouse_tip',
  'notable_notableViewFramework_NumberOfGroups',
  'notable_components_allowSelectMultiGroup',
  'notable_notableViewPlugins_TheRecordHasBeenDeleted',
  'notable_notableComponents_OpenInSplitView',
  'notable_notableComponents_CopyViewUrl',
  'notable_notableViewFramework_CopyThisFieldOnThe',
  'we_notable_export_as',
  'we_notable_export_ad_excel',
  'we_notable_export_ad_zip',
  'sync_setting',
  'sync_sync',
  'sync_last_update',
  'sync_unsync',
  'we_notable_cancel',
  'notable_view_flag_current_private',
  'notable_view_flag_current_public',
  'notable_please_enter_view_name',
  'notable_who_can_see_this_view',
  'notable_visible_to_only_me',
  'notable_create_view',
  'notable_view_name',
  'notable_field_barcode_enter_mode',
  'notable_field_barcode_only_mobile_scan',
  'we_notable_field_flow_cancel_tip',
  'we_notable_field_flow_must_fill_tip',
  'we_notable_field_flow_return_node_state',
  'we_notable_field_flow_cancel',
  'we_notable_field_flow_cancel_node',
  'we_notable_field_flow_has_cancelled',
  'we_notable_field_flow_recover',
  'we_notable_field_flow_node_has_done',
  'we_notable_field_flow_cancel_drawer_title',
  'we_notable_field_flow_cancel_confirm_tip',
  'we_notable_field_flow_done_node',
  'notable_notableComponents_extendField_multiSelect_tip',
  'notable_notableComponents_Prompt',
  'notable_notableComponents_Delete',
  'notable_notableComponents_extendField_source_title',
  'notable_notableComponents_extendField_source_invalid_tip',
  'notable_notableComponents_extendField_no_permission_tip',
  'notable_notableComponents_extendField_source_convert',
  'notable_notableComponents_extendField_source_error_multi_person',
  'notable_notableComponents_extendField_no_permission_prefix',
  'notable_form_ref_options_hint',
  'we_notable_data_synchronizing',
  'we_notable_data_synchronization_success',
  'we_notable_data_synchronization_fail',
  'notable_notableComponents_extendField_source_refresh',
  'view_setting',
  'notable_view_flag_lock_hover_tip',
  'notable_view_flag_private_hover_tip',
  'we_notable_field_already_hidden',
  'notable_notableComponents_Search',
  'we_notable_no_search_results',
  'we_notable_view_update_alert_msg',
  'we_notable_view_update_alert_cancel',
  'we_notable_view_update_alert_save',
  'we_notable_view_update_alert_title',
  'we_notable_view_update_alert_success',
  'all_view',
  'search_view',
  'import_or_sync_data',
  'not_allow_the_characters',
  'not_allow_empty',
  'name_count_limit',
  'section',
  'drag_to_move_position',
  'name_duplicated',
  'move_section_not_allowed',
  'disable_del_only_one_sheet',
  'drag_here_tip',
  'connot_move_to_section',
  'read_only_disable_rename',
  'move_to',
  'no_section_can_move',
  'connot_move_to_sub_section',
  'contain_section_connot_move',
  'connot_move_to_self',
  'we_notable_guide_view_menu_btn_guide_title',
  'we_notable_guide_create_new_doc_guide_title',
  'we_notable_guide_create_new_form_guide_title',
  'we_notable_guide_view_list_learn_more_guide_title',
  'we_notable_guide_create_new_section_guide_title',
  'notable_view_flag_lock_content',
  'notable_view_flag_lock_title',
  'notable_view_lock_success',
  'notable_view_flag_unlock_content',
  'notable_view_flag_unlock_title',
  'notable_view_unlock_success',
  'notable_view_flag_private_content',
  'notable_operation_failed_with_refresh',
  'notable_view_flag_public_content',
  'notable_view_nav_guide_desc',
  'sheet_desc',
  'sheet_desc_placeholder',
  'notable_notableComponents_EditTableDesc',
  'notable_notableViewFramework_EditDesc',
  'view_desc',
  'view_desc_placeholder',
  'update_desc_not_supported',
  'record_alias_name',
  'record_name_alias_tip_title',
  'record_unnamed_alias',
  'read_only_disable_update_desc',
  'notable_record_alias_name_helper',
  'notable_record_alias_name_setting_title',
  'read_only_disable_update_sheet',
  'update_sheet_not_supported',
  'pageprint-plugins',
  'notable_record_alias_name_setting_guide_title',
  'notable_record_alias_name_setting_guide_desc',
  'notable_notableFormView_DefaultValue',
  'notable_notableComponents_RecordOpenMethod',
  'notable_notableComponents_RecordOpenModal',
  'notable_notableComponents_RecordOpenSide',
  'notable_notableComponents_RecordOpenFullPage',
  'notable_notableComponents_RecordOpenModal_title',
  'notable_notableComponents_RecordOpenSide_title',
  'notable_notableComponents_RecordOpenFullPage_title',
  'notable_unnamed_document',
  'notable_primary_upgrade_tip_content',
  'notable_primary_upgrade_tip_title',
  'notable_primary_upgrade_tip_cancelText',
  'notable_primary_upgrade_tip_confirmText',
  'we_notable_record_primary_doc_placeholder',
];
