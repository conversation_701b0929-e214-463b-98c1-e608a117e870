{"name": "@ali/notable-react", "version": "0.36.0", "main": "src/index.tsx", "sideEffects": ["./src/locale/*.ts", "./locale/*.js"], "publishConfig": {"registry": "https://registry.anpm.alibaba-inc.com"}, "author": "alidocs panda", "scripts": {"reinstall": "rm -rf node_modules pnpm-lock.yaml && pnpm i", "clean": "rm -rf pkg build", "start": "we start-storybook -p 9000", "build-storybook": "we build-storybook", "build": "we build-component", "test": "we jest", "deploy": "we deploy-pages -d build/ -b demo -m 'deloy. to #xxx'", "site": "pnpm build-storybook && pnpm deploy", "typechecking": "we typechecking", "pub": "npm run build && tnpm publish pkg", "coverage": "we jest --coverage --passWithNoTests", "lint": "we eslint --quiet && we stylelint", "update-i18n": "update-i18n ./localesKeys.js ./src/i18nResources -w 'zh_CN|en_US|zh_TW|zh_HK|ja_JP' --moreLangs ko_KR,tr_TR,pt_BR,th_TH,id_ID,ms_MY -l ts -n notable-sdk"}, "dependencies": {"@ali/we-design-3": "0.4.0", "@ali/notable-common": "0.36.0", "@ali/notable-components": "0.36.0", "@ali/notable-core": "0.36.0", "@ali/notable-design": "0.36.0", "@ali/notable-field-plugins": "0.36.0", "@ali/notable-i18n": "0.36.0", "@ali/notable-jsapi-adaptor": "0.36.0", "@ali/notable-model": "0.36.0", "@ali/we-design-token": "0.7.0", "@ali/we-icons": "0.0.6-beta.aba4d0.202506171637", "@ali/we-file-icons": "0.0.6-beta.aba4d0.202506171637", "@ali/we-icons-3": "0.0.6-beta.aba4d0.202506171637", "classnames": "^2.2.1", "dayjs": "1.10.6", "react-beautiful-dnd": "^13.1.0", "mobx-react-lite": "3.4.3", "dingtalk-design-mobile": "2.6.4", "react-sortable-hoc": "^2.0.0", "resize-observer-polyfill": "^1.5.1", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@module-federation/runtime": "0.14.3"}, "devDependencies": {"@types/lodash-es": "4.17.7", "babel-jest": "27.5.1", "@types/react-beautiful-dnd": "^13.0.0", "@types/styled-components": "^5.1.25"}, "peerDependencies": {"@ali/we-util": "0.3.60-rc.56", "@babel/runtime": "7.x", "ahooks": "3.7.6", "lodash-es": "*", "react": "17.x", "react-dom": "17.x", "styled-components": "^5.2.1"}}