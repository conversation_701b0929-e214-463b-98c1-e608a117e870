{"name": "@ali/notable-view-plugins", "version": "0.36.0", "main": "src/index.ts", "sideEffects": ["./src/locale/*.ts", "./locale/*.js"], "publishConfig": {"registry": "https://registry.anpm.alibaba-inc.com"}, "author": "alidocs panda", "scripts": {"reinstall": "rm -rf node_modules pnpm-lock.yaml && pnpm i", "clean": "rm -rf pkg build", "start": "we start-storybook -p 9000", "build-storybook": "we build-storybook", "build": "we build-component  && cp -r assets pkg/assets", "test": "echo test", "deploy": "we deploy-pages -d build/ -b demo -m 'deloy. to #xxx'", "site": "pnpm build-storybook && pnpm deploy", "typechecking": "we typechecking", "pub": "pnpm build && tnpm publish pkg", "coverage": "we jest --coverage --passWithNoTests", "update-i18n-resource": "update-i18n ./src/$VIEW/localesKeys.js ./src/$VIEW/i18nResources -w 'zh_CN|en_US|zh_TW|zh_HK|ja_JP' --moreLangs ko_KR,tr_TR,pt_BR,th_TH,id_ID,ms_MY -l ts -n notable-sdk", "i18n-calendar": "VIEW=calendar pnpm update-i18n-resource", "i18n-kanban": "VIEW=kanban pnpm update-i18n-resource", "i18n-gallery": "VIEW=gallery pnpm update-i18n-resource", "i18n-form": "VIEW=form pnpm update-i18n-resource", "i18n-gantt": "VIEW=gantt pnpm update-i18n-resource", "i18n-grid": "VIEW=grid pnpm update-i18n-resource", "update-i18n": "pnpm i18n-calendar && pnpm i18n-kanban && pnpm i18n-gallery && pnpm i18n-form && pnpm i18n-gantt && pnpm i18n-grid", "lint": "we eslint --quiet && we stylelint"}, "dependencies": {"@ali/notable-common": "0.36.0", "@ali/notable-components": "0.36.0", "@ali/notable-core": "0.36.0", "@ali/notable-design": "0.36.0", "@ali/notable-field-plugins": "0.36.0", "@ali/notable-i18n": "0.36.0", "@ali/notable-jsapi-adaptor": "0.36.0", "@ali/notable-model": "0.36.0", "@ali/notable-react": "0.36.0", "@ali/we-assets": "0.0.6-beta.aba4d0.202506171637", "@ali/we-design-next": "0.51.1", "@ali/we-design-token": "0.7.0", "@ali/we-design-3": "0.3.0", "@ali/we-icons": "0.0.6-beta.aba4d0.202506171637", "@ali/we-icons-3": "0.0.6-beta.aba4d0.202506171637", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "classnames": "^2.2.1", "copy-to-clipboard": "3.3.3", "dayjs": "1.10.6", "dingtalk-design-mobile": "2.6.4", "dnd-core": "14.0.0", "jotai": "^1.8.5", "konva": "^8.4.2", "lodash-es": "^4.17.21", "mobx": "6.12.0", "mobx-react-lite": "3.4.3", "rc-resize-observer": "^1.0.0", "react-dnd": "14.0.0", "react-dnd-html5-backend": "14.0.0", "react-dnd-touch-backend": "14.0.0", "react-image-crop": "^9.0.4", "react-konva": "17.0.2-6", "react-textarea-autosize": "^8.3.3", "react-virtualized": "^9.22.3", "react-virtualized-auto-sizer": "^1.0.7", "react-window": "^1.8.8", "resize-observer-polyfill": "^1.5.1", "styled-normalize": "^8.0.7", "swiper": "^8.3.1", "use-image": "^1.1.0"}, "devDependencies": {"@babel/core": "^7.14.3", "@testing-library/react": "10.0.4", "@types/faker": "^5.1.3", "@types/lodash-es": "4.17.7", "@types/react": "17.x", "@types/react-dom": "17.x", "@types/react-virtualized": "^9.21.12", "@types/react-virtualized-auto-sizer": "^1.0.1", "@types/react-window": "^1.8.5", "@types/styled-components": "^5.1.25", "faker": "^5.1.0", "lint-staged": "^11.0.0", "prettier": "2.2.1"}, "peerDependencies": {"@babel/runtime": "7.x", "ahooks": "3.7.6", "jotai": "1.8.5", "lodash-es": "*", "react": "17.x", "react-dom": "17.x", "styled-components": "^5.2.1"}, "precommit": "lint-staged", "lint-staged": {"*.{ts,tsx}": ["we eslint --quiet --fx", "we stylelint"]}}