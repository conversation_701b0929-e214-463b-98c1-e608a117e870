import React, { useEffect } from 'react';
import { useMemoizedFn } from 'ahooks';
import styled from 'styled-components';
import { observer } from 'mobx-react-lite';
import $i18n from '@ali/notable-i18n';
import { IconButton, Menu, Popover, Tooltip } from '@ali/we-design-next';
import { Goback20, Close20, AddM20, More20, Delete20, Link20, Share20 } from '@ali/we-icons';
import { themeCss } from '@ali/notable-design';
import { KEY_CODES, CommonKeymapEnum, useHotkeys } from '@ali/notable-common';
import { useNotableCore } from '@ali/notable-core';
import { getRecordNameConfig } from '@ali/notable-components';

export interface HeaderProps {
  title?: string | null;
  onClose: () => void;

  showAddRowBtn?: boolean;
  showCopyRecordUrlBtn?: boolean;
  showRecordShareBtn?: boolean;
  disableAddRowBtn?: boolean;
  onClickAddRow?: () => void;
  onCopyRecordUrl?: () => void;
  onShareRecord?: () => void;

  showPrevNextRowBtn?: boolean;
  disablePrevRowBtn?: boolean;
  disableNextRowBtn?: boolean;
  onClickNextOrPrev?: (isNext: boolean) => void;
  onDeleteRow?: () => void;
}

export const Header: React.FC<HeaderProps> = observer((props) => {
  const {
    title,
    onClose,

    showAddRowBtn,
    showCopyRecordUrlBtn,
    showRecordShareBtn,
    disableAddRowBtn,
    onClickAddRow = () => {},
    onCopyRecordUrl = () => {},
    onShareRecord = () => {},

    showPrevNextRowBtn,
    disablePrevRowBtn,
    disableNextRowBtn,
    onClickNextOrPrev = () => {},
  } = props;

  const { frameworkController } = useNotableCore();
  const { recordName, xRecordName, unnamed } = getRecordNameConfig(frameworkController.currentSheet);

  const handleClose = useMemoizedFn(() => {
    /**
     * ModalMask mousedown 会同步执行 handleClose
     * 但触发 Field blur 是异步的
     * 导致如果同步设置 setVisible Field 组件会先 unmount，而不会触发 blur
     */
    ((document.activeElement as HTMLElement) || document.body).blur();
    onClose();
  });

  useHotkeys(
    CommonKeymapEnum.switchPrev,
    (keyEvent) => {
      if (showPrevNextRowBtn && !disablePrevRowBtn) {
        keyEvent.preventDefault();
        keyEvent.stopPropagation();
        onClickNextOrPrev(false);
      }
    },
    [onClickNextOrPrev],
  );

  useHotkeys(
    CommonKeymapEnum.switchNext,
    (keyEvent) => {
      if (showPrevNextRowBtn && !disableNextRowBtn) {
        keyEvent.preventDefault();
        keyEvent.stopPropagation();
        onClickNextOrPrev(true);
      }
    },
    [onClickNextOrPrev],
  );

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.keyCode === KEY_CODES.Escape) {
        handleClose();
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Root>
      <Title>{title || unnamed}</Title>
      <Buttons>
        {(showCopyRecordUrlBtn || showRecordShareBtn) && (
          <>
            {showCopyRecordUrlBtn && (
              <Tooltip title={$i18n.t('record_copy_link_alias', { recordName })} placement="top">
                <IconButton
                  onClick={onCopyRecordUrl}
                  icon={<Link20 />}
                  size="small"
                  data-role="RecordDetails_CopyRecordUrl"
                  minimized
                />
              </Tooltip>
            )}
            {showRecordShareBtn && (
              <Tooltip title={$i18n.t('we_notable_share_record_to_chat', { recordName })} placement="top">
                <IconButton
                  className="share-record"
                  onClick={onShareRecord}
                  icon={<Share20 />}
                  size="small"
                  data-role="RecordDetails_ShareRecord"
                  minimized
                />
              </Tooltip>
            )}
            <Divider />
          </>
        )}
        {showAddRowBtn && (
          <>
            <Tooltip title={$i18n.t('record_add_alias', { recordName })} placement="top">
              <IconButton
                onClick={onClickAddRow}
                disabled={disableAddRowBtn}
                icon={<AddM20 />}
                size="small"
                data-role="RecordDetails_AddRow"
                minimized
              />
            </Tooltip>
            <Divider />
          </>
        )}
        {
          showPrevNextRowBtn && (
            <>
              <Tooltip title={$i18n.t('record_previous_alias', { recordNameClassifier: xRecordName })} placement="top">
                <IconButton
                  disabled={disablePrevRowBtn}
                  icon={<Goback20 className="prev-icon" />}
                  onClick={() => onClickNextOrPrev(false)}
                  size="small"
                  data-role="RecordDetails_Prev"
                  minimized
                />
              </Tooltip>
              <EmptyDivider />
              <Tooltip title={$i18n.t('record_next_alias', { recordNameClassifier: xRecordName })} placement="top">
                <IconButton
                  onClick={() => onClickNextOrPrev(true)}
                  disabled={disableNextRowBtn}
                  icon={<Goback20 className="next-icon" />}
                  size="small"
                  data-role="RecordDetails_Next"
                  minimized
                />
              </Tooltip>
              { props.onDeleteRow && (
                <>
                  <EmptyDivider />
                  <Popover
                    trigger="click"
                    placement="bottomLeft"
                    placementOffset={[4, 0]}
                    content={
                      <Menu
                        items={[{
                          key: 'delete',
                          prefix: <Delete20 />,
                          title: $i18n.t('notable_notableViewFramework_DeleteName', { name: $i18n.t('record_name') }),
                        }]}
                        onSelect={(k) => {
                          if (k.join('.') === 'delete') {
                            props.onDeleteRow?.();
                          }
                        }}
                      />
                    }
                  >
                    <IconButton size="small" icon={<More20 />} />
                  </Popover>
                </>
              )}
              <Divider />
            </>
          )
        }
        <Tooltip title={$i18n.t('notable_notableViewPlugins_CloseARecord')} placement="top">
          <IconButton
            onClick={handleClose}
            icon={<Close20 />}
            size="small"
            data-role="RecordDetails_Close"
            minimized
          />
        </Tooltip>
      </Buttons>
    </Root>
  );
});

const Root = styled.header`
  display: flex;
  height: 100%;
  padding: 0 16px;
  justify-content: space-between;
  align-items: center;

  .prev-icon {
    transform: rotate(90deg);
  }

  .next-icon {
    transform: rotate(-90deg);
  }

  .share-record {
    margin-left: 8px;
  }
`;

const Title = styled.div`
  width: 0;
  flex: 1 auto;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 16px;
  color: ${themeCss.color.level1};
  font-weight: bold;
`;

const Buttons = styled.div`
  display: flex;
  align-items: center;
  margin-left: 10px;
`;

const Divider = styled.i`
  display: block;
  width: 1px;
  height: 16px;
  background-color: ${themeCss.color.line_light};
  margin: 0 12px;

  &.toggle-button-divider {
    margin-left: 8px;
  }
`;

const EmptyDivider = styled.i`
  display: block;
  width: 12px;
  height: 16px;
`;
