import { useCallback, useEffect, useState } from 'react';
import { safeSetStorage, safeGetStorage, primaryDocCreateDocGuideLocalKey, primaryDocOpenGuideLocalKey } from '@ali/notable-common';

export const useCreateDocGuide = (loading, dentryUuid, contentRef) => {
  const [visible, setVisible] = useState(false);

  const showCreateGuide = useCallback(() => {
    if (!contentRef.current) {
      return;
    }
    const needShow = safeGetStorage(primaryDocCreateDocGuideLocalKey);
    if (needShow === '1') {
      setVisible(true);
      safeSetStorage(primaryDocCreateDocGuideLocalKey, '0');
      safeSetStorage(primaryDocOpenGuideLocalKey, '1');
    }
  }, [contentRef]);

  useEffect(() => {
    if (!loading && !dentryUuid) {
      showCreateGuide();
    }
    return () => {
      setVisible(false);
    };
  }, [loading, dentryUuid, showCreateGuide]);

  const closeCreateGuide = useCallback(() => {
    setVisible(false);
  }, []);
  return { visible, closeCreateGuide };
};
