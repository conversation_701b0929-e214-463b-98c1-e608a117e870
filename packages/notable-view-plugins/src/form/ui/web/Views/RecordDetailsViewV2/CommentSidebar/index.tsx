/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useMemo, useState } from 'react';
import {
  LazyCommentSidebarApp,
  createRecordCommentId,
  getWordUtils,
  isRecordComment,
  CommentSidebarProvider,
  getRecordCommentRowId,
} from '@ali/notable-react';
import { RecordComment } from './RecordComment';

interface ICommentProps {
  viewId: string;
  sheetId: string;
  rowId: string;
  loading: boolean | 'copying';
  documentController?: any;
}

export const CommentSidebar = (props: ICommentProps) => {
  const { documentController, loading, viewId, sheetId, rowId } = props;
  const [simpleController, setSimpleController] = useState<any>(null);
  const [utils, setUtils] = useState<any>(null);

  useEffect(() => {
    getWordUtils().then((_utils) => {
      setUtils(_utils);
    });
  }, []);

  useEffect(() => {
    if (!documentController && !loading && utils?.createSimpleController) {
      const controller = utils.createSimpleController({
        renderCustomComment: (id) => {
          if (isRecordComment(id)) {
            const recordId = getRecordCommentRowId(id) || rowId;
            return <RecordComment viewId={viewId} sheetId={sheetId} rowId={recordId} />;
          }

          return undefined;
        },
      });
      setSimpleController(controller);
    }
  }, [documentController, utils, loading, viewId, sheetId, rowId]);

  const customCommentItems = useMemo(
    () => [
      {
        id: createRecordCommentId(rowId),
        position: 0,
      },
    ],
    [rowId],
  );

  const renderContent = () => {
    if (loading) {
      return null;
    }

    if (documentController || simpleController) {
      return documentController ? (
        <LazyCommentSidebarApp
          documentController={documentController}
          customItems={customCommentItems}
        />
      ) : (
        <LazyCommentSidebarApp controller={simpleController} customItems={customCommentItems} />
      );
    }

    return null;
  };

  return <CommentSidebarProvider>{renderContent()}</CommentSidebarProvider>;
};
