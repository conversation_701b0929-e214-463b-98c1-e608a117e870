import React, { useEffect, useState } from 'react';
import { useMemoizedFn } from 'ahooks';
import styled from 'styled-components';
import { observer } from 'mobx-react-lite';
import $i18n from '@ali/notable-i18n';
import { IconButton, Menu, Popover, TextButton, Tooltip } from '@ali/we-design-3';
import { themeVars } from '@ali/notable-design';
import { KEY_CODES, CommonKeymapEnum, useHotkeys } from '@ali/notable-common';
import { useNotableCore } from '@ali/notable-core';
import { getRecordNameConfig } from '@ali/notable-components';
import {
  Add16,
  More16,
  RightArrow16,
  RightLeft16,
  Comment16,
  Close16,
  Share16,
  ExpandLeft16,
  ShrinkLeft16,
} from '@ali/we-icons-3';
import { useMoreItems } from './useMoreItems';
import { useShareItems } from './useShareItems';

export interface HeaderProps {
  title?: string | null;
  onClose: () => void;

  commentVisible: boolean;
  showOpenInTab: boolean;
  showPrintBtn?: boolean;
  showCommentBtn?: boolean;
  showHistoryBtn?: boolean;
  showDocHistory?: boolean;
  showAddRowBtn?: boolean;
  showCopyRecordUrlBtn?: boolean;
  showRecordShareBtn?: boolean;
  disableAddRowBtn?: boolean;
  toggleCommentVisible: () => void;
  onClickAddRow?: () => void;
  onCopyRecordUrl?: () => void;
  onShareRecord?: () => void;
  onExpandFullPage?: () => void;
  onCollapseFullPage?: () => void;
  openFieldHistory?: () => void;
  openDocHistory?: () => void;
  openPagePrint?: () => void;
  openFlow?: (path: string) => void;

  showPrevNextRowBtn?: boolean;
  disablePrevRowBtn?: boolean;
  disableNextRowBtn?: boolean;
  onClickNextOrPrev?: (isNext: boolean) => void;
  onDeleteRow?: () => void;
  openInTab?: () => void;
}

const StyledIconButton = styled(IconButton)`
  color: ${themeVars.color.level_2};
  &:not([disabled]):hover {
    color: ${themeVars.color.level_2};
  }
`;

const StyledTextButton = styled(TextButton)`
  color: ${themeVars.color.level_2};
  &:not([disabled]):hover {
    color: ${themeVars.color.level_2};
  }
`;

export const Header: React.FC<HeaderProps> = observer((props) => {
  const {
    onClose,
    commentVisible,
    showOpenInTab,
    showPrintBtn,
    showCommentBtn,
    showHistoryBtn,
    showDocHistory,
    showAddRowBtn,
    showCopyRecordUrlBtn,
    showRecordShareBtn,
    disableAddRowBtn,
    toggleCommentVisible = () => {},
    onExpandFullPage = () => {},
    onCollapseFullPage = () => {},
    onClickAddRow = () => {},
    onCopyRecordUrl = () => {},
    onShareRecord = () => {},
    showPrevNextRowBtn,
    disablePrevRowBtn,
    disableNextRowBtn,
    onClickNextOrPrev = () => {},
    openInTab = () => {},
    openFieldHistory = () => {},
    openDocHistory = () => {},
    openPagePrint = () => {},
    openFlow = () => {},
  } = props;
  const [visible, setVisible] = useState<string>('');

  const closePopover = () => {
    setVisible('');
  };

  const openPopover = (type: string) => {
    setVisible(type);
  };

  const {
    frameworkController,
    layoutController: { rowExpanded },
  } = useNotableCore();
  const { recordName, xRecordName } = getRecordNameConfig(frameworkController.currentSheet);
  // const recordOpenMethod = frameworkController.currentView?.recordOpenMethod;
  const handleClose = useMemoizedFn(() => {
    /**
     * ModalMask mousedown 会同步执行 handleClose
     * 但触发 Field blur 是异步的
     * 导致如果同步设置 setVisible Field 组件会先 unmount，而不会触发 blur
     */
    ((document.activeElement as HTMLElement) || document.body).blur();
    onClose();
  });

  useHotkeys(
    CommonKeymapEnum.switchPrev,
    (keyEvent) => {
      if (showPrevNextRowBtn && !disablePrevRowBtn) {
        keyEvent.preventDefault();
        keyEvent.stopPropagation();
        onClickNextOrPrev(false);
      }
    },
    [onClickNextOrPrev],
  );

  useHotkeys(
    CommonKeymapEnum.switchNext,
    (keyEvent) => {
      if (showPrevNextRowBtn && !disableNextRowBtn) {
        keyEvent.preventDefault();
        keyEvent.stopPropagation();
        onClickNextOrPrev(true);
      }
    },
    [onClickNextOrPrev],
  );

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.keyCode === KEY_CODES.Escape) {
        handleClose();
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const shareItems = useShareItems({
    showCopyRecordUrlBtn,
    showRecordShareBtn,
    recordName,
  });
  const moreItems = useMoreItems({
    showDocHistory,
    showHistoryBtn,
    showPrintBtn,
    showOpenInTab,
    columns: frameworkController.currentView?.columns,
    onDeleteRow: props.onDeleteRow,
  });

  // const renderOpenMethodIcon = () => {
  //   if (recordOpenMethod === 'modal') {
  //     return <PopView16 />;
  //   }
  //   if (recordOpenMethod === 'full-page') {
  //     return <FullWindow16 />;
  //   }
  //   return <SpliteView16 />;
  // };

  return (
    <Root>
      <LeftButtons>
        {!rowExpanded?.isFullScreen && (
          <>
            <Tooltip title={$i18n.t('notable_recordDetails_expand_to_full_screen')} placement="top">
              <StyledIconButton
                onClick={onExpandFullPage}
                icon={<ExpandLeft16 />}
                size="small"
                data-role="RecordDetails_expand"
                minimized
              />
            </Tooltip>
            <Divider />
            {/* <EmptyDivider /> */}
          </>
        )}
        {rowExpanded?.isFullScreen && (
          <>
            <Tooltip title={$i18n.t('we_notable_fold')} placement="top">
              <StyledIconButton
                onClick={onCollapseFullPage}
                icon={<ShrinkLeft16 />}
                size="small"
                data-role="RecordDetails_collapse"
                minimized
              />
            </Tooltip>
            <Divider />
            {/* <EmptyDivider /> */}
          </>
        )}
        {/* <Popover
          visible={visible === 'openMethodMenu'}
          onVisibleChange={(v) => {
            if (v) {
              openPopover('openMethodMenu');
            } else {
              closePopover();
            }
          }}
          placement="bottomLeft"
          placementOffset={[4, 0]}
          content={
            <Menu
              items={[
                {
                  key: 'record-open-side',
                  prefix: <SpliteView16 />,
                  suffix: (!recordOpenMethod || recordOpenMethod === 'side') && <Selectcheck16 />,
                  title: $i18n.t('notable_notableComponents_RecordOpenSide'),
                },
                {
                  key: 'record-open-modal',
                  prefix: <PopView16 />,
                  suffix: recordOpenMethod === 'modal' && <Selectcheck16 />,
                  title: $i18n.t('notable_notableComponents_RecordOpenModal'),
                },
                {
                  key: 'record-open-full-page',
                  prefix: <FullWindow16 />,
                  suffix: recordOpenMethod === 'full-page' && <Selectcheck16 />,
                  title: $i18n.t('notable_notableComponents_RecordOpenFullPage'),
                },
              ]}
              onSelect={(k) => {
                if (!frameworkController.currentSheetId || !frameworkController.currentViewId) {
                  return;
                }
                if (k.join('.') === 'record-open-side') {
                  viewController.setRecordOpenMethod(frameworkController.currentSheetId, frameworkController.currentViewId, 'side');
                } else if (k.join('.') === 'record-open-modal') {
                  viewController.setRecordOpenMethod(frameworkController.currentSheetId, frameworkController.currentViewId, 'modal');
                } else if (k.join('.') === 'record-open-full-page') {
                  viewController.setRecordOpenMethod(frameworkController.currentSheetId, frameworkController.currentViewId, 'full-page');
                }
                closePopover();
              }}
            />
          }
        >
          <Tooltip title={$i18n.t('notable_recordDetails_expand_to_full_screen')} placement="top">
            <StyledIconButton
              onClick={() => openPopover('openMethodMenu')}
              icon={renderOpenMethodIcon()}
              size="small"
              data-role="RecordDetails_openMethodMenu"
              minimized
            />
          </Tooltip>
        </Popover> */}
        {showPrevNextRowBtn && (
          <>
            <Tooltip
              title={$i18n.t('record_previous_alias', { recordNameClassifier: xRecordName })}
              placement="top"
            >
              <StyledIconButton
                disabled={disablePrevRowBtn}
                icon={<RightLeft16 className="prev-icon" />}
                onClick={() => onClickNextOrPrev(false)}
                size="small"
                data-role="RecordDetails_Prev"
                minimized
              />
            </Tooltip>
            <EmptyDivider />
            <Tooltip
              title={$i18n.t('record_next_alias', { recordNameClassifier: xRecordName })}
              placement="top"
            >
              <StyledIconButton
                onClick={() => onClickNextOrPrev(true)}
                disabled={disableNextRowBtn}
                icon={<RightArrow16 className="next-icon" />}
                size="small"
                data-role="RecordDetails_Next"
                minimized
              />
            </Tooltip>
            <Divider />
          </>
        )}
        {showAddRowBtn && (
          <>
            <Tooltip title={$i18n.t('record_add_alias', { recordName })} placement="top">
              <StyledIconButton
                onClick={onClickAddRow}
                disabled={disableAddRowBtn}
                icon={<Add16 />}
                size="small"
                data-role="RecordDetails_AddRow"
                minimized
              />
            </Tooltip>
          </>
        )}
      </LeftButtons>
      <Buttons>
        {showCommentBtn && (
          <StyledTextButton
            size="small"
            type="normal"
            active={commentVisible}
            onClick={toggleCommentVisible}
            icon={<Comment16 />}
            style={{ marginLeft: '8px' }}
            data-role="RecordDetails_openComment"
          >
            <span>
              {$i18n.t('notable_notableViewPlugins_Comments')}
            </span>
          </StyledTextButton>
        )}
        {(showCopyRecordUrlBtn || showRecordShareBtn) && (
          <Popover
            visible={visible === 'openShareMenu'}
            onVisibleChange={(v) => {
              if (v) {
                openPopover('openShareMenu');
              } else {
                closePopover();
              }
            }}
            placement="bottomLeft"
            placementOffset={[4, 0]}
            content={
              <Menu
                items={shareItems}
                onSelect={(k) => {
                  if (k.join('.') === 'copy-record-url') {
                    onCopyRecordUrl();
                  } else if (k.join('.') === 'share-record') {
                    onShareRecord();
                  }
                  closePopover();
                }}
              />
            }
          >
            <StyledTextButton
              size="small"
              type="normal"
              active={visible === 'openShareMenu'}
              onClick={() => openPopover('openShareMenu')}
              icon={<Share16 />}
              style={{ marginLeft: '8px' }}
              data-role="RecordDetails_openShareMenu"
            >
              {$i18n.t('notable_portal_share')}
            </StyledTextButton>
          </Popover>
        )}
        {moreItems.length > 0 && (
          <Popover
            visible={visible === 'openMoreMenu'}
            onVisibleChange={(v) => {
              if (v) {
                openPopover('openMoreMenu');
              } else {
                closePopover();
              }
            }}
            placement="bottomLeft"
            placementOffset={[4, 0]}
            content={
              <Menu
                items={moreItems}
                onSelect={(k) => {
                  if (k.join('.') === 'history.record-field-history') {
                    openFieldHistory?.();
                  } else if (k.join('.') === 'history.record-doc-history') {
                    openDocHistory?.();
                  } else if (k.join('.') === 'page-print') {
                    openPagePrint?.();
                  } else if (k.join('.').startsWith('flow.')) {
                    openFlow?.(k[1]);
                  } else if (k.join('.') === 'delete') {
                    props.onDeleteRow?.();
                  } else if (k.join('.') === 'openInTab') {
                    openInTab?.();
                  }
                  closePopover();
                }}
              />
            }
          >
            <StyledIconButton
              icon={<More16 />}
              size="small"
              active={visible === 'openMoreMenu'}
              onClick={() => openPopover('openMoreMenu')}
              data-role="RecordDetails_MoreMenu"
              style={{ marginLeft: '8px' }}
              minimized
            />
          </Popover>
        )}
        <Divider />

        <Tooltip title={$i18n.t('notable_notableViewPlugins_CloseARecord')} placement="top">
          <StyledIconButton
            onClick={handleClose}
            icon={<Close16 />}
            size="small"
            data-role="RecordDetails_Close"
            minimized
          />
        </Tooltip>
      </Buttons>
    </Root>
  );
});

const Root = styled.header`
  display: flex;
  height: 100%;
  padding: 0 12px;
  justify-content: space-between;
  align-items: center;

  .prev-icon {
    transform: rotate(90deg);
  }

  .next-icon {
    transform: rotate(90deg);
  }

  .share-record {
    margin-left: 8px;
  }
`;

const LeftButtons = styled.div`
  display: flex;
  align-items: center;
`;

const Buttons = styled.div`
  display: flex;
  align-items: center;
  margin-left: 10px;
`;

const Divider = styled.i`
  display: block;
  width: 1px;
  height: 16px;
  background-color: ${themeVars.color.line_light};
  margin: 0 12px;

  &.toggle-button-divider {
    margin-left: 8px;
  }
`;

const EmptyDivider = styled.i`
  display: block;
  width: 8px;
  height: 16px;
`;
