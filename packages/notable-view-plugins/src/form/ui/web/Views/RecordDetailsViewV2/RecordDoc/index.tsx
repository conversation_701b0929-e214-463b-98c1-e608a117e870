import React, { useMemo } from 'react';
import styled from 'styled-components';
import { Spin } from '@ali/we-design-next';
import $i18n from '@ali/notable-i18n';
import { isRecordComment, LazyWordApp } from '@ali/notable-react';
import { RecordComment } from '../CommentSidebar/RecordComment';
import { DEFAULT_MIN_HEIGHT } from '../useDocMinHeight';
import { Placeholder, PlaceholderWrapper } from './Placeholder';

const PrimaryDocWrapper = styled.div`
  position: relative;
`;

const SpinWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
`;

const RecordDingDoc: React.FC<{
  viewId: string;
  sheetId: string;
  rowId: string;
  minHeight: number;
  dentryUuid: string;
  loading: boolean | 'copying';
  readOnly: boolean;
  anchorId?: string;
  toCreateDoc: () => void;
  getScrollableContainer: () => HTMLElement | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onDocumentControllerRef: (controller: any) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onCangjieControllerRef: (controller: any) => void;
}> = (props) => {
  const {
    loading,
    dentryUuid,
    readOnly,
    viewId,
    rowId,
    sheetId,
    minHeight = DEFAULT_MIN_HEIGHT,
    anchorId,
    toCreateDoc,
    getScrollableContainer,
    onDocumentControllerRef,
    onCangjieControllerRef,
  } = props;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const editorConfigs: any = useMemo(() => {
    return {
      commentConfig: {
        type: 'customSidebar',
        renderCustomComment: (id) => {
          if (isRecordComment(id)) {
            return <RecordComment viewId={viewId} sheetId={sheetId} rowId={rowId} />;
          }

          return undefined;
        },
      },
    };
  }, [viewId, sheetId, rowId]);

  if (loading === true) {
    return (
      <SpinWrapper style={{ minHeight }}>
        <Spin size="large" />
      </SpinWrapper>
    );
  }
  return (
    <PrimaryDocWrapper>
      {!dentryUuid && loading === 'copying' && (
        <PlaceholderWrapper style={{ userSelect: 'none', cursor: 'not-allowed', minHeight }}>
          {$i18n.t('we_notable_record_primary_doc_copying')}
        </PlaceholderWrapper>
      )}
      {!dentryUuid && !loading && !readOnly && (
        <Placeholder minHeight={minHeight} toCreateDoc={toCreateDoc} loading={loading} dentryUuid={dentryUuid} />
      )}
      {dentryUuid && (
        <>
          <LazyWordApp
            dentryUuid={dentryUuid}
            docMode={readOnly}
            anchorId={anchorId}
            getScrollableContainer={getScrollableContainer}
            onDocumentControllerRef={onDocumentControllerRef}
            onCangjieControllerRef={onCangjieControllerRef}
            editorConfigs={editorConfigs}
            contentStyle={{
              marginTop: '24px',
              minHeight,
              paddingBottom: '40vh',
            }}
            scene="notable"
            sceneExtra={{ viewId }}
          />
        </>
      )}
    </PrimaryDocWrapper>
  );
};

export default RecordDingDoc;
