import { themeVars } from '@ali/notable-design';
import { CloseS12, LeftL16 } from '@ali/we-icons-3';
import React from 'react';
import styled from 'styled-components';

const ModalHeader = styled.div`
    display: flex;
    padding: 0 12px;
    align-items: center;
`;

const TitleWrapper = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
`;

const IconWrapper = styled.div`
  padding: 6px;
  display: flex;
  align-items: center;
  color: ${themeVars.color.level_1};
`;

const Title = styled.div`
  font-size: 16px;
  line-height: 24px;
  color: ${themeVars.color.level_1};
`;

const CloseWrapper = styled.div`
    border-radius: 100%;
    background-color: ${themeVars.color.overlay_light};
    box-shadow: inset 0px 1px 0px 0px rgba(255, 255, 255, 0);
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover {
        background: ${themeVars.color.overlay_heavy};
    }
`;

export const Header = (props: { title: string; closeModal: () => void; goBack?: () => void }) => {
  const { goBack, title, closeModal } = props;
  return (
    <ModalHeader>
      <TitleWrapper>
        {goBack && (
          <IconWrapper style={{ marginRight: '4px' }} onClick={goBack}>
            <LeftL16 />
          </IconWrapper>
        )}
        <Title>{title}</Title>
      </TitleWrapper>
      <CloseWrapper onClick={closeModal}>
        <CloseS12 />
      </CloseWrapper>
    </ModalHeader>
  );
};
