import React, { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import { useMemoizedFn } from 'ahooks';
import { generateRecordId } from '@ali/notable-common';
import { observer } from 'mobx-react-lite';
import { useNotableCore } from '@ali/notable-core';
import type { RecordDetailsViewProps } from '@ali/notable-core';
import { WordProvider, usePrimaryDoc } from '@ali/notable-react';
import { shareRecordToChat } from '../../../../../utils/shareRecord';
import {
  commonContextMenuItem,
  CommonContextMenuItemType,
} from '../../../../../common/commonContextMenuItems';
import { Panel } from './Panel';
import { Header } from './Header';
import { useGridStore, useGridViewSelection } from './hooks';
import { useViewId } from './useViewId';
import { Content } from './Content';
import { IExtendProps, ModuleModal } from './ModuleModal';

const RecordDetailsPanel: React.FC<RecordDetailsViewProps> = observer((props) => {
  const { rowId, viewId: defaultViewId, sheetId, onClose } = props;

  const notableCore = useNotableCore();
  const gridStore = useGridStore();

  const {
    abilityManager,
    proxyController: { api },
    frameworkController,
    layoutController,
    rowController,
    globalDataController,
    viewExtensionController,
    services,
    primaryDocCommentController: { store: commentStore },
  } = notableCore;
  const { currentSheetId, currentView, currentViewQueryModel, currentSheet } = frameworkController;
  const { sidePanelConfigs, rowExpanded } = layoutController;
  const {
    embeded,
    enablePrint,
    readonly,
    isWeWordPlugin,
    enableRecordShare,
  } = globalDataController.settings;
  const { getLwpClient, sendRecordReminder } = services;
  const [commentVisible, setCommentVisible] = useState<boolean>(false);
  const [modalInfo, setModalInfo] = useState<{
    visible: boolean;
    type: string;
    extend?: IExtendProps;
  }>({
    visible: false,
    type: '',
    extend: undefined,
  });
  const timer = useRef<NodeJS.Timeout | null>(null);
  const hasCommentWidth = useRef(false);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [documentController, setDocumentController] = useState<any | undefined>();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [cangjieController, setCangjieController] = useState<any | undefined>();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [broker, setBroker] = useState<any>();
  const needFocus = useRef<boolean>(false);

  const viewId = useViewId(defaultViewId, sheetId);

  const { dentryUuid, loading, toCreateDoc } = usePrimaryDoc({ sheetId, viewId, rowId });

  const canReadRecord = abilityManager.can('recordRead', sheetId, rowId);
  const showComment = sidePanelConfigs.find(
    (config) => config.key === 'comment' && !config.isHidden?.({ sheetId }),
  );
  const showHistory = sidePanelConfigs.find(
    (config) => config.key === 'history' && !config.isHidden?.({ sheetId }),
  );
  const showDocHistory = !!dentryUuid;
  const isCurrentSheet = sheetId === currentSheetId;
  const canCreateRecord = abilityManager.can('recordCreate', sheetId);
  const showOpenInTab = !!(
    dentryUuid ||
    (!dentryUuid && !readonly && abilityManager.cannotReason('recordUpdate', sheetId, rowId))
  );

  useGridViewSelection();

  const onDocumentControllerRef = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (controller: any) => {
      setDocumentController(controller);
    },
    [],
  );

  const onCangjieControllerRef = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (controller: any) => {
      setCangjieController(controller);
    },
    [],
  );

  const closeModal = () => {
    setModalInfo({
      visible: false,
      type: '',
      extend: undefined,
    });
  };

  const openModal = useCallback((type: string, extend?: IExtendProps) => {
    setModalInfo({
      visible: true,
      type,
      extend,
    });
  }, []);

  const openFlow = useCallback(
    (fieldId: string) => {
      const allFields = currentView?.columns ?? [];
      const field = allFields.find((item) => item.id === fieldId);
      openModal('flow', {
        field,
        rowId,
        viewId,
        sheetId,
        readOnly: readonly,
        canReadRecord: canReadRecord || false,
      });
    },
    [openModal, currentView?.columns, readonly, canReadRecord, rowId, viewId, sheetId],
  );

  // 1: 评论，2: 历史记录
  const currentTab = layoutController.rowExpanded?.currentTab;
  // 流程字段点击会传递该参数，用于显示流程字段弹框
  const currentFieldId = notableCore.layoutController.rowExpanded?.fieldId;

  useEffect(() => {
    if (currentFieldId) {
      openFlow(currentFieldId);
      layoutController.expandRow(sheetId, viewId, rowId, { fieldId: '', currentTab: 0 });
    }
  }, [currentFieldId, openFlow, sheetId, viewId, rowId, layoutController]);

  const clearAnchorId = useMemoizedFn(() => {
    if (rowExpanded?.anchorId) {
      layoutController.updateExpandedRow({ anchorId: '' });
    }
  });

  useEffect(() => {
    return () => {
      clearAnchorId();
    };
  }, [rowId, clearAnchorId]);

  const openFieldHistory = useCallback(() => {
    openModal('fieldHistory', {
      rowId,
      viewId,
      sheetId,
    });
  }, [openModal, rowId, viewId, sheetId]);

  useEffect(() => {
    if (currentTab === 1) {
      // 评论sdk初始化会默认发送onSidebarVisibleChange为false事件
      timer.current = setTimeout(() => {
        setCommentVisible(true);
      }, 200);
    } else if (currentTab === 2) {
      openFieldHistory();
    }
    return () => {
      if (timer.current) {
        clearTimeout(timer.current);
        timer.current = null;
      }
    };
  }, [currentTab, openFieldHistory]);

  useEffect(() => {
    if (needFocus.current && dentryUuid && cangjieController) {
      needFocus.current = false;
      cangjieController.command('focus');
    }
  }, [dentryUuid, cangjieController]);

  const setNeedFocus = () => {
    needFocus.current = true;
  };

  useEffect(() => {
    let offs: Array<() => void> = [];
    if (broker) {
      offs = [
        broker.on('comment:onSidebarVisibleChange', (value: boolean) => {
          if (commentVisible !== value) {
            setCommentVisible(value);
          }
        }),
        broker.on('comment:onCountsChange', (...args) => {
          console.log('comment:onCountsChange', args);
        }),
      ];
    }
    return () => {
      offs.forEach((off) => off());
    };
  }, [broker, commentVisible]);

  useEffect(() => {
    if (broker) {
      broker.emit('comment:setSidebarVisible', commentVisible);
    }
  }, [commentVisible, broker]);

  useEffect(() => {
    if (
      layoutController.rowExpanded &&
      !layoutController.rowExpanded.isFullScreen &&
      !layoutController.rowExpanded.isModal
    ) {
      if (commentVisible) {
        hasCommentWidth.current = true;
        layoutController.setRightPanelWidth(layoutController.rightPanelWidth + 300);
      } else if (hasCommentWidth.current) {
        layoutController.setRightPanelWidth(layoutController.rightPanelWidth - 300);
      }
    }
  }, [commentVisible, layoutController]);

  const toggleCommentVisible = useMemoizedFn(() => {
    const newVisible = !commentVisible;
    if (newVisible) {
      const subTopics = commentStore.getUnResolvedPersistedSubTopicsBy(sheetId, rowId);
      const hasComments = subTopics.length > 0;
      if (hasComments) {
        commentStore.focusSubTopic(subTopics[0].id);
      } else {
        const newSubTopicId = commentStore.addNewSubTopic(sheetId, rowId);
        if (!newSubTopicId) return;
        commentStore.focusSubTopic(newSubTopicId);
      }
    } else {
      commentStore.cancelNewSubTopic();
    }
    setCommentVisible(newVisible);
  });

  const clickCommentTip = () => {
    setCommentVisible(true);
  };

  const initBroker = useCallback((_broker) => {
    setBroker(_broker);
  }, []);

  const openDocHistory = () => {
    openModal('docHistory', {
      dentryUuid,
    });
  };
  const openPagePrint = () => {
    const printConfig = sidePanelConfigs.find((config) => config.key === 'print');
    if (!printConfig) {
      const { extensions } = viewExtensionController;
      const ext = extensions.find((item) => item.id === 'print');
      ext?.onRegister(
        () => {
          openModal('pagePrint', {
            rowId,
            viewId,
            sheetId,
          });
        },
        { showType: 'record' },
      );
    } else {
      openModal('pagePrint', {
        rowId,
        viewId,
        sheetId,
      });
    }
  };

  const setNewRowId = useMemoizedFn((id: string) => {
    layoutController.updateExpandedRow({ rowId: id });
    // 使面板滚动到顶部
    const formContainer = document.getElementById('record-details-root');
    formContainer?.scrollTo?.(0, 0);
  });

  const handleAddNextRow = useMemoizedFn(() => {
    const newRowId = generateRecordId();
    const res = api.insertRecords({
      sheetId,
      preRowId: rowId,
      rows: [{ id: newRowId, cells: null }],
    });
    if (res) {
      setNewRowId(newRowId);
      setTimeout(() => {
        try {
          const firstField = document.querySelector('.f-query-form-primary-filed-container');
          const target =
            firstField?.querySelector('textarea') || firstField?.querySelector('input');
          target?.focus({ preventScroll: true });
        } catch (e) {
          // noop
        }
      }, 0);
    }
  });

  const onExpandFullPage = useMemoizedFn(() => {
    layoutController.updateExpandedRow({ isFullScreen: true });
  });

  const onCollapseFullPage = useMemoizedFn(() => {
    layoutController.updateExpandedRow({ isFullScreen: false });
  });

  const onClickNextOrPrev = useMemoizedFn((isNext: boolean) => {
    const targetRow = isNext
      ? currentViewQueryModel?.getNextRow(rowId)
      : currentViewQueryModel?.getPrevRow(rowId);
    targetRow && setNewRowId(targetRow.id);
  });

  const openDoc = (uuid: string) => {
    if (!uuid) {
      return;
    }
    const notableDentryUuid = globalDataController.fileInfo.dentryUuid;
    const targetUr = `https://${
      window.location.host
    }/i/nodes/${uuid}?belongNodeId=${notableDentryUuid}&iframeQuery=${encodeURIComponent(
      'view_mode=notable-record',
    )}`;
    window.open(targetUr);
  };

  const openInTab = useMemoizedFn(async () => {
    if (dentryUuid) {
      // 直接打开
      openDoc(dentryUuid);
    } else {
      // 创建文档再打开
      try {
        toCreateDoc((_dentryUuid) => {
          if (_dentryUuid) {
            openDoc(_dentryUuid);
          }
        });
      } catch {
        // empty
      }
    }
  });

  const handleClose = useMemoizedFn(() => {
    if (
      gridStore &&
      !readonly &&
      rowExpanded &&
      !rowExpanded.isModal &&
      currentView &&
      currentView.id === viewId
    ) {
      // 关闭面板时选中当前行第一个 cell
      const position = { rowId, fieldId: currentView.columns[0].id };
      gridStore.selectCell({ position });
    }
    onClose();
  });

  const canRecordCreate = useMemo(() => abilityManager.can('newRecordMustCanRead', sheetId), [
    sheetId,
    abilityManager.can,
  ]);

  const handleDeleteRow = useMemoizedFn((sId: string, rId: string) => {
    onClose();
    // 先关闭窗口再删除，因为当前窗口监听了记录的变更，如果同步删除会得到一个记录不存在的 tip
    Promise.resolve().then(() => {
      rowController.deleteRecords(sId, [rId]);
    });
  });

  const handleCopyRecordUrl = useMemoizedFn(() => {
    commonContextMenuItem[CommonContextMenuItemType.copyRecordUrl](currentSheet).onSelect({
      dentryUuid: globalDataController.fileInfo.dentryUuid,
      sheetId,
      viewId,
      rowId,
    });
  });

  const handleShareRecord = useMemoizedFn(() => {
    const lwpClient = getLwpClient?.();
    shareRecordToChat(
      {
        sheetId,
        viewId,
        recordId: rowId,
      },
      { sendRecordReminder, lwpClient },
    );
  });

  const [disablePrev, disableNext] = useMemo(() => {
    return [!currentViewQueryModel?.getPrevRow(rowId), !currentViewQueryModel?.getNextRow(rowId)];
  }, [rowId, currentViewQueryModel]);

  return (
    <>
      <WordProvider initBroker={initBroker}>
        <Panel
          header={
            <Header
              title={rowExpanded?.title}
              showAddRowBtn={!readonly && isCurrentSheet && canCreateRecord}
              disableAddRowBtn={readonly || !canRecordCreate}
              onClickAddRow={handleAddNextRow}
              onExpandFullPage={onExpandFullPage}
              onCollapseFullPage={onCollapseFullPage}
              showPrintBtn={enablePrint}
              showCommentBtn={showComment && isCurrentSheet}
              showHistoryBtn={showHistory && isCurrentSheet}
              showDocHistory={showDocHistory && isCurrentSheet}
              showPrevNextRowBtn={isCurrentSheet}
              disablePrevRowBtn={disablePrev}
              disableNextRowBtn={disableNext}
              onClickNextOrPrev={onClickNextOrPrev}
              onClose={handleClose}
              onDeleteRow={
                embeded && !readonly
                  ? () => {
                    handleDeleteRow(sheetId, rowId);
                  }
                  : undefined
              }
              showCopyRecordUrlBtn={!isWeWordPlugin}
              showRecordShareBtn={!isWeWordPlugin && enableRecordShare}
              onCopyRecordUrl={handleCopyRecordUrl}
              onShareRecord={handleShareRecord}
              showOpenInTab={showOpenInTab}
              openInTab={openInTab}
              commentVisible={commentVisible}
              toggleCommentVisible={toggleCommentVisible}
              openFieldHistory={openFieldHistory}
              openDocHistory={openDocHistory}
              openPagePrint={openPagePrint}
              openFlow={openFlow}
            />
          }
          content={
            <Content
              toCreateDoc={() => {
                toCreateDoc(() => {
                  setNeedFocus();
                });
              }}
              dentryUuid={dentryUuid}
              loading={loading}
              viewId={viewId}
              commentVisible={commentVisible}
              title={rowExpanded?.title}
              sheetId={sheetId}
              rowId={rowId}
              onDocumentControllerRef={onDocumentControllerRef}
              clickCommentTip={clickCommentTip}
              documentController={documentController}
              cangjieController={cangjieController}
              onCangjieControllerRef={onCangjieControllerRef}
              anchorId={rowExpanded?.anchorId}
            />
          }
        />
        <ModuleModal
          closeModal={closeModal}
          closeRecordDetail={onClose}
          visible={modalInfo.visible}
          type={modalInfo.type}
          extend={modalInfo.extend}
        />
      </WordProvider>
    </>
  );
});

export default RecordDetailsPanel;
