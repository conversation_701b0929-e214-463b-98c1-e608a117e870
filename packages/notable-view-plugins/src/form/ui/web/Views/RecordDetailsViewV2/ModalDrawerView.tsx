import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Modal } from '@ali/we-design-3';
import { logger, safeSetStorage, safeGetStorage } from '@ali/notable-common';
import styled, { createGlobalStyle, keyframes } from 'styled-components';
import { themeVars } from '@ali/notable-design';
import { useNotableCore } from '@ali/notable-core';
import classNames from 'classnames';

const MIN_WIDTH = 420;
const MIN_LEFT_DIST = 300;

const STORAGE_KEY = 'notable-record-detail-width';

interface IProps {
  content: React.ReactNode;
  isModal?: boolean;
  isFullScreen?: boolean;
  modalConfig?: {
    styles?: React.CSSProperties;
  };
  enableMask?: boolean;
  onClickMask?: () => void;
}

export const ModalDrawerView: React.FC<IProps> = observer((props) => {
  const { isModal, modalConfig, content, enableMask, isFullScreen, onClickMask } = props;

  const notableCore = useNotableCore();

  const width = notableCore.layoutController.rightPanelWidth;
  const [isResizing, setIsResizing] = useState(false);

  useEffect(() => {
    if (isModal) {
      notableCore.layoutController.setRightPanelWidth(0);
      return;
    }
    const storedWidth = parseInt(safeGetStorage(STORAGE_KEY) || '780', 10);
    notableCore.layoutController.setRightPanelWidth(storedWidth);
  }, [isModal, notableCore.layoutController]);

  const startResizing = (e: React.MouseEvent) => {
    setIsResizing(true);
    e.preventDefault();
    e.stopPropagation();
    const MAX_WIDTH = Math.max(window.innerWidth - MIN_LEFT_DIST, MIN_WIDTH);
    let currentWidth = Math.max(Math.min(Number(width) || 0, MAX_WIDTH), MIN_WIDTH); // 兼容脏数据
    const baseX = currentWidth + e.clientX;
    const handleResizing = (ev: MouseEvent) => {
      const newWidth = Math.ceil(baseX - ev.clientX);
      requestAnimationFrame(() => {
        currentWidth = Math.max(Math.min(newWidth, MAX_WIDTH), MIN_WIDTH);
        notableCore.layoutController.setRightPanelWidth(currentWidth);
      });
    };
    const stopResizing = () => {
      setIsResizing(false);
      safeSetStorage(STORAGE_KEY, String(currentWidth));
      window.removeEventListener('mousemove', handleResizing);
      window.removeEventListener('mouseup', stopResizing);
      logger.log('record_detail_resize', currentWidth);
    };
    window.addEventListener('mousemove', handleResizing);
    window.addEventListener('mouseup', stopResizing);
  };

  if (isModal) {
    return (
      <>
        <ModalStyled
          visible
          mask
          onClose={onClickMask}
          id="pc-form-modal"
          zIndex={999}
          style={modalConfig?.styles}
          className={classNames({ fullScreen: isFullScreen })}
          footer={null}
          header={null}
          content={content}
          data-notable-in-grid
        />
      </>
    );
  }

  return (
    <>
      <SideDrawer id="pc-form-side-drawer" style={isFullScreen ? { width: '100%', maxWidth: 'unset' } : { width }}>
        {content}
        <div onMouseDown={startResizing} className={`resizer${isResizing ? ' resizing' : ''}`} />
      </SideDrawer>
      {enableMask && <PageMask onClick={onClickMask} />}
      {isResizing && <ResizingStyle />}
    </>
  );
});

const slideInAnimation = keyframes`
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
`;

const PageMask = styled.div`
  position: absolute;
  z-index: 999;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
`;

const SideDrawer = styled.div`
  position: absolute;
  height: 100vh;
  right: 0;
  bottom: 0;
  min-width: ${MIN_WIDTH}px;
  max-width: calc(100vw - ${MIN_LEFT_DIST}px);
  overflow: visible;
  z-index: 1000;
  background-color: ${themeVars.color.fg_heavy};
  border-width: 0px 0px 0px 1px;
  border-style: solid;
  border-color: ${themeVars.color.line_heavy};
  box-shadow: 0px 8px 32px 0px rgba(0, 0, 0, 0.12);
  .resizer {
    position: absolute;
    left: -6px;
    top: 0;
    bottom: 0;
    cursor: col-resize;
    width: 12px;
    &:hover:after, &.resizing:after {
      content: "";
      display: block;
      width: 2px;
      height: 100%;
      margin-left: 4px;
      background-color: ${themeVars.color.brand_5_default};
    }
  }
  animation: ${slideInAnimation} .2s;
`;

const ModalStyled = styled(Modal)`
  padding: 0;
  overflow: hidden;
  width: 70vw;
  height: 80vh;
  min-width: 800px;
  max-width: 1440px;
  min-height: 500px;
  max-height: 900px;
  z-index: 999;
  border-radius: 16px;
  .wdn-tabs,
  .wd3-modal-content,
  .wdn-modal-content,
  .wdn-carousel-inner,
  .wdn-carousel-inner-item,
  .wdn-carousel-inner-item > :first-child {
    width: 100%;
    height: 100%;
  }
  .wdn-tabs {
    height: calc(100% - 56px);
  }
  .wdn-tabs-nav {
    padding: 0 16px;
  }

  &.fullScreen {
    width: 100%;
    height: 100%;
    min-width: unset;
    min-height: unset;
    max-width: unset;
    max-height: unset;
    border-radius: 0px;
  }
`;

const ResizingStyle = createGlobalStyle`
  * {
    cursor: col-resize;
  }
`;
