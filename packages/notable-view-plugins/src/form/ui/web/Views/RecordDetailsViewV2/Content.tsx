import React, { useEffect, useMemo, useRef, useState } from 'react';
import { isNil } from 'lodash-es';
import { useNotableCore } from '@ali/notable-core';
import { calc } from '@ali/notable-model';
import { getRecordNameConfig, NoPermission } from '@ali/notable-components';
import $i18n from '@ali/notable-i18n';
import styled from 'styled-components';
import { themeVars } from '@ali/notable-design';
import { IconButton, Tooltip } from '@ali/we-design-3';
import { TextArea } from '@ali/we-design-next';
import { useMemoizedFn } from 'ahooks';
import {
  UpdateCells,
  safeGetStorage,
  safeRemoveStorage,
  safeSetStorage,
  RowCell,
} from '@ali/notable-common';
import { ListExpand16, ListFold16 } from '@ali/we-icons-3';
import classNames from 'classnames';
import { primaryDocToggleFieldsLocalKey } from './constant';
import { useFormData } from './hooks';
import { RecordContent } from './RecordContent';
import { useViewId } from './useViewId';
import { LazyRecordDoc } from './RecordDoc/lazy-doc-module';
import { CommentSidebar } from './CommentSidebar';
import { useDocMinHeight } from './useDocMinHeight';

const ContentScroll = styled.div`
  overflow: auto;
  height: 100%;
`;
const ContentLayout = styled.div`
  display: flex;
  min-height: 100%;
`;

const CommentTipWrapper = styled.div`
  position: absolute;
  top: 6px;
  right: -28px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.2s ease-in-out;

  & > div {
    opacity: 0;
  }

  & > div.show {
    opacity: 1;
  }
`;

const IconButtonWrapper = styled.div`
  opacity: 0;
  position: absolute;
  display: flex;
  align-items: center;
  left: -25px;

  &.show {
    opacity: 1;
  }
`;

const TitleWrapper = styled.div`
  margin-bottom: 20px;
  font-size: 32px;
  font-weight: 600;
  line-height: 32px;
  color: ${themeVars.color.level_1};
  display: flex;
  align-items: center;
  position: relative;

  &:hover {
    ${CommentTipWrapper} {
      & > div {
        opacity: 1;
      }
    }
    ${IconButtonWrapper} {
      opacity: 1;
    }
  }
`;

const StyledTextArea = styled(TextArea)`
  border: none;
  &:focus-within {
    box-shadow: none;
  }
  textarea {
    font-size: 32px;
    font-weight: 600;
    line-height: 32px;
    &:focus {
      outline: none;
    }
  }
`;

const ContentMain = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 64px 64px 24px 44px;
  width: 100%;
  background: ${themeVars.color.pop_bg_light_blur};
  border-radius: 12px 12px 0 0;

  #record-details-root {
    padding: 0px;
  }
`;

const HeaderSection = styled.div`
  padding-left: 20px;
`;

const Divider = styled.div`
  height: 0.5px;
  margin: 32px 0 0 0;
  width: 100%;
  background-color: ${themeVars.color.line_light};
`;

const CommentWrapper = styled.div`
  width: 0px;
  transition: width 0.3s;
`;

export const Content = (props: {
  viewId: string;
  sheetId: string;
  rowId: string;
  commentVisible: boolean;
  dentryUuid: string;
  loading: boolean | 'copying';
  anchorId?: string;
  toCreateDoc: (callback?: (dentryUuid: string) => void) => void;
  title?: string | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  documentController?: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  cangjieController?: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onDocumentControllerRef: (controller: any) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onCangjieControllerRef: (controller: any) => void;
  clickCommentTip: () => void;
}) => {
  const {
    viewId: defaultViewId,
    sheetId,
    rowId,
    loading,
    dentryUuid,
    title,
    commentVisible,
    cangjieController,
    toCreateDoc,
    clickCommentTip,
  } = props;
  const notableCore = useNotableCore();

  const [contentFold, setContentFold] = useState(
    Boolean(safeGetStorage(`${primaryDocToggleFieldsLocalKey}_${sheetId}`)),
  );

  const ref = useRef<HTMLDivElement | null>(null);
  const headerRef = useRef<HTMLDivElement | null>(null);
  const viewId = useViewId(defaultViewId, sheetId);

  const {
    abilityManager,
    proxyController: { api },
    frameworkController: { currentSheet },
    globalDataController: {
      settings: { readonly, commentConfig },
    },
  } = notableCore;

  const CommentTip = commentConfig?.visible && commentConfig?.primaryDocTitleCommentTip;

  const { unnamed } = getRecordNameConfig(currentSheet);

  const [value, setValue] = useState(title || '');

  const readOnly = readonly || !abilityManager.can('recordUpdate', sheetId, rowId);

  const commentRef = useRef<HTMLDivElement | null>(null);

  const minHeight = useDocMinHeight({ loading: !!loading, dentryUuid, contentRef: ref, headerRef });
  const titleRef = useRef<HTMLTextAreaElement | null>(null);
  const timer = useRef<NodeJS.Timeout | null>(null);
  const compositing = useRef(false);
  const { formData, row } = useFormData(sheetId, viewId, rowId);

  const primaryDocField = useMemo(() => {
    return formData?.columns.find((item) => item.isPrimary && item.type === 'primaryDoc');
  }, [formData?.columns]);

  const focusTitle = () => {
    if (!titleRef.current) {
      return;
    }
    titleRef.current?.focus();
  };

  useEffect(() => {
    setValue(title || '');
    timer.current = setTimeout(() => {
      focusTitle();
    }, 500);
    return () => {
      if (timer.current) {
        clearTimeout(timer.current);
        timer.current = null;
      }
    };
  }, [title]);

  useEffect(() => {
    const primaryColumn = formData.columns.find((item) => item.isPrimary);
    const primaryDataVal = primaryColumn?.value;
    if (primaryColumn && row && !isNil(primaryDataVal)) {
      const docTitle =
        calc.renderCellToStr(
          { dataType: primaryColumn.dataType, value: primaryDataVal } as RowCell,
          { ...primaryColumn },
          row.createdTime,
        ) ?? '';
      notableCore.layoutController.updateExpandedRow({ title: docTitle });
    } else {
      notableCore.layoutController.updateExpandedRow({ title: '' });
    }
  }, [formData.columns, notableCore.layoutController, row]);

  const renderContent = () => {
    const canReadRecord = abilityManager.can('recordRead', sheetId, rowId);
    const recordItemContent = canReadRecord ? (
      <RecordContent rowId={rowId} viewId={viewId} sheetId={sheetId} readOnly={readOnly} />
    ) : (
      <NoPermission tip={$i18n.t('notable_biz_advanced_permission_record_no_permission_tip')} />
    );

    return recordItemContent;
  };

  const handleChange = useMemoizedFn((v: string, update: boolean) => {
    setValue(v);
    if (update) {
      onChange(v);
    }
  });

  const canCellEdit = useMemo((): boolean => {
    if (readOnly) {
      return false;
    }
    if (!primaryDocField) {
      return true;
    }
    const [cellUpdateAllow] = abilityManager.cannotReason(
      'cellUpdate',
      sheetId,
      primaryDocField.id,
      rowId,
    );
    if (!cellUpdateAllow) return false;
    const [recordUpdateAllow] = abilityManager.cannotReason('recordUpdate', sheetId, rowId);
    if (!recordUpdateAllow) return false;
    return true;
  }, [readOnly, abilityManager.cannotReason, sheetId, rowId, primaryDocField]);

  const onChange = useMemoizedFn((text: string) => {
    if (readOnly || !primaryDocField) return;
    const updated = {
      [primaryDocField.id]: {
        dataType: primaryDocField.dataType,
        value: text,
      },
    } as UpdateCells;
    api.updateRecords({
      sheetId,
      rows: [
        {
          rowId,
          cells: updated,
        },
      ],
    });
  });

  const onCompositionStart = useMemoizedFn(() => {
    compositing.current = true;
  });

  const onCompositionEnd = useMemoizedFn((e: React.CompositionEvent<HTMLTextAreaElement>) => {
    compositing.current = false;
    if (e.target instanceof HTMLInputElement) {
      handleChange(e.target.value, true);
    }
  });

  const onInputChange = useMemoizedFn((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    handleChange(e.target.value, !compositing.current);
  });

  const renderTitle = () => {
    return (
      <TitleWrapper style={contentFold ? { marginBottom: 0 } : undefined}>
        <Tooltip
          title={
            contentFold
              ? $i18n.t('notable_record_detail_fields_expand')
              : $i18n.t('notable_record_detail_fields_fold')
          }
        >
          <IconButtonWrapper className={classNames({ show: contentFold })}>
            <IconButton
              size="extra-small"
              icon={contentFold ? <ListFold16 /> : <ListExpand16 />}
              onClick={() => {
                const newState = !contentFold;
                if (newState) {
                  safeSetStorage(`${primaryDocToggleFieldsLocalKey}_${sheetId}`, '1');
                } else {
                  safeRemoveStorage(`${primaryDocToggleFieldsLocalKey}_${sheetId}`);
                }

                setContentFold(newState);
              }}
              style={{ marginRight: 4 }}
            />
          </IconButtonWrapper>
        </Tooltip>
        <StyledTextArea
          value={value}
          ref={titleRef}
          placeholder={unnamed}
          onChange={onInputChange}
          onCompositionStart={onCompositionStart}
          onCompositionEnd={onCompositionEnd}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && !compositing.current) {
              e.preventDefault();
              e.stopPropagation();
              if (loading) {
                return;
              }
              if (!dentryUuid) {
                toCreateDoc();
              } else if (cangjieController) {
                cangjieController.command('focus');
              }
            }
          }}
          autoHeight={{ unit: 'row', min: 1, max: 8 }}
          style={{ minHeight: 32 }}
          disabled={!canCellEdit}
        />
        {CommentTip && (
          <CommentTipWrapper>
            <CommentTip rowId={rowId} handleClick={clickCommentTip} sheetId={sheetId} />
          </CommentTipWrapper>
        )}
      </TitleWrapper>
    );
  };

  return (
    <ContentScroll ref={ref}>
      <ContentLayout>
        <ContentMain>
          <HeaderSection ref={headerRef}>
            {renderTitle()}
            {!contentFold ? renderContent() : null}
            <Divider />
          </HeaderSection>
          <LazyRecordDoc
            {...props}
            minHeight={minHeight}
            readOnly={readOnly}
            getScrollableContainer={() => ref.current}
          />
        </ContentMain>
        <CommentWrapper
          style={commentVisible ? { width: 300, marginLeft: '12px' } : {}}
          ref={commentRef}
        >
          <CommentSidebar {...props} />
        </CommentWrapper>
      </ContentLayout>
    </ContentScroll>
  );
};
