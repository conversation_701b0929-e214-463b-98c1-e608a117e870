import React, { useRef } from 'react';
import styled from 'styled-components';
import { themeVars } from '@ali/notable-design';
import $i18n from '@ali/notable-i18n';
import { Button, Popover } from '@ali/we-design-3';
import { DEFAULT_MIN_HEIGHT } from '../useDocMinHeight';
import { useCreateDocGuide } from './useCreateGuide';

export const PlaceholderWrapper = styled.div`
  font-size: 16px;
  line-height: 24px;
  display: flex;
  padding-left: 20px;
  padding-top: 24px;
  color: ${themeVars.color.level_4};
  height: 40vh;
`;

const Content = styled.div`
  padding: 16px;
  width: 280px;
  border-radius: ${themeVars.radius.radius_xl};
  background: ${themeVars.color.bg_default};
  box-shadow: ${themeVars.shadow.shadow_m};
  backdrop-filter: blur(30px);
`;

const Title = styled.div`
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: ${themeVars.color.level_1};
`;

const Desc = styled.div`
  font-size: 14px;
  line-height: 20px;
  color: ${themeVars.color.level_2};
  margin-top: 8px;
`;

const Footer = styled.div`
  margin-top: 12px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
`;

const PlaceholderDesc = styled.div`
  height: 24px;
`;

export const Placeholder = (props: {
  loading: boolean;
  minHeight: number;
  dentryUuid: string;
  toCreateDoc: () => void;
}) => {
  const { toCreateDoc, dentryUuid, loading, minHeight = DEFAULT_MIN_HEIGHT } = props;
  const guideRef = useRef<HTMLDivElement>(null);
  const { visible: guideVisible, closeCreateGuide: hideGuide } = useCreateDocGuide(
    loading,
    dentryUuid,
    guideRef,
  );

  const renderContent = () => {
    return (
      <Content>
        <Title>{$i18n.t('we_notable_primary_doc_create_guide_title')}</Title>
        <Desc>{$i18n.t('we_notable_primary_doc_create_guide_description')}</Desc>
        <Footer>
          <Button
            type="primary"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              hideGuide();
            }}
          >
            {$i18n.t('notable_notableViewFramework_IKnow')}
          </Button>
        </Footer>
      </Content>
    );
  };

  if (!dentryUuid && !loading) {
    return (
      <PlaceholderWrapper
        style={{ minHeight }}
        onClick={() => {
          toCreateDoc();
        }}
      >
        <Popover placement="bottomLeft" placementOffset={[24, 0]} visible={guideVisible} animation={false} content={renderContent()}>
          <PlaceholderDesc ref={guideRef}>{$i18n.t('we_notable_record_primary_doc_pc_placeholder')}</PlaceholderDesc>
        </Popover>
      </PlaceholderWrapper>
    );
  }
  return null;
};
