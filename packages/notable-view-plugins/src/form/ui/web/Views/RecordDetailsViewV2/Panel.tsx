import { themeVars } from '@ali/notable-design';
import React from 'react';
import styled from 'styled-components';

export interface PanelProps {
  header: React.ReactNode;
  content: React.ReactNode;
  contentStyle?: React.CSSProperties;
}

export const Panel: React.FC<PanelProps> = (props) => {
  const { header, content, contentStyle } = props;

  return (
    <PanelRoot>
      <Header>{header}</Header>
      <Content style={contentStyle}>{content}</Content>
    </PanelRoot>
  );
};

const PanelRoot = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: ${themeVars.color.pop_bg_heavy_blur};
  .wdn-tabs,
  .wdn-carousel-inner,
  .wdn-carousel-inner-item,
  .wdn-carousel-inner-item > :first-child {
    width: 100%;
    height: 100%;
  }
  .wdn-tabs {
    height: calc(100% - 56px);
  }
  .wdn-tabs-nav {
    padding: 0 16px;
  }
  overflow: visible;
`;

const Header = styled.div`
  width: 100%;
    height: 52px;
    flex-shrink: 0;
    padding: 12px 0;
`;

const Content = styled.div`
  overflow: auto;
  border-radius: 12px 12px 0 0;
  margin: 0 12px;
  flex: 1;
`;
