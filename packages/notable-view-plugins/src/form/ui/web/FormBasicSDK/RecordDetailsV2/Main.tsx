import React, { useEffect, useMemo, useState } from 'react';
import { useMemoizedFn } from 'ahooks';
import { keyBy, partition } from 'lodash-es';
import { observer } from 'mobx-react-lite';
import styled from 'styled-components';
import { themeVars } from '@ali/notable-design';
import { DownL16, Hide16, UpL16 } from '@ali/we-icons-3';
import $i18n from '@ali/notable-i18n';
import { FormFieldSchema } from '@ali/notable-common';
import { validateFieldValue } from '@ali/notable-react';
import { useNotableCore } from '@ali/notable-core';
import { useCoreStore, useGlobalStore } from '../../../store';
import { sanitizeFormItem } from '../utils';
import { HideFieldWrapper } from './styled';
import { CommonProps } from './types';
import { FormItem } from './FormItem';
import { TitleButton } from './TitleButton';

export interface IRecordDetailsProps {
  sheetId: string;
  viewId: string;
  columns: FormFieldSchema[];
  rowId: string;
  disableFieldEdit: boolean;
  canCellEdit: (fieldId: string) => [boolean, string | null];
  hiddenFields?: Record<string, boolean>;
  requiredFields?: Record<string, boolean>;
  ErrorFields?: Record<string, string>;
  onChange?: (id: string, item: FormFieldSchema) => void;
  autoCalc?: boolean;
  onUpdateField?: (
    schema: FormFieldSchema,
    changedTypes: string[],
    prevSchema: FormFieldSchema,
    useNewField?: boolean,
  ) => void;
  renderAdd?: () => React.ReactNode;
}

const RecordDetails = observer((props: IRecordDetailsProps) => {
  const {
    canCellEdit,
    disableFieldEdit,
    hiddenFields = {},
    requiredFields = {},
    ErrorFields = {},
    sheetId,
    viewId,
    rowId,
    autoCalc,
    renderAdd,
  } = props;
  const localStore = useCoreStore();
  const globalStore = useGlobalStore();
  const notableCore = useNotableCore();
  const { fieldPlugins } = notableCore.pluginController;
  const { columns } = localStore;
  const { displayHiddenFields } = globalStore;
  const handleToggleHiddenFields = useMemoizedFn(() => {
    globalStore.toggleDisplayHiddenFields();
    const formContainer = document.getElementById('notable-formView');
    if (formContainer) {
      setTimeout(() => {
        const height = formContainer.clientHeight;
        (formContainer.parentNode as HTMLElement).scrollTo(0, height);
      }, 100);
    }
  });

  const filterColumns = useMemo(() => {
    return columns.filter((c) => c.type !== 'primaryDoc');
  }, [columns]);

  const onValueCommit = useMemoizedFn<CommonProps['onValueCommit']>((id, data) => {
    sanitizeFormItem(data);
    const column = filterColumns.find((c) => c.id === id);
    const plugin = fieldPlugins.find((f) => f.type === column?.type);
    const invalid = !validateFieldValue({
      fieldPlugin: plugin ?? null,
      fieldValue: Array.isArray(data?.value) ? null : data?.value ?? null,
    });
    if (invalid) return;

    localStore.setValue(id, data, autoCalc);
    props.onChange?.(id, data);
  });

  const onUpdateField = useMemoizedFn((schemaTemp, type, oldSchema, useNewField) => {
    props.onUpdateField?.(schemaTemp, type, oldSchema, useNewField);
    if (autoCalc) {
      localStore.autoCalc();
    }
  });

  const [hiddenSchema, visibleSchema] = useMemo(() => {
    return partition(filterColumns, (c) => hiddenFields[c.id]);
  }, [filterColumns, hiddenFields]);

  const shouldCollapsed = visibleSchema.length > 6;
  const hiddenFieldsLength = hiddenSchema.length;

  const fieldPluginMap = useMemo(() => keyBy(fieldPlugins, 'type'), [fieldPlugins]);

  const [collapsed, setCollapsed] = useState(shouldCollapsed);

  const renderFormItems = () => {
    if (!visibleSchema.length) {
      return <div className="h1" />;
    }

    // 完全展开
    if (!collapsed) {
      return (
        <div className={'pl0'}>
          {visibleSchema.map((item) => {
            const required = requiredFields?.[item.id];
            const plugin = fieldPluginMap[item.type];
            return (
              <FormItem
                key={item.id}
                item={item}
                sheetId={sheetId}
                viewId={viewId}
                rowId={rowId}
                plugin={plugin}
                onValueCommit={onValueCommit}
                canCellEdit={canCellEdit}
                disableFieldEdit={disableFieldEdit || !!item.config.renderFieldConfig?.synced}
                fieldPlugins={fieldPlugins}
                onUpdateField={onUpdateField}
                scene="recordDetails"
                required={required}
                errorTip={ErrorFields?.[item.id]}
              />
            );
          })}
        </div>
      );
    }

    return (
      <div className={'pl0'}>
        {visibleSchema.slice(0, 6).map((item) => {
          const required = requiredFields?.[item.id];
          const plugin = fieldPluginMap[item.type];
          return (
            <FormItem
              key={item.id}
              item={item}
              sheetId={sheetId}
              viewId={viewId}
              rowId={rowId}
              plugin={plugin}
              onValueCommit={onValueCommit}
              canCellEdit={canCellEdit}
              disableFieldEdit={disableFieldEdit || !!item.config.renderFieldConfig?.synced}
              fieldPlugins={fieldPlugins}
              onUpdateField={onUpdateField}
              scene="recordDetails"
              required={required}
              errorTip={ErrorFields?.[item.id]}
            />
          );
        })}
        <CollapsedButton icon={<DownL16 />} onClick={() => setCollapsed(false)}>
          {$i18n.t('notable_notableFormView_ExpandField', {
            value: visibleSchema.length - 6,
          })}
        </CollapsedButton>
      </div>
    );
  };

  useEffect(() => {
    setCollapsed(shouldCollapsed);
  }, [shouldCollapsed]);

  return (
    <Wrap data-role="RecordDetails_Wrapper">
      {renderFormItems()}
      {hiddenFieldsLength > 0 && !collapsed && (
        <HideFieldWrapper>
          <CollapsedButton
            onClick={handleToggleHiddenFields}
            suffix={displayHiddenFields ? 'down' : 'right'}
            icon={<Hide16 />}
          >
            {hiddenFieldsLength}
            {$i18n.t('notable_notableFormView_HideField')}
          </CollapsedButton>
          {displayHiddenFields && (
            <HiddenContentWrap>
              <Divider />
              {hiddenSchema.map((item) => {
                const required = requiredFields?.[item.id];
                const plugin = fieldPluginMap[item.type];
                return (
                  <FormItem
                    hiddenField
                    key={item.id}
                    item={item}
                    sheetId={sheetId}
                    viewId={viewId}
                    rowId={rowId}
                    plugin={plugin}
                    onValueCommit={onValueCommit}
                    canCellEdit={canCellEdit}
                    disableFieldEdit={disableFieldEdit || !!item.config.renderFieldConfig?.synced}
                    fieldPlugins={fieldPlugins}
                    onUpdateField={onUpdateField}
                    scene="recordDetails"
                    required={required}
                    errorTip={ErrorFields?.[item.id]}
                  />
                );
              })}
            </HiddenContentWrap>
          )}
        </HideFieldWrapper>
      )}
      {!collapsed && renderAdd?.()}
      {shouldCollapsed && !collapsed && (
        <CollapsedButton icon={<UpL16 />} onClick={() => setCollapsed(true)}>
          {$i18n.t('notable_notableFormView_CollapseField')}
        </CollapsedButton>
      )}
    </Wrap>
  );
});

export default RecordDetails;

const Wrap = styled.div`
  display: flex;
  flex-direction: column;
`;

const CollapsedButton = styled(TitleButton)`
  margin-top: 12px;
  color: ${themeVars.color.level_3};
  width: fit-content;
`;

const HiddenContentWrap = styled.div`
  position: relative;
`;

const Divider = styled.div`
  width: 1px;
  height: 100%;
  background: ${themeVars.color.line_light};
  position: absolute;
  left: 16px;
`;
