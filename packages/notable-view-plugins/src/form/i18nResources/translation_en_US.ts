const resource: {[key: string]: string} = {
  "notable_notableFormView_FormView": "Form View",
  "notable_notableViewPlugins_FormViewIntro": "Quickly gather the required information",
  "notable_notableFormView_copy": "Copy",
  "notable_notableFormView_SubmitForm": "Submit Form",
  "notable_notableFormView_SubmitForm_Link": "Link",
  "notable_notableFormView_SubmitForm_EndText": "Conclusion",
  "notable_notableFormView_SubmitForm_AfterSubmit": "After successful submission",
  "notable_notableFormView_SubmitForm_JumpTo": "Jump to",
  "notable_notableFormView_SubmitForm_SubmitSuccessJumpTo": "Jump after submission",
  "notable_notableFormView_SubmitForm_DisclaimerDesc": "Please review the link carefully and confirm that the link points to a legitimate and valid third-party website before clicking Confirm",
  "notable_notableFormView_SubmitForm_DisclaimerAction": "Legal statement",
  "notable_notableFormView_SubmitForm_OnlySupportHttps": "Adding a non-https domain name is not supported.",
  "notable_notableFormView_ThisOperationIsUnavailableIn": "This operation is unavailable in read-only mode.",
  "notable_notableFormView_DingNotable": "SmartTable",
  "notable_notableFormView_TechSupport": "Powered by",
  "notable_notableFormView_SystemErrorPleaseTryAgain": "System error. Please try again later.",
  "notable_notableViewPlugins_FailedToSubmit": "Failed to submit",
  "notable_notableFormView_ThankYouForSubmitting": "Thank you for submitting ~",
  "notable_notableViewPlugins_FillInAgain": "Fill it again",
  "notable_notableFormView_RemoveAll": "Hide All",
  "notable_notableFormView_AddAll": "Add All",
  "notable_notableFormView_OptionalQuestions": "Questions",
  "notable_notableFormView_AddQuestions": "New Questions",
  "notable_notableFormView_AddQuestions_qre": "Form Question Type",
  "notable_notableFormView_AddQuestions_Tips": "When collecting within the organization, the author's name/job number/department will be automatically obtained.",
  "notable_notableFormView_FormDesc": "Please enter a form description",
  "notable_notableFormView_AddFields": "+ Add Field",
  "notable_notableFormView_Privacy_Icon_Tip": "This question involves privacy, please pay attention to the protection of data",
  "notable_notableViewPlugins_TheFieldIsBeingRepaired": "Field is under emergency repair",
  "notable_notableFormView_HideField": " Hidden fields",
  "notable_notableGridView_DeleteAFieldOrColumn": "Delete a field or column",
  "we_notable_confirm_deletion_of_field_with_extra_info": "Are you sure you want to delete the field “{{fieldName}}”? After deletion, {{extraInfo}}",
  "we_notable_restore_original_field_by_undoing": "You can restore the original field by undoing{{shortcut}}.",
  "notable_notableComponents_QRESameNameError": "Duplicate title names are not supported",
  "notable_notableComponents_Sort": "Sort",
  "notable_notableComponents_Determine": "Submit",
  "we_notable_form_success": "Form submitted successfully!",
  "we_notable_delete": "Delete",
  "we_notable_cancel": "Cancel",
  "we_notable_common_submit": "Submit",
  "we_notable_form_submit_and_next": "Submit and Add Another",
  "notable_notableFormView_DragAndDropComponents": "Drag-and-drop sort",
  "notable_notableFormView_WhetherRequired": "required",
  "notable_notableFormView_HiddenFields": "Hide Question",
  "notable_notableFormView_CopyComponents": "Copy field",
  "notable_notableFormView_DeleteAComponent": "Delete field",
  "notable_notableViewPlugins_CloseARecord": "Close Record",
  "notable_notableViewPlugins_DetailsEditingIsNotSupported": "Details editing is not supported for this type field.",
  "we_notable_mobile_form_submit_successfully": "Submitted successfully",
  "we_notable_mobile_form_submit_failed": "Failed to submit",
  "we_notable_form_no_repeat_submission": "The form has reached the maximum number of answers collected and cannot be filled in any more.",
  "we_notable_form_not_exist": "Form does not exist",
  "we_notable_form_stop_collecting": "Form stopped collecting",
  "we_notable_form_no_permission": "No form filling permission",
  "we_notable_form_frequency_exceed_limit": "You have submitted your form more than the limit",
  "we_notable_form_invalid_time": "Not within the effective time, do not support filling",
  "we_notable_form_no_permission_msg": "No need to fill",
  "we_notable_form_no_permission_msg_v2": "You do not need to fill out this form.",
  "we_notable_form_no_permission_sub_msg": "Take it easy, no pen is needed here.",
  "we_notable_form_no_collect": "Collection ended",
  "we_notable_form_no_reversion_row": "The record to be modified no longer exists",
  "we_notable_form_sheet_not_exist": "Form has been deleted",
  "notable_biz_advanced_permission_sheet_no_permission_tip": "Advanced permissions have been enabled. You do not have permission to view the current data table.",
  "notable_biz_advanced_permission_record_no_permission_tip": "Advanced permissions have been enabled for this multi-dimensional table, and you do not have permission to view the current record.",
  "notable_biz_advanced_permission_need_submit_new_record": "Advanced Permission is enabled in this file, and after submitting new records you may not be allowed to modify them,  please confirm data before submitting.",
  "notable_notableViewFramework_TheDataTableHasBeen": "Data table has been deleted!",
  "notable_notableViewFramework_TheRecordHasBeenDeleted": "The record does not exist or has been deleted",
  "notable_notableQNRFormView_QNRFormView": "Questionnaire View",
  "we_notable_copySuccess": "copy success",
  "we_notable_copyFailed": "Copy failed, please try again",
  "notable_form_fieldHasDeleted": "The question has been deleted",
  "notable_form_duplicate_question": "Duplicate Question",
  "notable_form_delete_question": "Delete Question",
  "notable_form_delete_question_confirm": "Are you sure to delete the question \"{{name}}\"?",
  "we_notable_form_style": "Style",
  "notable_form_setting": "Settings",
  "we_notable_attach_preview": "Preview",
  "we_notable_form_data_anylize": "Statistics",
  "we_biz_bulletEditor_saveInfo_saveAs": "success",
  "we_notable_common_pc": "Computer",
  "we_notable_common_phone": "Mobile Phone",
  "we_notable_return_to_edit": "Return to Edit",
  "we_notable_preview_not_support_submit": "Preview page does not support form submission",
  "we_notable_TitleTooLong": "The file name must not exceed 50 words",
  "we_notable_titleEditor_empty_tip": "Not Allow Empty Name",
  "notable_render_filed_inline_input_placeholder": "Please enter a description (optional)",
  "we_notable_submitted_records_hidden": "The new record is hidden",
  "notable_notableFormView_Privacy_Field_Tip": "*This question involves private information, please pay attention to verify the identity of the collector",
  "notable_notableViewFramework_QRE_Catalog_Basic": "Basic Question",
  "notable_notableViewFramework_QRE_Catalog_Frequent": "Freqenent Questions",
  "notable_notableViewFramework_QRE_Catalog_Advanced": "Advanced Question Type",
  "notable_notableViewFramework_QRE_Field_Alias_text": "Single line of text",
  "notable_notableViewFramework_QRE_Field_Alias_select": "Single Select",
  "notable_notableViewFramework_QRE_Field_Alias_multiSelect": "Multiple select",
  "notable_notableViewFramework_QRE_Field_Alias_file": "Picture/File",
  "notable_notableViewFramework_QRE_Field_Alias_person": "Personnel selection",
  "notable_notableViewFramework_QRE_Field_Alias_department": "Sector selection",
  "notable_notableViewFramework_QRE_Field_Alias_richtext": "Multi-line graphics",
  "notable_form_setting_fillSettings": "Fill in settings",
  "notable_form_setting_afterSubmit": "After submitting the form",
  "notable_form_setting_enableCollect": "Open Form Collection",
  "notable_form_setting_stopCollect": "Stop collecting",
  "notable_form_setting_stopCollect_tip": "After stopping the collection, your form will not be filled out, confirm the stop?",
  "notable_form_setting_enableAnonFill": "Fill in anonymously",
  "notable_form_setting_enableModifyAfterSubmit": "Allow modifications after submission",
  "notable_form_setting_enableEffectiveTime": "Effective time for filling in",
  "notable_form_setting_limitStartTime": "Limit Start Time",
  "notable_form_setting_limitEndTime": "Limit end time",
  "notable_form_setting_endTimeNotEarlierThanStartTime": "End time cannot be earlier than start time",
  "notable_form_setting_startTimeNotEqualEndTime": "Start time cannot be the same as end time",
  "notable_form_setting_endTimeToStartTimeNotExceed24h": "End time cannot be more than 24 hours from start time",
  "notable_form_setting_endTimeToStartTimeNotExceed7d": "End time cannot be more than 7 days from start time",
  "notable_form_setting_endTimeToStartTimeNotExceed1m": "End time cannot be more than one month from start time",
  "notable_form_setting_enableCollectCycle": "Timing reminder by cycle",
  "notable_form_setting_enableCollectCycle_desc": "After the setting is turned on, the filling person can fill in normally within a limited period, and the time zone GMT +08:00 shall prevail.",
  "notable_form_setting_collectCycle": "Collection cycle",
  "notable_form_setting_submitDays": "Date of submission",
  "notable_form_setting_submitDays_everyday": "Every day",
  "notable_form_setting_restrictStartTime": "Start time (send reminder)",
  "notable_form_setting_restrictEndTime": "End Time",
  "notable_form_setting_noticeWay": "Notification Method",
  "notable_form_setting_noticeTipMemberList": "A notification will be sent to the specified person at the Limited Start Time",
  "notable_form_setting_enableSubmitUserLimit": "Limit submissions per user",
  "notable_form_setting_userSubmitLimit_onceInTotal": "Only once in total",
  "notable_form_setting_userSubmitLimit_onceInTotal_desc": "Each user cannot submit it after completing it once.",
  "notable_form_setting_userSubmitLimit_onceADay": "Only once per day",
  "notable_form_setting_userSubmitLimit_onceADay_desc": "Can be submitted again after 24:00 every day",
  "notable_form_setting_userSubmitLimit_oncePerCycle": "Only once in a cycle",
  "notable_form_setting_userSubmitLimit_oncePercycle_desc": "Optional after the Cycle Collection setting needs to be turned on",
  "notable_form_setting_enableSubmitUserLimit_classDesc": "As long as one student or student's parents have submitted it, other family members cannot submit it again.",
  "notable_form_setting_enableSubmitUserLimit_class": "Limit submissions per student",
  "notable_form_setting_userSubmitLimit_onceInTotal_desc_class": "Each student cannot submit it after completing it once.",
  "notable_form_setting_enableSubmitNumTotalLimit": "Limit Total Collection",
  "notable_form_setting_submitNumTotalLimit": "{{ count }}",
  "notable_precision_infinity": "Infinity",
  "notable_notableFormView_AddFiller": "Add Stub",
  "notable_notableFormView_LinkHasBeenCopiedTo": "Link has been copied to the pasteboard",
  "notable_notableFormView_SaveQRCode": "Save QR Code",
  "notable_notableFormView_CopyLink": "Copy",
  "notable_notableFormView_field_filter": "Display Conditions",
  "notable_notableFormView_field_filter_modal_title": "Show this question when the answer meets the following conditions",
  "notable_common_help_doc": "Help Manual",
  "notable_notableFormView_AddQuestions_Mobile": "Insert Title",
  "notable_notableFormView_RemoveMember": "Remove",
  "notable_notableFormView_ShareForm": "Share form",
  "notable_notableFormView_Open": "Open",
  "notable_notableFormView_SubmitForm_UrlType_FillInTips": "After clicking Submit, you will jump to the external URL set by the form publisher. Please identify its validity and legality and protect your personal information.",
  "form": "Form",
  "notable_notableFormView_ShareQRCodeDownloadImgName": "Sharing Form QR Code",
  "notable_notableFormView_WhoCanFill": "Who can fill in",
  "notable_notableFormView_MemberEmpty": "No filling person",
  "notable_notableComponents_TextDisplayedAfterSubmission": "Text displayed after submission",
  "we_notable_confirm": "Confirm",
  "notable_form_style_cover": "Cover",
  "notable_form_style_theme": "Theme",
  "notable_notableViewFramework_Cancel": "Cancel",
  "notable_notableViewFramework_Ok": "Determine",
  "notable_notableViewFramework_DeletedSuccessfully": "Deleted successfully",
  "notable_notableViewFramework_FailedToDelete": "Delete failed",
  "notable_common_change_cover": "Cover recommend ratio 10:3",
  "notable_common_change_cover_tip": "The recommend size is 1600*480. In order to ensure the display effect of computers and mobile phones, please display the main text/elements in the middle area.",
  "notable_common_change_cover_check_template": "View Template",
  "notable_notableCore_Option": "Option one",
  "notable_notableCore_Option2": "Option 2",
  "notable_add_rule": "Add condition",
  "notable_notableFormView_field_filter_empty": "Not set",
  "notable_notableFormView_empty_placeholder_left": "Drag",
  "notable_notableFormView_empty_placeholder_right": "Add questions on the left",
  "notable_notableFormView_field_filter_modal_subtitle": "This question will only be displayed when the writer fills in the specified answer in the preceding question.",
  "notable_notableFormView_field_filter_modal_learn_more": "Learn more about topic display conditions",
  "notable_notableViewFramework_NameAlreadyExistsEnterA": "{{name}} already exists, please enter a different name",
  "we_notable_mention_send": "Send",
  "notable_form_timeLocale_days": "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday",
  "notable_form_timeLocale_hour": "%h o'clock",
  "notable_form_timeLocale_minute": "%m minute",
  "notable_form_timeLocale_thatDayHour": "%h o'clock the day",
  "notable_form_timeLocale_nextDayHour": "%h o'clock next day",
  "notable_form_timeLocale_thatDayTime": "%h:%m the day",
  "notable_form_timeLocale_nextDayTime": "%h:%m next day",
  "notable_form_timeLocale_thatWeek": "That %w",
  "notable_form_timeLocale_nextWeek": "Next %w",
  "notable_form_timeLocale_thatWeekTime": "That %w at %h:%m",
  "notable_form_timeLocale_nextWeekTime": "Next %w at %h:%m",
  "notable_form_timeLocale_thatMonth": "%dth of the month",
  "notable_form_timeLocale_nextMonth": "%dth of next month",
  "notable_form_timeLocale_thatMonthTime": "%h:%m on the %dth of the month",
  "notable_form_timeLocale_nextMonthTime": "%h:%m on the %dth of next month",
  "notable_form_timeLocale_fromTo": "%f to %t",
  "notable_form_timeLocale_from": "Effective at %f",
  "notable_form_timeLocale_to": "Expired at %t",
  "notable_form_app_name": "Form",
  "notable_complete": "Complete",
  "notable_notableViewFramework_select_extra_name_long": "Extra content",
  "we_biz_topbar_menu_shareCommon": "Share",
  "detail": "Details",
  "we_notable_untitledName": "Untitled Form",
  "we_notable_form_invite_editor": "Add Collaborators",
  "we_notable_more": "More",
  "notable_form_modify_last_content": "Modify filling content",
  "notable_form_import": "Import",
  "notable_form_if_import_last_content": "Import last filled content",
  "we_notable_form_has_submit": "You have already submitted the form",
  "we_notable_form_has_submit_can_modify": "You can modify the last submitted record",
  "notable_notableFormView_SendImmediately": "Send Now",
  "notable_notableViewFramework_Rename": "Rename",
  "notable_notableViewFramework_Form_Desc_Field": "Description",
  "notable_notableViewFramework_Form_Desc_Field_Placeholder": "Please enter a paragraph description",
  "notable_notableFormView_ViewCollectionResult": "View Collection Results",
  "notable_notableFields_extra_nps": "Scale/NPS",
  "notable_form_setting_enableNoLoginFill": "No login to fill in",
  "notable_form_setting_enableCollectCycle_helpText": "Repeat form submission daily/weekly/monthly",
  "notable_form_setting_enableAnonFill_helpText": "Filled person information is not collected (displayed as a dummy number)",
  "notable_notableFormView_disable_person": "Contact person/department/group question type, \"anyone can fill in\" is not supported for the time being 」",
  "notable_form_setting_enableNoLoginFill_disableTip": "Select \"Anyone can fill\" to open",
  "notable_form_setting_enableNoLoginFill_tips": "No need to log in DingTalk to fill out, but cannot get the submitter information ",
  "notable_form_jumping_to_new_page": "Submitted successfully, about to jump to the page...",
  "notable_notableFormView_UrgeFill": "Reminder",
  "notable_notableFormView_quantifier_copies": "Copies",
  "notable_notableFormView_quantifier_people": "People",
  "notable_notableFormView_historyCycleData": "Historical cycle data",
  "notable_notableFormView_Published": "Published",
  "notable_notableFormView_Published_desc_class": "A notification will be sent to the selected class group at the specified time",
  "notable_notableFormView_Published_desc_normal": "Notifications will be sent to specified people/groups at specified times",
  "notable_notableFormView_Publish": "Publish and share",
  "notable_notableFormView_Unsubmit_Number": "Number of Unsubmitted",
  "notable_notableFormView_Share_Open": "Open",
  "notable_notableFormView_WhoCanFill_Guide_Title": "Publish before inviting people to fill in",
  "notable_notableFormView_Invite_Notify": "Invite and notify the person",
  "notable_form_setting_partition": "Sub-table by cycle",
  "notable_form_setting_partition_have_opened_tip": "The current data table has been opened over the table, please close the table of another form view and try again",
  "notable_form_setting_partition_jump_to_opened_view": "Go to Open Form View",
  "notable_form_setting_partition_helpText": "After the table is divided, it can support a larger number of cycles and the number of collected pieces, but the data of the historical cycle has more functional restrictions.",
  "notable_form_setting_partition_help_link": "Learn More",
  "notable_form_setting_partition_warning_title": "Determine the sub-table?",
  "notable_form_setting_partition_warning_line_1": "After opening, the collection results will be divided into different data tables by cycle, but the data tables of non-current cycle will be \"frozen\", mainly as follows:",
  "notable_form_setting_partition_warning_line_2": "Not editable, grouped, sorted, associated references, formula calculations, and not triggered by automated processes.",
  "notable_notableViewFramework_DeleteName": "Delete {{name}}",
  "notable_notableViewFramework_toDoc": "Go to Settings",
  "notable_notableFormView_ShareDefaultValue": "Set Default Values",
  "record_name": "Record",
  "we_notable_export_as": "Export as ",
  "we_notable_form_current_cycle": "Current Cycle",
  "we_notable_form_view_by_class": "View by Class",
  "we_notable_form_query_view_only_show_owner_class": "Show only class data for which you are the head teacher",
  "notable_notableGridView_Entered": "Filled",
  "we_notable_form_history_cycle_not_support_urge": "Historical cycle does not support reminders.",
  "we_notable_form_share_and_notice": "Sharing and Notifications",
  "we_notable_form_notice_after_submit": "Notify data sharers when submitted",
  "we_notable_form_close_automation_failed": "Failed to close \"notify data sharers when submitted\", please go to the automation process panel to close",
  "we_notable_form_no_notice_object": "No notification object",
  "we_notable_form_add_notice_object": "Add Notification Object",
  "we_notable_form_send_notice_after_commit": "It has been set as the data sharer and will be notified automatically when it is submitted.",
  "we_notable_form_create_notice_automation_failed": "Failed to create, please make sure you have group management permission",
  "we_notable_need_manage_permission_tip": "Requires Manageable permission",
  "we_notable_form_invite_fill": "Invitation to fill in person",
  "we_notable_form_invite_must_fill": "Add Required Person",
  "we_notable_form_invite_specify_fill": "Add Designated Person",
  "we_notable_form_invite_specify_fill_and_invite": "Add Designated Person and Notify",
  "we_notable_form_invite_must_fill_and_notice": "Add required person and notify",
  "we_notable_form_publish_and_invite_specify_fill": "Publish and Add Designers",
  "we_notable_form_publish_and_invite_must_fill": "Publish and add required person",
  "we_notable_form_invite_cycle_fill": "Invitation to fill in regularly",
  "we_notable_form_query_edit_form": "Edit Form",
  "we_notable_form_query_enter_notable": "View statistical charts",
  "we_notable_form_query_enter_notable_tip": "Data is automatically imported into multi-dimensional tables to achieve one-stop convenient data management, providing advanced permissions, automated process configuration and multi-view analysis.",
  "we_notable_form_select_cycle": "Select cycle",
  "we_notable_loading": "Data loading...",
  "we_notable_refresh": "Refresh",
  "we_notable_form_history_cycle_not_support_view_by_class": "History cycle does not support viewing by class",
  "we_notable_export_ad_excel": "Export to Excel (.xlsx) ",
  "we_notable_export_ad_zip": "Export attachments as a compressed package ",
  "we_notable_export_ad_zip_tip": "Export Pictures/attachments/signatures as folders",
  "notable_notableViewFramework_IKnow": "I know.",
  "notable_notableFormView_UrgeFill_noNeed": "No unfilled person",
  "we_notable_form_query_cycle_statistic_tip": "There may be inconsistencies between the number of submissions in the current cycle and the number of rows in the list when you turn on/off circular filling, or turn off Split Table.",
  "notable_biz_advanced_permission_not_support_form": "Viewing form view is not supported in preview mode",
  "notable_notableViewPlugins_TheRecordHasBeenDeleted": "This record has been deleted",
  "notable_field_both_way_link": "Both-way Link",
  "notable_field_one_way_link": "One-way Link",
  "we_notable_field_flow_done_need_fill": "Complete the fields required for the node",
  "we_notable_field_flow_cancel_tip": "Flow terminated",
  "we_notable_field_flow_must_fill_tip": "This item is required",
  "we_notable_field_flow_return_node_state": "Return to this node",
  "we_notable_field_flow_cancel_node": "Terminate the flow",
  "we_notable_field_flow_has_cancelled": "This flow has been terminated",
  "we_notable_field_flow_recover": "Resume the flow",
  "we_notable_field_flow_node_has_done": "Completed",
  "we_notable_field_flow_config": "Flow Configuration",
  "we_notable_field_flow_done_node": "Complete Node",
  "we_notable_field_flow_done_node_no_permission": "You do not have permission to complete this node",
  "we_notable_field_flow_permission_preview_tip": "The preview interface does not support operation flow fields.",
  "notable_form_setting_noticeWay_todo": "To-do notice",
  "notable_form_setting_noticeWay_work": "Notification of Work",
  "notable_form_setting_noticeWay_dingdoc": "DingTalk Docs Assistant notification",
  "notable_form_setting_noticeWay_chat": "Chat notifications",
  "notable_notableComponents_notice_not_in_org_tips": "Inviters outside of your organization won't receive work or to-do notifications ",
  "notable_notableFields_Select_Config_DisplayMode_Flat": "Tile",
  "notable_notableFields_Select_Config_DisplayMode_Dropdown": "Dropdown",
  "notable_notableFormView_select_dropdown_warning_message": "Drop-down style does not support \"other\" option and insert picture",
  "record_add_alias": "Add {{recordName}}",
  "record_copy_link_alias": "Copy {{recordName}} Link",
  "record_previous_alias": "Previous {{recordNameClassifier}} ⌘ Shift ↑",
  "record_next_alias": "Next {{recordNameClassifier}} ⌘ Shift ↓",
  "record_unnamed_alias": "Untitled {{recordName}}",
  "pageprint-name-row": "Record print",
  "pageprint-name-view": "View Print",
  "pageprint-view-ext-add": "Add plug-in",
  "pageprint-view-add": "Create page to print views",
  "pageprint-view-n-title": "This view does not support preview.",
  "pageprint-view-n-msg": "Please open from the home page to use this plug-in",
  "pageprint-view-m-title": "Mobile view is not supported",
  "pageprint-view-m-msg": "Please use PC to open",
  "we_notable_share_record_to_chat": "Share {{recordName}} to chat",
  "notable_notableFormView_DefaultValue": "Default value",
  "notable_set_default_value_via_shareLink": "Set a default value by sharing a link ",
  "notable_get_help": "Go and find out. ",
  "we_biz_topbar_menu_panel_history": "History",
  "we_biz_topbar_menu_panel_record_field_history": "Field/Column History ",
  "plugin": "Plugin",
  "notable_portal_share": "Share",
  "notable_notableViewPlugins_Comments": "Comments",
  "we_notable_field_flow_name": "Flow",
  "notable_recordDetails_expand_to_full_screen": "Expand to full screen ",
  "we_biz_topbar_menu_panel_record_doc_history": "History of the main text ",
  "pageprint-name": "Page Print",
  "we_notable_record_primary_doc_placeholder": "Click to edit ",
  "notable_notableFormView_CollapseField": "Collapse",
  "notable_notableFormView_ExpandField": "Expand {{value}} collapsed fields ",
  "notable_unnamed_document": "Untitled Document ",
  "we_notable_open_new_tab": "Open in new tab ",
  "we_notable_primary_doc_create_guide_title": "Click here to start editing your document ",
  "we_notable_primary_doc_create_guide_description": "This is the DingTalk document editing area. Try to input \"/\" to add more content. Come on and show your creativity! ",
  "we_notable_record_primary_doc_copying": "Creating a copy, please wait ",
  "we_notable_record_primary_doc_pc_placeholder": "Click or press Enter to edit the text ",
  "notable_record_detail_fields_expand": "Click to expand all fields/columns ",
  "notable_record_detail_fields_fold": "Click to collapse all fields/columns "
};

export default resource