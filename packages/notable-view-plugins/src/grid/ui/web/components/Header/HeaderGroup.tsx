import { observer } from 'mobx-react-lite';
import React, { useMemo } from 'react';
import styled from 'styled-components';
import { themeCss } from '@ali/notable-design';
import { useFieldGroupHighlightHeaderStyle } from '@ali/notable-components';
import { FieldGroup } from '@ali/notable-common';
import { COLOR } from '../../constants/color';
import { useColumnStore } from '../../hooks/useStore';
import { CalculatedColumn, CalculatedColumnGroup } from '../../types';
import zIndex from '../../constants/zIndex';
import HEIGHT from '../../constants/height';
import HeaderGroupedRenderer from './HeaderGroupedRenderer';
import { useResizeColumn } from './useResizeColumn';
import { useHeaderDragDropGroup } from './useHeaderDragDropGroup';
import { useIsGroupSelected } from './useIsColumnSelected';

interface Props {
  columns: CalculatedColumn[];
  fieldGroup: FieldGroup;
  canFieldResize: boolean;
  positionOffset?: number;
}

const HeaderGroup = ({
  columns,
  fieldGroup,
  canFieldResize,
  positionOffset = 0,
  children,
}: React.PropsWithChildren<Props>) => {
  const { adaptViewportFrozenColCount, noLeftBorder, groupMapWithRange, getColumnByKey } = useColumnStore();
  const isGroupSelected = useIsGroupSelected(fieldGroup.id);

  const groupRange = groupMapWithRange[fieldGroup.id];
  const hasPrimary = useMemo(() => groupRange.children.some((child) => child.isPrimary), [groupRange.children]);

  const rightColumn = groupRange?.children[groupRange.children.length - 1] ?? columns[columns.length - 1];
  const leftColumn = groupRange?.children[0] ?? columns[0];

  const notPrimaryDocWithPrimary = fieldGroup.children.some((c) => {
    const column = getColumnByKey(c.id);
    return column?.isPrimary;
    // return column?.isPrimary && column?.type !== 'primaryDoc';
  });

  const [resizeDrag] = useResizeColumn({
    frozen: !!rightColumn.frozen,
    fieldId: rightColumn.key,
    index: rightColumn.idx,
    width: rightColumn.width,
    canDrag: canFieldResize,
  });

  const width = useMemo(
    () => {
      const originalWidth = groupRange.width ?? columns.reduce((acc, cur) => acc + cur.width, 0);
      return hasPrimary ? positionOffset + originalWidth : originalWidth;
    },
    [columns, groupRange, positionOffset, hasPrimary],
  );

  const left = useMemo(() => {
    const originLeft = noLeftBorder && leftColumn.sortedIndex < adaptViewportFrozenColCount
      ? leftColumn.left - 1
      : leftColumn.left;
    return hasPrimary ? originLeft : positionOffset + originLeft;
  }, [noLeftBorder, leftColumn, adaptViewportFrozenColCount, positionOffset, hasPrimary]);

  const highlightStyle = useFieldGroupHighlightHeaderStyle(fieldGroup.id, { width, left });

  const calculatedColumnGroup = useMemo<CalculatedColumnGroup>(
    () => ({
      ...fieldGroup,
      idx: leftColumn.idx,
      width,
      left,
      resizable: rightColumn.resizable,
      isLastFrozenColumn: rightColumn.isLastFrozenColumn,
      fieldGroupId: fieldGroup.id,
    }),
    [width, left, fieldGroup, rightColumn, leftColumn],
  );

  const [drag, isDragging, handleMouseUp] = useHeaderDragDropGroup({
    fieldOrGroupId: fieldGroup.id,
    index: groupRange.range,
    width,
    notPrimaryDocWithPrimary,
    isGroup: true,
  });

  return (
    <>
      <StyledHeaderCell
        isDragging={isDragging}
        isSelected={isGroupSelected}
        isLastFrozenColumn={false}
        data-field-group-key={fieldGroup.id}
        data-role={`HeaderGroupCell_${fieldGroup.id}`}
        style={highlightStyle}
        ref={drag}
        onClick={handleMouseUp}
      >
        <HeaderGroupWrapper>
          <HeaderGroupedRenderer
            columns={columns}
            fieldGroup={calculatedColumnGroup}
            isLastFrozenColumn={false}
            isSelected={isGroupSelected}
          />
        </HeaderGroupWrapper>

        <ResizeDrag ref={resizeDrag} disabled={!canFieldResize} className="m-header-cell-resizer" />
      </StyledHeaderCell>
      {children}
    </>
  );
};

export default observer(HeaderGroup);

const StyledHeaderCell = styled.div<{
  isLastFrozenColumn: boolean;
  isDragging: boolean;
  isSelected: boolean;
}>`
  position: absolute;
  height: ${HEIGHT.header};
  display: flex;
  align-items: center;
  cursor: ${(props) => (props.isSelected ? 'grab' : 'default')};
  border-top: 1px solid ${COLOR.outerBorder};
  border-left: none;
  .newIA & {
    border-top: none;
  }

  .newIA .Gantt & {
    border-top: 1px solid ${COLOR.outerBorder};
  }

  .newIA & {
    background-color: ${themeCss.color.unstable_grid_header_bg};
  }
`;

const HeaderGroupWrapper = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  flex-direction: column;
`;

const ResizeDrag = styled.div<{ disabled?: boolean }>`
  ${({ disabled }) => (disabled ? '' : 'cursor: col-resize;')}
  position: absolute;
  top: 0;
  right: -7px;
  bottom: 0;
  width: 13px;
  z-index: ${zIndex.resizeDrag};
  background-image: linear-gradient(
    to right,
    transparent 5px,
    transparent 5px 8px,
    transparent 8px 13px
  );
`;
