import { Tooltip, Icon<PERSON>utton, Spin } from '@ali/we-design-next';
import { FieldDTO, isCalcField, isFaasFieldDecorator, mixColors, isFieldDecorator } from '@ali/notable-common';
import { useNotableCore } from '@ali/notable-core';
import { useMemoizedFn } from 'ahooks';
import $i18n from '@ali/notable-i18n';
import { observer } from 'mobx-react-lite';
import React, { useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';
import { themeCss } from '@ali/notable-design';
import { FieldIcon } from '@ali/notable-components';
import ResizeObserver from 'resize-observer-polyfill';
import { DownExpand20, Lock16, FieldpluginAI16 } from '@ali/we-icons';
import { useDesignTokenContext } from '@ali/we-design-token';
import { useConfigStore, useGridStore } from '../../hooks/useStore';
import { CalculatedColumn } from '../../types';
import useOpenEditFieldModal from './useOpenEditFieldModal';
import { DescriptionIcon } from './DescriptionEditor';
import { HeaderInvalidTip } from './HeaderInvalidTip';
import { AIGenerationProgress } from './AIGenerationProgress';
import { UpgradeTag } from './UpgradeTag';

const Wrapper = styled.div<{ showButton: boolean; bgColor?: string; hoverBgColor?: string }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 0 8px;
  background: ${({ bgColor }) => bgColor};

  .more-btn {
    display: none;
    margin-left: 4px;
    width: 20px;
    height: 20px;
    border-radius: 2px;
  }

  .decorator-icon {
    display: none;
  }

  &:hover {
    background: ${({ hoverBgColor }) => hoverBgColor || themeCss.color.overlay_light};
    .more-btn {
      display: block;
    }
    .decorator-icon {
      display: flex;
    }
  }
`;

const TitleAndIcon = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
  margin-left: 5px;
  margin-right: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 13px;
  color: ${themeCss.color.level1};
`;

const TitleWrapper = styled.div`
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
`;

const WarningIconWrapper = styled.div`
  flex-shrink: 1;
  align-items: center;
  display: flex;
  margin-left: 5px;
`;

const Size16Wrap = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-left: 4px;
  border-radius: 3px;
  flex-shrink: 0;
  overflow: hidden;
`;

interface HeaderRendererProps {
  column: CalculatedColumn;
}

function eventPreventDefault(e: React.MouseEvent<HTMLElement, MouseEvent>) {
  e.stopPropagation();
  e.preventDefault();
}

const HeaderRenderer: React.FC<HeaderRendererProps> = ({ column }) => {
  const { sheetId, uiStore, fillColorStore, editingDescField, setEditingDescField } = useGridStore();
  const notableCore = useNotableCore();
  const { model, externalCalcController, globalDataController: { settings: { enableNewIA, enablePrimaryDoc } }, decoratorController, services: { isAdmin } } = notableCore;
  const { aiGenerationController: { columnTasks } } = decoratorController;
  const wrapperRef = useRef<HTMLDivElement>(null);
  const { readonly, disableColumnConfiguration, hideFieldIcon, isEmbedded } = useConfigStore();
  const disableColumnConfig = readonly || disableColumnConfiguration;
  const { openEditFieldModal } = useOpenEditFieldModal(column);
  const fieldPlugin = useMemo(() => {
    return notableCore.pluginController.getFieldPlugin(column.type);
  }, [notableCore, column.type]);
  const titleRef = useRef<HTMLDivElement>(null);
  const [disableTitleTip, setDisableTitleTip] = useState<boolean>(false);
  const { colorScheme } = useDesignTokenContext();
  const cellBgColor = fillColorStore.getColumnBg(column.key, colorScheme);
  const existFieldDecorator = useMemo(() => {
    return isFieldDecorator(column as Partial<FieldDTO>);
  }, [column]);
  const showUpgradeTag = !readonly && column.isPrimary && column.type === 'text' && enablePrimaryDoc && !isEmbedded && isAdmin?.() && !existFieldDecorator;

  const openHeaderCellPanel = useMemoizedFn((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (disableColumnConfig || uiStore.isOperatingOnColumn) {
      return;
    }
    if (wrapperRef.current) {
      const rect = wrapperRef.current.getBoundingClientRect();
      uiStore.toggleHeaderCellPanel(column, rect);
    }
  });

  const [isDoingCalc, setIsDoingCalc] = useState(false);
  useEffect(() => {
    const { fxEngine } = model;
    if (isCalcField(column.type) && fxEngine && sheetId) {
      const offListener = fxEngine.onCalcSliceDone(() => {
        setIsDoingCalc(fxEngine.isFieldDirty(sheetId, column.key));
      });
      return offListener;
    }
  }, [column, model, sheetId]);

  const handleDoubleClick = useMemoizedFn((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!sheetId) {
      return;
    }
    if (!notableCore.abilityManager.can('fieldUpdate', sheetId, column.fieldId)) {
      return;
    }
    if (disableColumnConfig || uiStore.isOperatingOnColumn) {
      return;
    }
    uiStore.setHeaderCellPanelVisible(false);
    openEditFieldModal();
  });

  useLayoutEffect(() => {
    let resizeObserver: ResizeObserver;
    if (titleRef.current) {
      resizeObserver = new ResizeObserver((c: ResizeObserverEntry[]) => {
        if (c[0].target) {
          setDisableTitleTip(!(c[0].target.clientWidth < c[0].target.scrollWidth));
        }
      });
      resizeObserver.observe(titleRef.current);
    }
    return (() => {
      resizeObserver?.disconnect();
    });
  }, []);

  const isAiFieldDecoratorFlag = isFaasFieldDecorator(column as Partial<FieldDTO>);
  const decoratorPlugin = useMemo(() => {
    if (!existFieldDecorator) return null;
    const { innerType } = column.config.decorator || {};
    if (!innerType) return null;
    return notableCore.pluginController.getFieldDecoratorPlugin(innerType) ?? null;
  }, [column, existFieldDecorator, notableCore.pluginController]);

  const task = useMemo(() => {
    return columnTasks.find((item) => {
      return item.sheetId === sheetId && item.fieldId === column.key;
    });
  }, [columnTasks, sheetId, column.key]);

  const hoverBgColor = useMemo(() => {
    if (cellBgColor) {
      return mixColors([cellBgColor, themeCss.color.overlay_light]);
    }
  }, [cellBgColor]);

  return (
    <Wrapper
      ref={wrapperRef}
      onContextMenu={openHeaderCellPanel}
      onDoubleClick={handleDoubleClick}
      showButton={!disableColumnConfig}
      bgColor={cellBgColor}
      hoverBgColor={hoverBgColor}
    >
      {!enableNewIA && column.isPrimary && (
        <Tooltip title={$i18n.t('notable_notableViewFramework_TheFirstColumnIsThe')} placement="top">
          <IconContainer style={{ width: 14, height: 14, marginRight: 4 }}>
            <Lock16 fill={themeCss.color.level1} />
          </IconContainer>
        </Tooltip>
      )}
      {!hideFieldIcon && (
        <Tooltip
          placement="top"
          title={column.isPrimary && column.type === 'primaryDoc' ? $i18n.t('notable_notableViewFramework_primary_doc_tip') : ''}
        >
          <div>
            <FieldIcon
              config={column.config}
              fieldPlugin={fieldPlugin}
              style={{ color: enableNewIA ? themeCss.color.level2 : themeCss.color.level3 }}
            />
          </div>
        </Tooltip>
      )}
      <TitleAndIcon className="header-title">
        <Tooltip
          placement="top"
          bridgeMask={false}
          title={disableTitleTip ? '' : <div style={{ wordBreak: 'break-all', whiteSpace: 'normal' }}>{column.name}</div>}
        >
          <TitleWrapper ref={titleRef}>{column.name}</TitleWrapper>
        </Tooltip>
        {showUpgradeTag && <UpgradeTag />}
        {isAiFieldDecoratorFlag && !task && (
          <FieldpluginAI16 style={{ marginLeft: 6, flexShrink: 0 }} />
        )}
        <DescriptionIcon
          description={column.description}
          disabledTooltip={editingDescField !== null}
          onClick={(ev) => {
            if (disableColumnConfig || !notableCore.abilityManager.can('fieldUpdate', sheetId, column.fieldId)) {
              return;
            }
            // 阻止整列被选中
            ev.stopPropagation();
            setEditingDescField({ fieldId: column.fieldId, type: column.type });
          }}
        />
        <HeaderInvalidTip
          column={column}
          onOpenFieldEditModal={handleDoubleClick}
        />
      </TitleAndIcon>
      {isDoingCalc && externalCalcController.doingCalc && (<Spin size={20} />)}
      {isAiFieldDecoratorFlag && task && (
        <AIGenerationProgress {...task} />
      )}
      {decoratorPlugin && (
        <Tooltip
          title={decoratorPlugin.name}
          description={decoratorPlugin.description?.desc}
          placement="bottom"
        >
          <Size16Wrap className="decorator-icon">
            {React.createElement(decoratorPlugin.icon, { style: { borderRadius: 3 }, size: 16 })}
          </Size16Wrap>
        </Tooltip>
      )}

      {!disableColumnConfig && (
        <IconButton
          size="extra-small"
          className="more-btn"
          onClick={openHeaderCellPanel}
          // 阻止 mouseDown 的冒泡，触发上层监听重新设置 active cell
          onMouseDown={eventPreventDefault}
          icon={<DownExpand20 style={{ color: themeCss.color.level2 }} />}
        />
      )}
    </Wrapper>
  );
};

export default observer(HeaderRenderer);

const IconContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  width: 20px;
  height: 20px;

  path {
    fill: ${themeCss.color.level3};
    fill-opacity: 1;
  }
`;
