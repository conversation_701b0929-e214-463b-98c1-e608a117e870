/* eslint-disable no-console */
import React, { ReactElement, ReactNode, useEffect } from 'react';
import $i18n from '@ali/notable-i18n';
import { toJS } from 'mobx';
import { get } from 'lodash-es';
import { Menu, Modal, Tooltip } from '@ali/we-design-next';
import { observer } from 'mobx-react-lite';
import { useNotableCore } from '@ali/notable-core';
import { useCatalogStore } from '@ali/notable-react';
import { InsertViewOP, validateFrozenColCount } from '@ali/notable-model';
import { Direction, FieldDTO, FieldPlugin, FieldTypes, isFaasFieldDecorator, ViewTypes } from '@ali/notable-common';
import { themeCss } from '@ali/notable-design';
import {
  Copy20,
  Delete20,
  Group20,
  InsertColumnLeft20,
  InsertColumnRight20,
  Filter20,
  Edit20,
  Hide20,
  SortPositive20,
  SortNegative20,
  KanbanViewNew20,
  Calendar<PERSON>iewNew20,
  GalleryViewNew20,
  AIchat20,
  FrozenColumn20,
  Info20,
  Refresh20,
  Iconfont20,
  Fiilcolor20,
} from '@ali/we-icons';
import {
  Column,
  openH5Link,
  shortcutNote,
  SizedBox,
  useLockView,
  automationReminderStore,
  getAutomationAccessMenu,
  AutomationReminderKey,
} from '@ali/notable-components';
import { useConfigStore, useGridStore, useUiStore } from '../../hooks/useStore';
import { truncateColumnName } from '../../utils/truncateColumnName';
import { useGridViewId } from '../../hooks/useUniqIdOfElement';
import { queryHeaderCell } from '../../utils/calcCellSizeAndPosition';
import { useGridController } from '../../hooks/store-provider';
import { CalculatedColumn, CalculatedColumnGroup } from '../../types';
import { getContextCCPMenuOptions } from '../ccpContextMenuOptions';
import useOpenEditFieldModal from './useOpenEditFieldModal';
import { fillCellByStaffId } from './fillCellByStaffid';
import { getMultiSelectColumnMenuOptions } from './multiSelectColumnMenuOptions';
import { getFieldGroupColumnMenuOptions } from './fieldGroupColumnMenuOptions';
import { getSelectGroupMenuOptions, getCombinedMenuOptions } from './selectGroupMenuOptions';

function i18nT(key?: string | null) {
  if (!key) {
    return null;
  }
  return $i18n.t(key);
}

interface ObjectMenuItem {
  key: string;
  title: ReactNode;
  prefix?: ReactNode;
  suffix?: ReactNode;
  disabled?: boolean;
  selected?: boolean;
  // TODO: 将其改为 tooltip，是否展示应该由业务使用方根据 disabled 来决定
  disabledTooltip?: string | ReactNode | null;
}

type MenuItem = ObjectMenuItem | string;

// 单选字段右键菜单面板
function MenuContents({ column }: { column: CalculatedColumn }): ReactElement {
  const { setHeaderCellPanelVisible } = useUiStore();
  const {
    sheetId, viewId, columnStore, viewType, updateCreateFieldModalConfig,
    setEditingDescField, contextMenuStore,
    selectColumns, clearColumnSelection, clearGroupSelection, resetSelectedPosition,
  } = useGridStore();
  const notableCore = useNotableCore();
  const catalogStore = useCatalogStore();
  const gridViewId = useGridViewId();
  const configStore = useConfigStore();
  const gridController = useGridController();
  const { locked, handleLockViewChange } = useLockView();
  const { abilityManager: ability, globalDataController: { settings }, decoratorController } = gridController.api.controller;
  const { api: proxyApi } = notableCore.proxyController;
  const { services: { isAdmin } } = notableCore;
  const { aiGenerationController: { activeColumnTaskFieldName } } = decoratorController;
  useEffect(() => {
    resetSelectedPosition();
    clearColumnSelection();
    clearGroupSelection();
    selectColumns([column.key], null);
  }, [column.key, clearColumnSelection, clearGroupSelection, selectColumns, resetSelectedPosition]);
  // 按「字段名」创建「视图」映射关系
  const viewInsertByFieldConfigMap: Partial<{
    [key in ViewTypes]: FieldTypes[];
  }> = {
    Kanban: notableCore.pluginController.fieldPlugins.filter((i) => !!i.kanbanAdapter).map((i) => i.type),
    Gallery: ['file'],
    Calendar: ['date'],
  };

  const { openEditFieldModal } = useOpenEditFieldModal(column);
  const getHeaderCell = () => queryHeaderCell({ gridViewId, fieldId: column.key, type: column.type });

  const onSelect = async (paths: string[]) => {
    const [majorKey, secondaryKey] = paths;
    if (majorKey === 'edit') {
      openEditFieldModal();
    } else if (majorKey === 'insertLeft') {
      const position = getHeaderCell()?.getBoundingClientRect() || {
        bottom: 0,
        left: 0,
      };

      updateCreateFieldModalConfig({
        positionX: position.left,
        positionY: position.bottom,
        referFieldId: column.key,
        insertAfterRefer: false,
        fixed: !!column.isPrimary,
      });
    } else if (majorKey === 'insertRight') {
      const position = getHeaderCell()?.getBoundingClientRect() || {
        bottom: 0,
        left: 0,
        right: 0,
      };

      updateCreateFieldModalConfig({
        positionX: position.right,
        positionY: position.bottom,
        referFieldId: column.key,
        insertAfterRefer: true,
        fixed: !!column.isPrimary,
      });
    } else if (majorKey === 'copy') {
      const { api } = gridController;
      await api.controller.fieldController.duplicateField(sheetId, column.key);
    } else if (majorKey === 'sortAsc') {
      if (locked) return handleLockViewChange();
      gridController.onAddViewSortCriteria({ fieldId: column.key, direction: Direction.ASC });
    } else if (majorKey === 'sortDesc') {
      if (locked) return handleLockViewChange();
      gridController.onAddViewSortCriteria({ fieldId: column.key, direction: Direction.DESC });
    } else if (majorKey === 'hide') {
      if (locked) return handleLockViewChange();
      gridController.onHideColumn(sheetId, viewId, [column.key]);
    } else if (majorKey === 'filter') {
      if (locked) return handleLockViewChange();
      gridController.onAddFilterOfField(column.key);
    } else if (majorKey === 'group') {
      if (locked) return handleLockViewChange();
      gridController.onAddViewGroupCriteria(column.key);
    } else if (majorKey === 'delete') {
      setTimeout(() => {
        const { controller } = gridController.api;
        const fieldPlugin = controller.pluginController.getFieldPlugin(column.type) as FieldPlugin;

        const fieldName = column.name;
        const confirmText = fieldPlugin?.getConfirmTextWhenDeleteOrChangeToOthers?.({
          isDelete: true,
          fieldName,
          sheetId,
          originFieldId: column.fieldId,
        }) || $i18n.t('we_notable_confirm_deletion_of_field_with_extra_info', {
          fieldName,
          extraInfo: $i18n.t('we_notable_restore_original_field_by_undoing', {
            shortcut: shortcutNote.getNoteText('Z'),
          }),
        });

        Modal.message(confirmText, {
          title: $i18n.t('notable_notableGridView_DeleteAFieldOrColumn'),
          cancelText: $i18n.t('we_notable_cancel'),
          confirmText: $i18n.t('we_notable_delete'),
          mask: true,
          onConfirm: () => {
            gridController.api.deleteField({ fieldId: column.key });
          },
        });
      }, 50);
    } else if (majorKey === 'insertKanbanViewByColumn') {
      let result: InsertViewOP | undefined;
      notableCore.batchStart('InsertViewByField');
      try {
        result = notableCore.viewController.insertTail(sheetId, 'Kanban');
        if (viewInsertByFieldConfigMap.Kanban?.includes(column.type)) {
          const kanbanViewId = result?.payload.value.id;
          if (proxyApi && kanbanViewId && result?.payload.value.type === 'Kanban') {
            const { custom } = result?.payload.value;
            const canUpdateView = ability.can('viewUpdate', sheetId, kanbanViewId);
            if (canUpdateView) {
              proxyApi.updateView({
                viewId: kanbanViewId,
                paths: ['custom', 'groupBase'],
                value: { ...custom.groupBase, baseFieldId: column.key },
                origin: custom.groupBase,
              });
            }
          }
        }
      } finally {
        notableCore.batchEnd();
      }
      if (result) {
        const value = get(result, 'payload.value', null);
        if (value) {
          catalogStore.setActiveId(value.id, sheetId);
        }
      }
    } else if (majorKey === 'insertGalleryViewByColumn') {
      let result: InsertViewOP | undefined;
      notableCore.batchStart('InsertViewByField');
      try {
        result = notableCore.viewController.insertTail(sheetId, 'Gallery');
        if (viewInsertByFieldConfigMap.Gallery?.includes(column.type)) {
          const galleryViewId = result?.payload.value.id;
          if (proxyApi && galleryViewId && result?.payload.value.type === 'Gallery') {
            const { custom } = result?.payload.value;
            const canUpdateView = ability.can('viewUpdate', sheetId, galleryViewId);
            if (canUpdateView) {
              proxyApi.updateView({
                viewId: galleryViewId,
                paths: ['custom', 'cardSetting'],
                value: {
                  ...custom?.cardSetting,
                  coverFieldId: column.key,
                  coverMode: 'custom',
                },
                origin: custom?.cardSetting,
              });
            }
          }
        }
      } finally {
        notableCore.batchEnd();
      }
      if (result) {
        const value = get(result, 'payload.value', null);
        if (value) {
          catalogStore.setActiveId(value.id, sheetId);
        }
      }
    } else if (majorKey === 'insertCalendarViewByColumn') {
      const result = notableCore.viewController.insertTail(sheetId, 'Calendar');
      if (result) {
        if (result) {
          const value = get(result, 'payload.value', null);
          if (value) {
            catalogStore.setActiveId(value.id, sheetId);
          }
        }
      }
    } else if (majorKey === 'fillCellByStaffIn') {
      fillCellByStaffId(gridController.api.controller, sheetId, column.fieldId);
    } else if (majorKey === 'frozen') {
      const { frozenColCount } = columnStore;
      const newFrozenColCount = validateFrozenColCount(column.sortedIndex + 1, columnStore.allColumnsIncludingHiddenCount);
      if (newFrozenColCount !== frozenColCount) {
        sheetId && notableCore.viewController.updateCustomValue(sheetId, viewId, 'frozenColCount', newFrozenColCount);
      }
    } else if (majorKey === 'editDescription') {
      setEditingDescField({
        fieldId: column.fieldId,
        type: column.type,
      });
    } else if (majorKey === 'regenerateAI') {
      Modal.confirm($i18n.t('当前字段的所有记录将会更新，包括该字段生成的所有相关数据'), {
        title: $i18n.t('确定要更新整列记录吗'),
        confirmText: $i18n.t('更新'),
        mask: true,
        onConfirm: async () => {
          notableCore.decoratorController.aiGenerationController.triggerAIGeneration({
            sheetId,
            fieldIds: [column.fieldId],
            recordIds: [],
            all: true,
          });
        },
      });
    } else if (majorKey === 'fillColorOfField') {
      if (locked) return handleLockViewChange();
      gridController.onAddFillColorOfField([column.key]);
    } else if (majorKey === 'moveOutOfFieldGroup') {
      contextMenuStore.handleMoveOutOfFieldGroup();
    } else if (majorKey === 'moveIntoFieldGroup') {
      contextMenuStore.handleMoveIntoFieldGroup(paths);
    } else if (majorKey === 'dissolveFieldGroup') {
      contextMenuStore.handleDissolveFieldGroup();
    } else if (majorKey === 'newFieldGroup') {
      contextMenuStore.handleNewFieldGroup();
    } else if (majorKey === 'hideFieldGroup') {
      if (locked) return handleLockViewChange();
      gridController.onHideColumn(sheetId, viewId, [column.key]);
    } else if (majorKey === AutomationReminderKey) {
      automationReminderStore.openModal(secondaryKey, { fieldId: column.fieldId, source: 'grid-header' });
    } else {
      contextMenuStore.handleCCPCommand(majorKey);
    }

    if (process.env.NODE_ENV === 'development') {
      if (majorKey === 'dev_field_info') {
        console.log('DEV: sheetId', sheetId);
        console.log('DEV: field_info', toJS(column));
      }
    }

    setHeaderCellPanelVisible(false);
  };
  const { readonlyAllowCopyCut, enableAutomationQuickAccess } = settings;
  const { readonly } = configStore;
  const canCopy = !(readonly && !readonlyAllowCopyCut);
  const canPaste = !readonly;
  const isPrimaryDocField = column.type === 'primaryDoc';

  const items: MenuItem[] = [
    {
      key: 'edit',
      title: $i18n.t('notable_notableGridView_ModifyFieldsOrColumns'),
      prefix: <Edit20 fill={themeCss.color.level1} />,
      disabled: !ability.can('fieldUpdate', sheetId, column.fieldId),
      disabledTooltip: i18nT(ability.cannotReason('fieldUpdate', sheetId, column.fieldId)?.[1]),
    },
    {
      key: 'editDescription',
      title: $i18n.t('edit_field_desc'),
      prefix: <Info20 fill={themeCss.color.level1} />,
      disabled: !ability.can('fieldUpdate', sheetId, column.fieldId),
      disabledTooltip: i18nT(ability.cannotReason('fieldUpdate', sheetId, column.fieldId)?.[1]),
    },
    ...(
      (viewType === 'Grid') ? [{
        key: 'fillColorOfField',
        title: $i18n.t('notable_notableGridView_FillColorOfColumn'),
        disabled: !ability.can('viewFillColor', sheetId, viewId) && !locked,
        prefix: <Fiilcolor20 fill={themeCss.color.level1} />,
      }] : []
    ),
    ...(
      (!configStore.hideHideColumn && !column.isPrimary) ? [{
        key: 'hide',
        title: $i18n.t('notable_notableGridView_HideFieldsOrColumns'),
        disabled: !ability.can('viewHideFields', sheetId, viewId) && !locked, // 视图锁定下放开并提示用户解锁
        prefix: <Hide20 fill={themeCss.color.level1} />,
      }] : []
    ),
    ...(
      viewType === 'Grid' && isFaasFieldDecorator(column as Partial<FieldDTO>) ? [{
        key: 'regenerateAI',
        title: $i18n.t('update_content'),
        disabled: !ability.can('recordUpdate', sheetId, column.fieldId) || activeColumnTaskFieldName !== null,
        prefix: <Refresh20 fill={themeCss.color.level1} />,
        disabledTooltip: (
          i18nT(ability.cannotReason('recordUpdate', sheetId, column.fieldId)?.[1])
          ?? $i18n.t('ai_in_progress_save_later', { name: activeColumnTaskFieldName })
        ),
      }] : []),
    ...(
      viewType === 'Grid' && !column?.fieldGroupId ? [{
        key: 'frozen',
        title: $i18n.t('notable_notableGridView_FrozenFieldsOrColumns'),
        disabled: !configStore.canFieldResize || !ability.can('fieldFrozen', sheetId, column.fieldId),
        prefix: <FrozenColumn20 fill={themeCss.color.level1} />,
        disabledTooltip: i18nT(ability.cannotReason('fieldUpdate', sheetId, column.fieldId)?.[1]),
      }] : []
    ),
    ...(
      enableAutomationQuickAccess && isAdmin?.()
        ? getAutomationAccessMenu()
        : []
    ),
    ...getFieldGroupColumnMenuOptions({
      core: notableCore,
      rootStore: gridController.gridStore,
      divider: true,
    }) as MenuItem[],
    'divider',
    ...getContextCCPMenuOptions({ canCopy, canPaste }),
    'divider',
    ...[
      !isPrimaryDocField ? {
        key: 'copy',
        title: $i18n.t('notable_notableGridView_CopyFieldsOrColumns'),
        prefix: <Copy20 fill={themeCss.color.level1} />,
        disabled: !(
          ability.can('fieldCreate', sheetId)
          && ability.can('fieldCreateOfType', sheetId, column.type)
          && ability.can('fieldDuplicate', sheetId, column.fieldId)
        ),
        disabledTooltip: i18nT(ability.cannotReason('fieldDuplicate', sheetId, column.fieldId)?.[1]),
      } : '',
      !column.frozen || isPrimaryDocField ? {
        key: 'insertLeft',
        title: $i18n.t('notable_notableGridView_InsertAFieldOrColumn'),
        prefix: <InsertColumnLeft20 fill={themeCss.color.level1} />,
        disabled: (column.frozen && !isPrimaryDocField) || !ability.can('fieldCreate', sheetId),
        disabledTooltip: i18nT(ability.cannotReason('fieldCreate', sheetId)?.[1]),
      } : '',
      {
        key: 'insertRight',
        title: $i18n.t('notable_notableGridView_InsertAFieldOrColumn1'),
        prefix: <InsertColumnRight20 fill={themeCss.color.level1} />,
        disabled: !ability.can('fieldCreate', sheetId),
        disabledTooltip: i18nT(ability.cannotReason('fieldCreate', sheetId)?.[1]),
      },
    ].filter((item) => !!item),
    'divider',
  ];

  if (process.env.NODE_ENV === 'development') {
    items.unshift({
      key: 'dev_field_info',
      title: 'DEV: log 字段信息',
      prefix: <Iconfont20 />,
    });
  }

  if (!configStore.hideSortByColumn) {
    // 锁定下需放开点击但提示锁定。
    const disabled = column.disableSort || (!ability.can('viewSort', sheetId, viewId) && !locked);
    items.push(...[
      {
        key: 'sortAsc',
        title: $i18n.t('notable_notableGridView_PositiveSequence'),
        disabled,
        prefix: <SortPositive20 fill={themeCss.color.level1} />,
      },
      {
        key: 'sortDesc',
        title: $i18n.t('notable_notableGridView_Reverse'),
        disabled,
        prefix: <SortNegative20 fill={themeCss.color.level1} />,
      },
      'divider',
    ]);
  }

  if (
    !configStore.hideFilterColumn ||
    !configStore.hideGroupByColumn
  ) {
    // 锁定下需放开点击但提示锁定。
    const groupDisabled = column.disableGroup || (!ability.can('viewGroup', sheetId, viewId) && !locked);
    const filterDisabled = column.disableFilter || (!ability.can('viewFilter', sheetId, viewId) && !locked);
    items.push(...[
      !configStore.hideGroupByColumn ? {
        key: 'group',
        title: $i18n.t('notable_notableGridView_GroupByColumnname', {
          columnName: truncateColumnName(column.name),
        }),
        prefix: <Group20 fill={themeCss.color.level1} />,
        disabled: groupDisabled,
      } : null,
      !configStore.hideFilterColumn ? {
        key: 'filter',
        title: $i18n.t('notable_notableGridView_FilterByColumnname', {
          columnName: truncateColumnName(column.name),
        }),
        disabled: filterDisabled,
        prefix: <Filter20 fill={themeCss.color.level1} />,
      } : null,
      'divider',
    ].filter(<T,>(i: T | undefined | null): i is T => !!i));
  }

  const canInsertKanbanView = viewInsertByFieldConfigMap?.Kanban?.includes(column.type);
  const canInsertGalleryView = viewInsertByFieldConfigMap?.Gallery?.includes(column.type);
  const canInsertCalendarView = viewInsertByFieldConfigMap?.Calendar?.includes(column.type);

  if (canInsertKanbanView || canInsertGalleryView || canInsertCalendarView) {
    const insertViewItems: MenuItem[] = [];
    if (canInsertKanbanView) {
      insertViewItems.push({
        key: 'insertKanbanViewByColumn',
        title: $i18n.t('notable_notableGridView_InsertViewByField', {
          columnName: truncateColumnName(column.name),
          viewName: $i18n.t('notable_notableKanbanView_KanbanView'),
        }),
        disabled: settings.readonly || !ability.can('viewCreate', sheetId),
        prefix: <KanbanViewNew20 fill={themeCss.color.level1} />,
      });
    }
    if (canInsertGalleryView) {
      insertViewItems.push({
        key: 'insertGalleryViewByColumn',
        title: $i18n.t('notable_notableGridView_InsertViewByField', {
          columnName: truncateColumnName(column.name),
          viewName: $i18n.t('notable_notableGalleryView_AlbumView'),
        }),
        disabled: settings.readonly || !ability.can('viewCreate', sheetId),
        prefix: <GalleryViewNew20 fill={themeCss.color.level1} />,
      });
    }
    if (canInsertCalendarView) {
      insertViewItems.push({
        key: 'insertCalendarViewByColumn',
        title: $i18n.t('notable_notableGridView_InsertViewByField', {
          columnName: truncateColumnName(column.name),
          viewName: $i18n.t('notable_notableCalendarView_CalendarView'),
        }),
        disabled: settings.readonly || !ability.can('viewCreate', sheetId),
        prefix: <CalendarViewNew20 fill={themeCss.color.level1} />,
      });
    }

    if (insertViewItems.length > 0) {
      items.push(...[
        ...insertViewItems,
        'divider',
      ]);
    }
  }

  if (!configStore.canFieldDeleteFn(column.fieldId)) {
    items.push({
      key: 'delete',
      title: $i18n.t('notable_notableGridView_DeleteAFieldOrColumn'),
      prefix: <Delete20 fill={themeCss.color.level1} />,
      disabled: column.isPrimary || !ability.can('fieldDelete', sheetId, column.fieldId),
      disabledTooltip: (
        <Column>
          <span>{$i18n.t('notable_notableGridView_DeleteAFieldOrColumn_primary_field_notice')}</span>
          <SizedBox height={8} />
          <span
            style={{ color: themeCss.color.brand1, cursor: 'pointer' }}
            onClick={() =>
              openH5Link('https://wolai.dingtalk.com/3j8RubbDNtfRnQp7xG1DfE?dd_full_screen=true')
            }
          >
            {$i18n.t('notable_notableGridView_DeleteAFieldOrColumn_primary_field_learnMore')}
          </span>
        </Column>
      ),
    });
  }

  const { allowFillCellByStaffId } = gridController.api.controller.fieldController.settings;
  const { enablePrimaryDoc } = gridController.api.controller.globalDataController.settings;

  // 临时给大客提供填充工号的方法
  if (allowFillCellByStaffId && column.type === 'text') {
    items.push('divider');
    items.push({
      key: 'fillCellByStaffIn',
      title: '使用工号填充',
      prefix: <AIchat20 fill={themeCss.color.level1} />,
    });
  }

  return (
    <Menu
      style={{ minWidth: 200 }}
      items={items}
      onSelect={onSelect}
      renderItem={(it) => {
        const item = items.find((i): i is ObjectMenuItem => (get(i, 'key') === it.key));
        if (item?.disabled && item.disabledTooltip) {
          return (
            <Tooltip title={item.disabledTooltip} placement="top">
              <Menu.Item />
            </Tooltip>
          );
        }
        return <Menu.Item />;
      }}
    />
  );
}

// 多选字段右键菜单面板
function MultiSelectMenuContents(): ReactElement | null {
  const { uiStore, contextMenuStore } = useGridStore();
  const gridController = useGridController();
  const notableCore = useNotableCore();
  const { locked, handleLockViewChange } = useLockView();
  const { setHeaderCellPanelVisible } = uiStore;

  const onSelect = (path: string[]) => {
    if (path[0] === AutomationReminderKey) {
      automationReminderStore.openModal(path[1], { source: 'grid-multi-header' });
    } else {
      contextMenuStore.onMenuSelect(path, { locked, handleLockViewChange });
    }

    setHeaderCellPanelVisible(false);
  };

  const items = getMultiSelectColumnMenuOptions({
    core: notableCore,
    rootStore: gridController.gridStore,
  });

  if (items.length === 0) {
    return null;
  }

  return (<Menu data-notable-in-grid style={{ minWidth: 200 }} items={items} onSelect={onSelect} />);
}

function GroupMenuContents({ group }: { group: CalculatedColumnGroup }): ReactElement | null {
  const {
    clearColumnSelection, clearGroupSelection,
    selectGroups, resetSelectedPosition,
  } = useGridStore();
  const gridController = useGridController();
  const notableCore = useNotableCore();


  useEffect(() => {
    resetSelectedPosition();
    clearColumnSelection();
    clearGroupSelection();
    selectGroups([group.id], null);
  }, [group.id, clearColumnSelection, clearGroupSelection, selectGroups, resetSelectedPosition]);

  const items = getSelectGroupMenuOptions({
    core: notableCore,
    rootStore: gridController.gridStore,
  });

  if (items.length === 0) {
    return null;
  }

  return (<BaseMenuContents items={items} />);
}

function MultiGroupMenuContents(): ReactElement | null {
  const gridController = useGridController();
  const notableCore = useNotableCore();


  const items = getSelectGroupMenuOptions({
    core: notableCore,
    rootStore: gridController.gridStore,
  });

  if (items.length === 0) {
    return null;
  }

  return (<BaseMenuContents items={items} />);
}

function CombinedMenuContents(): ReactElement | null {
  const gridController = useGridController();
  const notableCore = useNotableCore();

  const items = getCombinedMenuOptions({
    core: notableCore,
    rootStore: gridController.gridStore,
  });

  if (items.length === 0) {
    return null;
  }

  return (<BaseMenuContents items={items} />);
}

function BaseMenuContents({ items }: { items: React.ComponentProps<typeof Menu>['items']}): ReactElement | null {
  const {
    uiStore, contextMenuStore,
  } = useGridStore();

  const { locked, handleLockViewChange } = useLockView();
  const { setHeaderCellPanelVisible } = uiStore;


  const onSelect = (path: string[]) => {
    contextMenuStore.onMenuSelect(path, { locked, handleLockViewChange });
    setHeaderCellPanelVisible(false);
  };


  if (!items || items.length === 0) {
    return null;
  }

  return (<Menu style={{ minWidth: 200 }} items={items} onSelect={onSelect} />);
}

export const HeaderMenuContents = observer(MenuContents);
export const HeaderGroupMenuContents = observer(GroupMenuContents);
export const HeaderMultiGroupMenuContents = observer(MultiGroupMenuContents);
export const HeaderCombinedMenuContents = observer(CombinedMenuContents);
export const HeaderMultiSelectMenuContents = observer(MultiSelectMenuContents);
