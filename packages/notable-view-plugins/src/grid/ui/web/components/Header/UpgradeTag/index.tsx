import React, { useState } from 'react';
import $i18n from '@ali/notable-i18n';
import { Popover, Toast } from '@ali/we-design-3';
import styled from 'styled-components';
import { useNotableCore } from '@ali/notable-core';
import { themeVars } from '@ali/notable-design';
import { PrimaryUpgradeModal } from '@ali/notable-components';
import { safeGetStorage, safeSetStorage, primaryDocCreateDocGuideLocalKey } from '@ali/notable-common';
import { UpgradeCard } from './UpgradeCard';

const Tag = styled.div`
  border-radius: 8px 8px 8px 0;
  font-size: 10px;
  font-weight: 500;
  padding: 2px 4px;
  line-height: 12px;
  margin-left: 6px;
  color: ${themeVars.color.red_6};
  background: ${themeVars.color.red_0};
`;

export const UpgradeTag = () => {
  const [visible, setVisible] = useState<boolean>(false);
  const notableCore = useNotableCore();
  const { fieldController, frameworkController, layoutController } = notableCore;
  const handleUpgrade = async () => {
    try {
      await fieldController.upgradePrimaryDocFieldAllSheets();
      const queryModel = frameworkController.currentViewQueryModel;
      let rowId = '';
      if (queryModel) {
        rowId = queryModel.records[0].id;
      }
      Toast.success($i18n.t('we_notable_upgrade_primary_doc_success_tip'));
      const needShow = safeGetStorage(primaryDocCreateDocGuideLocalKey);
      if (needShow !== '0') {
        safeSetStorage(primaryDocCreateDocGuideLocalKey, '1');
      }
      if (rowId && frameworkController.currentSheetId && frameworkController.currentViewId) {
        layoutController.expandRow(
          frameworkController.currentSheetId,
          frameworkController.currentViewId,
          rowId,
        );
      }
    } catch {
      // empty
    }
  };
  return (
    <>
      <Popover
        trigger="mouse-enter"
        content={
          <UpgradeCard handleUpgrade={handleUpgrade} showPrimaryUpgradeModal={() => setVisible(true)} />
        }
      >
        <Tag>{$i18n.t('upgrade_confirm')}</Tag>
      </Popover>

      <PrimaryUpgradeModal
        existPrimaryDoc={false}
        handleUpgrade={handleUpgrade}
        visible={visible}
        closeModal={() => {
          setVisible(false);
        }}
      />
    </>
  );
};
