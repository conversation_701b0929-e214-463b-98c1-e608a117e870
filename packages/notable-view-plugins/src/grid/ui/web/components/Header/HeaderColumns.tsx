import { observer } from 'mobx-react-lite';
import React, { useMemo } from 'react';
import { groupBy } from 'lodash-es';
import { useNotableCore } from '@ali/notable-core';
import { useMemoizedFn } from 'ahooks';
import { useColumnStore } from '../../hooks/useStore';
import { CalculatedColumn } from '../../types';
import HeaderGroup from './HeaderGroup';
import HeaderCell from './HeaderCell';

interface Props {
  columns: CalculatedColumn[];
  canFieldResize: boolean;
  positionOffset?: number;
}

const GroupedColumns = ({ columns, canFieldResize, positionOffset = 0 }: Props) => {
  const { fieldGroupController } = useNotableCore();
  const columnStore = useColumnStore();

  const renderedGroups = new Set<string>();

  const groupMap = useMemo(() => {
    return groupBy(columns, (column) => column.fieldGroupId);
  }, [columns]);

  const renderHeaderCell = useMemoizedFn((column: CalculatedColumn, grouped?: boolean) => (
    <HeaderCell
      key={column.key}
      column={{
        ...column,
        left: column.isPrimary ? column.left : column.left + positionOffset,
        width: column.isPrimary ? column.width + positionOffset : column.width,
      }}
      grouped={grouped}
      canFieldResize={canFieldResize}
      positionOffset={column.isPrimary ? positionOffset : 0}
    />
  ));

  const renderItems = useMemoizedFn((items: CalculatedColumn[], grouped = false) => {
    return (
      <React.Fragment>{items.map((column) => renderHeaderCell(column, grouped))}</React.Fragment>
    );
  });

  if (!fieldGroupController.enable) {
    return renderItems(columns);
  }

  return (
    <React.Fragment>
      {columns.map((column) => {
        const { fieldGroupId } = column;
        const fieldGroup = columnStore.fieldGroupMap[fieldGroupId];
        const groupedColumns = fieldGroupId ? groupMap[fieldGroupId] : null;
        if (fieldGroup && groupedColumns && !renderedGroups.has(fieldGroupId)) {
          renderedGroups.add(fieldGroupId);

          return (
            <HeaderGroup
              key={fieldGroupId}
              canFieldResize={canFieldResize}
              fieldGroup={fieldGroup}
              positionOffset={positionOffset}
              columns={groupedColumns}
            >
              {renderItems(groupedColumns, true)}
            </HeaderGroup>
          );
        } else if (!fieldGroup) {
          return renderHeaderCell(column);
        }
        return null;
      })}
    </React.Fragment>
  );
};

export default observer(GroupedColumns);
