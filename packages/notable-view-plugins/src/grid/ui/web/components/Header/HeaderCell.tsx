import { observer } from 'mobx-react-lite';
import React from 'react';
import classNames from 'classnames';
import styled from 'styled-components';
import { useFieldHighlightHeaderStyle } from '@ali/notable-components';
import { themeCss } from '@ali/notable-design';
import { useNotableCore } from '@ali/notable-core';
import { COLOR } from '../../constants/color';
import { useColumnStore } from '../../hooks/useStore';
import { CalculatedColumn } from '../../types';
import zIndex from '../../constants/zIndex';
import HEIGHT from '../../constants/height';
import HeaderRenderer from './HeaderRenderer';
import { useHeaderDragDropGroup } from './useHeaderDragDropGroup';
import { useResizeColumn } from './useResizeColumn';
import { useIsColumnSelected } from './useIsColumnSelected';
import { ResizeDrag as NewResizeFrag } from './ResizeDrag';


interface Props {
  column: CalculatedColumn;
  canFieldResize: boolean;
  positionOffset: number;
  grouped?: boolean;
}

const HeaderCell = ({ column, canFieldResize, grouped, positionOffset }: Props) => {
  const isColumnSelected = useIsColumnSelected(column.key);
  const { globalDataController: { settings: { enableColumnResizeContextMenu } }, frameworkController: { currentView } } = useNotableCore();
  const [drag, isDragging, handleMouseUp] = useHeaderDragDropGroup({
    fieldOrGroupId: column.key,
    index: column.idx,
    width: column.width,
    notPrimaryDocWithPrimary: column.isPrimary,
    // notPrimaryDocWithPrimary: column.isPrimary && column.type !== 'primaryDoc',
    isGroup: false,
  });
  const [resizeDrag] = useResizeColumn({
    frozen: !!column.frozen,
    fieldId: column.key,
    index: column.idx,
    width: column.width,
    canDrag: canFieldResize,
    positionOffset,
  });
  const { adaptViewportFrozenColCount, noLeftBorder } = useColumnStore();
  // 这个字段目前没有用到
  const isLastFrozenColumn = column.sortedIndex === adaptViewportFrozenColCount - 1;
  // 对于新信息架构，左侧没有 border，对于 header 的左侧 border 与 row 的实现不一致，所以不能直接使用 row 的 left
  // 因为 row 的 left 是新增了一个 border 的所以此时需要减去 1
  const left = noLeftBorder && column.sortedIndex < adaptViewportFrozenColCount ? column.left - 1 : column.left;
  const highlightStyle = useFieldHighlightHeaderStyle(column.key, {
    width: column.width,
    left,
    height: grouped ? HEIGHT.header : '100%',
    top: grouped ? HEIGHT.header : 0,
  });
  // 没有视图的 update 权限就无法调整列宽
  return (
    <StyledHeaderCell
      className={classNames('thumbnail', {
        isGrouped: Boolean(grouped),
      })}
      isDragging={isDragging}
      isSelected={isColumnSelected}
      isLastFrozenColumn={isLastFrozenColumn}
      data-column-key={column.key}
      isGrouped={Boolean(grouped)}
      data-role={`HeaderCell_${column.type}`}
      aria-colindex={column.idx + 1}
      style={highlightStyle}
      ref={drag}
      onClick={handleMouseUp}
      {...(column.isPrimary ? { 'data-primary': true } : {})}
    >
      <HeaderRenderer column={column} />
      {
        enableColumnResizeContextMenu && currentView?.type === 'Grid' ? (
          <NewResizeFrag
            column={column}
            ref={resizeDrag}
            disabled={!canFieldResize}
            className="m-header-cell-resizer"
          />
        ) : (
          <ResizeDrag
            ref={resizeDrag}
            disabled={!canFieldResize}
            className="m-header-cell-resizer"
          />
        )
      }
    </StyledHeaderCell>
  );
};
// StyledHeaderCell去掉了index，确认一下这个index为什么是3
// z-index: ${zIndex.dataCell};
const StyledHeaderCell = styled.div<{
  isLastFrozenColumn: boolean;
  isDragging: boolean;
  isSelected: boolean;
  isGrouped: boolean;
}>`
  position: absolute;
  height: 100%;
  display: flex;
  align-items: center;
  cursor: ${(props) => (props.isSelected ? 'grab' : 'default')};
  border-top: 1px solid ${COLOR.outerBorder};
  border-right: 1px solid ${({ isLastFrozenColumn }) => (isLastFrozenColumn ? 'transparent' : COLOR.innerBorder)};
  background: ${(props) => (props.isSelected ? COLOR.selectedRowBgColor : COLOR.defaultRowBgColor)};
  border-left: none;

  .newIA & {
    border-top: none;
  }

  .newIA .Gantt & {
    :not(.isGrouped) {
      border-top: 1px solid ${COLOR.outerBorder};
    }
  }

  .newIA & {
    background-color: ${themeCss.color.unstable_grid_header_bg};
  }
`;

const ResizeDrag = styled.div<{ disabled?: boolean }>`
  ${({ disabled }) => (disabled ? '' : 'cursor: col-resize;')}
  position: absolute;
  top: 0;
  right: -7px;
  bottom: 0;
  width: 13px;
  z-index: ${zIndex.resizeDrag};
  background-image: linear-gradient(to right, transparent 5px, transparent 5px 8px, transparent 8px 13px);
`;
// 层级有点问题，后续改进
// :hover {
//   background-image: linear-gradient(to right, transparent 5px, ${themeCss.color.brand1} 5px 8px, transparent 8px 13px);
// }
export default observer(HeaderCell);
