import { useEffect, useMemo, useRef } from 'react';
import { ConnectDragSource, useDrag } from 'react-dnd';
import { last } from 'lodash-es';
import { useMemoizedFn } from 'ahooks';
import { getEmptyImage } from 'react-dnd-html5-backend';
import { Message } from '@ali/we-design-next';
import $i18n from '@ali/notable-i18n';
import { Column } from '@ali/notable-common';
import type { Operation } from '@ali/notable-model';
import { useLockView, FunctionalModal } from '@ali/notable-components';
import { useNotableCore } from '@ali/notable-core';
import { useConfigStore } from '../../hooks/useStore';
import { useGridController } from '../../hooks/store-provider';

interface DragDropColumnProps {
  fieldOrGroupId: string;
  index: number | [number, number];
  width: number;
  notPrimaryDocWithPrimary: boolean;
  isGroup?: boolean;
}

type UseHeaderDragDropGroup = (
  payload: DragDropColumnProps,
) => [ConnectDragSource, boolean, (e: React.MouseEvent) => void];

export const useHeaderDragDropGroup: UseHeaderDragDropGroup = ({
  fieldOrGroupId,
  index,
  notPrimaryDocWithPrimary,
  width,
  isGroup,
}) => {
  const core = useNotableCore();
  const { fieldGroupController } = core;
  const gridController = useGridController();
  const { gridStore, dragType } = gridController;
  const { columnStore, rowStore, uiStore, sheetId } = gridStore;
  const { readonly, canFieldReorder } = useConfigStore();
  const { locked, handleLockViewChange } = useLockView();
  const action = useRef<'move' | 'select'>('move');

  const selectHeader = useMemoizedFn(() => {
    if (isGroup) {
      gridStore.selectGroups([fieldOrGroupId], index);
    } else {
      gridStore.selectColumns([fieldOrGroupId], index);
    }
  });

  const toggleHeaderSelection = useMemoizedFn(() => {
    if (isGroup) {
      gridStore.toggleGroupSelection(fieldOrGroupId, index);
    } else {
      gridStore.toggleColumnSelection(fieldOrGroupId, index);
    }
  });

  const fieldId = useMemo(() => {
    if (isGroup) {
      return columnStore.getVisibleLeftColumnOfGroup(fieldOrGroupId)?.key || '';
    }
    return fieldOrGroupId;
  }, [isGroup, fieldOrGroupId, columnStore]);

  const handleSelection = useMemoizedFn(() => {
    gridStore.clearColumnSelection();
    gridStore.clearGroupSelection();

    selectHeader();
    if (rowStore.firstRowId && fieldId) {
      gridStore.selectCell({
        position: { fieldId, rowId: rowStore.getVisibleFirstRowId() },
        isSelectingColumn: true,
      });
    }
  });

  const handleClick = (e: React.MouseEvent) => {
    action.current = 'select';
    const isCtrlPressed = e.metaKey || e.ctrlKey;
    if (!isCtrlPressed && !e.shiftKey) {
      // 单选
      handleSelection();
    } else if (e.shiftKey) {
      // 范围选
      if (gridStore.selectedColumnPivotRange !== null) {
        columnStore.selectColumnRange(gridStore.selectedColumnPivotRange, index);
      } else {
        handleSelection();
      }
    } else if (isCtrlPressed) {
      // 多选
      toggleHeaderSelection();
    }
  };

  const confirmAutoDeleteEmptyGroup = useMemoizedFn(() => {
    return FunctionalModal.warn($i18n.t('auto_delete_field_group_after_last_field_removed'), {
      title: $i18n.t('we_notable_confirm_move_out_of_field_group'),
      cancelText: $i18n.t('we_notable_cancel'),
      confirmText: $i18n.t('we_notable_move_out'),
      mask: true,
    });
  });

  const handleDragDropFieldGroup = useMemoizedFn(async (isDropInGroup: boolean) => {
    if (!gridStore.selectedGroupsSet.size && gridStore.selectedColumnsSet.size) {
      const selectedColumnIds = Array.from(gridStore.selectedColumnsSet);
      // 字段拖入组中
      const groupIdOfSelectedColumnIds = new Set(
        selectedColumnIds.map((columnId) => columnStore.getColumnByKey(columnId)?.fieldGroupId),
      );
      if (groupIdOfSelectedColumnIds.size > 1) {
        console.error('拖拽数据异常，不支持跨层选择');
        return;
      }

      const [fieldGroupId] = Array.from(groupIdOfSelectedColumnIds);

      const isDragFromGroup = Boolean(fieldGroupId);

      /**
       * 此处仅处理形成编组关系或断开编组关系，即
       * 1. 移出编组
       * 2. 移入编组
       */
      if (isDragFromGroup && !isDropInGroup) {
        // 移出编组
        const result = await fieldGroupController.moveFieldsOutOfGroup({
          sheetId,
          fieldIds: selectedColumnIds,
          originGroupId: fieldGroupId ?? '',
          emptyGroupAutoDeletedConfirm: () => confirmAutoDeleteEmptyGroup(),
        });
        if (result) {
          Message.success($i18n.t('group_removed'));
        }
      } else if (!isDragFromGroup && isDropInGroup) {
        // 移入编组
        const result = await fieldGroupController.moveFieldsToGroup({
          sheetId,
          fieldIds: selectedColumnIds,
          originGroupId: fieldGroupId,
          targetGroupId: columnStore.dragColumnTargetGroupId ?? '',
          emptyGroupAutoDeletedConfirm: () => Promise.resolve(true),
        });
        if (result) {
          Message.success($i18n.t('notable_notableComponents_added_to_fieldGroup'));
        }
      } else if (isDragFromGroup && isDropInGroup) {
        if (fieldGroupId !== columnStore.dragColumnTargetGroupId) {
          // 先移出，再移入
          const result = await fieldGroupController.moveFieldsToGroup({
            sheetId,
            fieldIds: selectedColumnIds,
            originGroupId: fieldGroupId,
            targetGroupId: columnStore.dragColumnTargetGroupId ?? '',
            emptyGroupAutoDeletedConfirm: () => confirmAutoDeleteEmptyGroup(),
          });
          if (result) {
            Message.success($i18n.t('notable_notableComponents_added_to_fieldGroup'));
          }
        }
      }
    }
  });

  const handleColumnsMove = useMemoizedFn((dragTarget: number) => {
    const { columns } = columnStore;
    const selectedColumns = Array.from(gridStore.selectedColumnsSet.values())
      .map((id) => {
        return columns.find((col) => col.key === id);
      })
      .filter((col): col is Column => !!col);

    if (selectedColumns.some((c) => c.isPrimary)) {
    // if (selectedColumns.some((c) => c.isPrimary && c.type !== 'primaryDoc')) {
      return;
    }

    let target = 0;
    if (dragTarget < columns.length) {
      target = columns[dragTarget]?.originIndex;
      if (typeof target !== 'number') {
        return;
      }
    } else {
      // 拖到最后，取最后一列的位置+1
      const lastIndex = last(columns)?.originIndex;
      if (typeof lastIndex !== 'number') {
        return;
      }
      target = lastIndex + 1;
    }

    const selectedColumnIndexes = selectedColumns.map((col) => col.originIndex);
    const startIndex = Math.min(...selectedColumnIndexes);
    const endIndex = Math.max(...selectedColumnIndexes);

    if (target >= startIndex && target <= endIndex + 1) return;

    const ops: Operation[] = [];

    // 往前移动，处理 targetIndex 递增
    if (target < startIndex) {
      let targetIndex = target;
      selectedColumns
        .sort((a, b) => a.originIndex - b.originIndex)
        .forEach((column) => {
          ops.push(
            ...gridController.api.moveColumn({
              shouldApply: false,
              fieldId: column.key,
              index: column.originIndex,
              target: targetIndex,
            }),
          );
          targetIndex += 1;
        });
    } else {
      // 往后移动
      let targetIndex = target;
      selectedColumns
        .sort((a, b) => b.originIndex - a.originIndex)
        .forEach((column) => {
          ops.push(
            ...gridController.api.moveColumn({
              shouldApply: false,
              fieldId: column.key,
              index: column.originIndex,
              target: targetIndex,
            }),
          );
          targetIndex -= 1;
        });
    }
    gridController.applyOp(ops);
  });

  const [{ isDragging }, drag, preview] = useDrag(
    {
      canDrag: !readonly && (canFieldReorder || locked), // 锁定状态下可以拖拽但不能应用更改，所以需要放开拖拽入口。
      type: dragType.COLUMN_DRAG,
      item: () => {
        action.current = 'move';
        if (isGroup) {
          if (!gridStore.selectedGroupsSet.has(fieldOrGroupId)) {
            gridStore.clearColumnSelection();
            gridStore.clearGroupSelection();
            selectHeader();
          }
        } else if (!gridStore.selectedColumnsSet.has(fieldId)) {
          gridStore.clearColumnSelection();
          gridStore.clearGroupSelection();
          selectHeader();
        }

        return {
          fieldId,
          fieldGroupId: isGroup ? fieldOrGroupId : '',
          type: dragType.COLUMN_DRAG,
          index,
          width,
          lockY: true,
          getAction: () => action.current,
          isDragGroupItem: () => gridStore.selectedGroupsSet.size > 0,
          isGroup,
        };
      },
      collect: (monitor) => ({ isDragging: !!monitor.isDragging() }),
      end() {
        if (action.current !== 'move') {
          uiStore.markIsSelectingColumn(false);
          return;
        }

        uiStore.markIsDraggingColumn(false);
        const dragTarget = columnStore.dragColumnTarget;
        if (typeof dragTarget !== 'number') {
          return;
        }

        if (locked) {
          return handleLockViewChange();
        }

        const isDropInGroup = Boolean(columnStore.dragColumnTargetGroupId);

        if (gridStore.selectedGroupsSet.size && isDropInGroup) {
          // 不能形成组嵌套
          return;
        }
        try {
          core.batchStart('applyOp');
          handleDragDropFieldGroup(isDropInGroup);
          handleColumnsMove(dragTarget);
        } finally {
          core.batchEnd();
        }
      },
    },
    [fieldOrGroupId, fieldId, isGroup, index, width, canFieldReorder, locked, handleLockViewChange],
  );

  useEffect(() => {
    preview(getEmptyImage(), { captureDraggingState: true });
  }, [preview]);

  useEffect(() => {
    if (isDragging) {
      if (action.current === 'move' && !notPrimaryDocWithPrimary) {
        uiStore.markIsDraggingColumn(true);
      } else if (action.current === 'select') {
        uiStore.markIsSelectingColumn(true);
      }
    }
  }, [fieldOrGroupId, gridStore, isDragging, uiStore, notPrimaryDocWithPrimary]);

  return [drag, isDragging, handleClick];
};
