import React, { CSSProperties, useEffect, useRef, useMemo } from 'react';
import { useDrag<PERSON>ayer, XYCoord } from 'react-dnd';
import { createPortal } from 'react-dom';
import styled from 'styled-components';
import { clamp, inRange } from 'lodash-es';
import { observer } from 'mobx-react-lite';
import { Popover, Tooltip } from '@ali/we-design-next';
import { useNotableCore } from '@ali/notable-core';
import $i18n from '@ali/notable-i18n';
import { useGridStore } from '../../hooks/useStore';
import CONSTANTS from '../../constants/constants';
import type ColumnStore from '../../stores/ColumnStore';
import { useGridController } from '../../hooks/store-provider';
import { useResizeColumnMarkStatus } from '../../hooks/useResizeColumnIndicatorStatus';
import { ABSORB_OFFSET, RESIZE_DRAG_WIDTH } from '../Header/ResizeDrag';
import HEIGHT from '../../constants/height';
import { useAutoScroll } from './useAutoScroll';
import { IItem } from './type';

const DEFAULT_COLUMN_MIN_WIDTH = 80; // 默认最小列宽

const layerStyles: CSSProperties = {
  position: 'fixed',
  pointerEvents: 'none',
  zIndex: 100,
  left: 0,
  top: 0,
  width: '100%',
  height: '100%',
};
const DragColumnIndicator = styled.div`
  position: absolute;
  width: 2px;
  background-color: rgb(0, 137, 255);
  margin-left: -0.5px;
`;

const FieldGroupDropZoneIndicator = styled.div`
  position: absolute;
  border: 1px dashed rgb(0, 137, 255);
`;

const ResizeColumnIndicator = styled.div`
  position: absolute;
  width: 2px;
  margin-left: -0.5px;
  background-color: rgb(0, 137, 255);
`;

function getItemStyles({
  initialOffset,
  currentOffset,
  item,
  domRect,
  maxResizeWidth,
}: {
  initialOffset: XYCoord | null;
  currentOffset: XYCoord | null;
  item: IItem;
  domRect?: DOMRect;
  maxResizeWidth: number;
}): CSSProperties {
  if (!initialOffset || !currentOffset || !domRect || !item) {
    return {
      display: 'none',
    };
  }
  let { x, y } = currentOffset;

  if (item.lockY) {
    y = initialOffset.y;
  } else if (item.lockX) {
    x = initialOffset.x;
  }
  x = clamp(x, 0, domRect.left + maxResizeWidth);
  const transform = `translate(
    ${clamp(x, domRect.left, domRect.right)}px,
    ${clamp(y, domRect.top, domRect.bottom)}px)`;
  return {
    transform,
    WebkitTransform: transform,
    position: 'absolute',
  };
}

const ColumnPreview = styled.div`
  background-color: rgba(126, 134, 142, 0.12);
  z-index: 100;
  position: absolute;
`;

interface DrayLayerProps {
  item: IItem;
  itemType: string | symbol | null;
  initialOffset: XYCoord | null;
  initialClientOffset: XYCoord | null;
  currentOffset: XYCoord | null;
  pointerOffset: XYCoord | null;
}

interface GetXYCoordinatesArgs {
  columnStore: ColumnStore;
  drayLayerProps: DrayLayerProps;
  scrollLeftAbs: number;
  action: string;
  containerRect?: DOMRect;
}

function getXYCoordinates(args: GetXYCoordinatesArgs) {
  const { columnStore, drayLayerProps, containerRect, scrollLeftAbs, action } = args;
  const { initialOffset, currentOffset, itemType, pointerOffset, item } = drayLayerProps;
  const {
    primaryColumnWidth,
    frozenColumnsTotalWidth,
    rightPaneViewportColumns,
    frozenColumns,
    fullColumns,
    columnWidthOffset,
  } = columnStore;
  // 根据列宽数据，计算出当前覆盖的
  let targetIndex: number | null = null;
  let indicatorLeft: number | null = null;
  let dndX: number | null = null;
  // 目标编组
  let targetGroupId: string | null = null;
  // 调整冻结列标识线

  const { dragType } = columnStore.rootStore.controller;
  const dragGroupItem = item?.isDragGroupItem?.() && action !== 'select';
  const dragResizeFrozenItem = itemType === dragType.RESIZE_FROZEN_DRAG;

  const showColumnResizer =
    (dragResizeFrozenItem || itemType === dragType.COLUMN_DRAG || dragGroupItem) &&
    action !== 'select';

  // 拖拽column和frozenColCount不同，column用idx，frozenColCount用originIndex
  const indexType = itemType === dragType.RESIZE_FROZEN_DRAG ? 'originIndex' : 'idx';
  if (showColumnResizer && pointerOffset !== null && containerRect) {
    const currentOffsetInView = pointerOffset.x + scrollLeftAbs - containerRect.left;
    // 最左边Primary列、最右边安全间距400px，最好能够上层传递下来
    const leftBoundary = primaryColumnWidth;
    const notUpgradePrimaryDocField = true; // frozenColumns[0]?.type !== 'primaryDoc' && frozenColumns[0]?.isPrimary;
    const rightBoundary =
      itemType === dragType.RESIZE_FROZEN_DRAG
        ? containerRect.width - CONSTANTS.frozenDividerRightSafeOffset
        : Infinity;
    let maxIndex = 0;
    let maxDistance = 0;
    if (notUpgradePrimaryDocField && currentOffsetInView < leftBoundary) {
      // const currentGroupId = fullColumns[0].fieldGroupId;
      // if (currentGroupId) {
      //   const firstIndex = fullColumns.findIndex((v) => v.fieldGroupId === currentGroupId);
      //   // 非组内首个列，不显示辅助线
      //   if (firstIndex !== i) {
      //     break;
      //   }
      //   // 组内首个列，判断是否在前半部分
      //   if (currentOffsetInView >= leftDistance - (fullColumns[i].width / 2)) {
      //     break;
      //   }
      // }
      // 不允许拖动超过isPrimary列
      targetIndex = fullColumns[0][indexType] || 1;
      indicatorLeft = primaryColumnWidth + containerRect.left;
      targetGroupId = fullColumns[0].fieldGroupId;
    } else {
      let leftDistance = CONSTANTS.operationCellWidth + columnWidthOffset;
      for (let i = 0; i < fullColumns.length; i++) {
        leftDistance += fullColumns[i].width;

        if (
          inRange(currentOffsetInView, leftDistance - fullColumns[i].width, leftDistance) &&
          leftDistance < rightBoundary
        ) {
          if (dragGroupItem || dragResizeFrozenItem) {
            const currentGroupId = fullColumns[i].fieldGroupId;
            if (currentGroupId) {
              const firstIndex = fullColumns.findIndex((v) => v.fieldGroupId === currentGroupId);
              // 非组内首个列，不显示辅助线
              if (firstIndex !== i) {
                break;
              }
              // 组内首个列，判断是否在前半部分
              if (currentOffsetInView >= leftDistance - fullColumns[i].width / 2) {
                break;
              }
            }
            const prevGroupId = fullColumns[i - 1]?.fieldGroupId;
            if (currentGroupId && currentGroupId === prevGroupId) {
              break;
            }
          }
          targetIndex = fullColumns[i][indexType];
          indicatorLeft = leftDistance + containerRect.left - fullColumns[i].width - scrollLeftAbs;
          targetGroupId = fullColumns[i].fieldGroupId;
          if (currentOffsetInView >= leftDistance - fullColumns[i].width / 2) {
            targetIndex++;
            indicatorLeft += fullColumns[i].width;
            targetGroupId = fullColumns[i]?.fieldGroupId;
          }
          break;
        }
        if (leftDistance > rightBoundary) {
          maxIndex = fullColumns[i][indexType];
          maxDistance = leftDistance - fullColumns[i].width + containerRect.left;
          break;
        }
      }
    }
    if (!indicatorLeft && maxIndex) {
      targetIndex = maxIndex;
      indicatorLeft = maxDistance;
    }
  }
  // 多选column
  let selectEndIndex: number | null = null;
  if (
    (itemType === dragType.COLUMN_DRAG || dragGroupItem) &&
    pointerOffset !== null &&
    action === 'select' &&
    containerRect
  ) {
    frozenColumns.some((v) => {
      const leftDistance = pointerOffset.x - containerRect.left;
      if (v.left < leftDistance && v.width + v.left > leftDistance) {
        selectEndIndex = v.idx;
        return true;
      }
      return false;
    });
    if (selectEndIndex === null) {
      rightPaneViewportColumns.some((v) => {
        const left = v.left - scrollLeftAbs + frozenColumnsTotalWidth;
        const leftDistance = pointerOffset.x - containerRect.left;
        if (left < pointerOffset.x && v.width + left > leftDistance) {
          selectEndIndex = v.idx;
          return true;
        }
        return false;
      });
    }
  }

  if (currentOffset !== null && initialOffset !== null) {
    dndX = currentOffset.x - initialOffset.x;
  }

  if (dragGroupItem) {
    targetGroupId = null;
  }

  return {
    action,
    targetIndex,
    indicatorLeft,
    selectEndIndex,
    dndX,
    targetGroupId: targetGroupId ?? null,
  };
}

// eslint-disable-next-line max-lines-per-function
const CustomDragLayer = ({ containerRef }: { containerRef: React.RefObject<HTMLElement> }) => {
  const { columnStore, rowStore, uiStore, scrollStore, configStore } = useGridStore();
  const { dragConfig: config, dragType } = useGridController();
  const { frozenColumnsTotalWidth, resizeColumnX } = columnStore;
  const { leftColumn } = useResizeColumnMarkStatus();
  const { scrollLeftAbs, scrollTop, gridRect } = scrollStore;
  const {
    globalDataController: {
      settings: { enableColumnResizeContextMenu },
    },
    frameworkController: { currentView },
  } = useNotableCore();
  const isGridView = currentView?.type === 'Grid';
  // 标识线在视图内部显示
  const innerViewportHeight = Math.min(
    gridRect.height,
    rowStore.headerAndContentHeight - scrollTop,
  );
  const resizingColumn = uiStore.resizingColumnId
    ? columnStore.getColumnByKey(uiStore.resizingColumnId)
    : null;

  const drayLayerProps = useDragLayer<DrayLayerProps>((monitor) => ({
    item: monitor.getItem(),
    itemType: monitor.getItemType(),
    initialOffset: monitor.getInitialSourceClientOffset(),
    initialClientOffset: monitor.getInitialClientOffset(),
    currentOffset: monitor.getSourceClientOffset(),
    pointerOffset: monitor.getClientOffset(),
  }));

  const { currentOffset, initialOffset, initialClientOffset, item, itemType } = drayLayerProps;
  const action = typeof item?.getAction === 'function' ? item.getAction() : 'move';

  useAutoScroll({
    isDragging: uiStore.isDragging || action === 'select',
    currentOffset,
    config,
    item,
    paddingLeft: frozenColumnsTotalWidth,
  });

  const containerRect: DOMRect | undefined = useMemo(() => {
    return containerRef?.current?.getBoundingClientRect();
    // 如果开始拖动，则重新拿 grid 的最新 rect 信息
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uiStore.isDragging, containerRef.current, action]);

  const { targetIndex, indicatorLeft, dndX, selectEndIndex, targetGroupId } = getXYCoordinates({
    containerRect,
    drayLayerProps,
    action,
    scrollLeftAbs,
    columnStore,
  });

  const initialScrollLeftRef = useRef<number>(0);

  const tempResizeColumnX = useMemo(() => {
    const scrollLeftOffset = scrollLeftAbs - initialScrollLeftRef.current;
    return dndX ? dndX + scrollLeftOffset : dndX;
  }, [dndX, scrollLeftAbs]);

  const resizingIndicatorTip = useMemo(() => {
    if (resizingColumn) {
      const currentWidth = Math.floor(resizingColumn.width + (resizeColumnX ?? 0));
      const minWidth = resizingColumn.isPrimary
        ? columnStore.minPrimaryColumnWidth
        : DEFAULT_COLUMN_MIN_WIDTH;
      const widthInfo = `${
        currentWidth <= (resizingColumn.minWidth || minWidth)
          ? resizingColumn.minWidth || minWidth
          : currentWidth
      }px`;
      return leftColumn?.width === currentWidth
        ? $i18n.t('we_notable_view_same_as_left_column_width', { widthInfo })
        : widthInfo;
    }
  }, [columnStore.minPrimaryColumnWidth, leftColumn?.width, resizeColumnX, resizingColumn]);

  const resizeColumnIndicatorLeft = useMemo(() => {
    if (resizingColumn) {
      const minWidth = resizingColumn.isPrimary
        ? columnStore.minPrimaryColumnWidth
        : DEFAULT_COLUMN_MIN_WIDTH;
      // 小于当前列最小宽度后不再缩小
      const leftInMinWidth =
        RESIZE_DRAG_WIDTH / 2 - (tempResizeColumnX ?? 0) - resizingColumn.width + minWidth;
      if (minWidth > resizingColumn.width + (tempResizeColumnX ?? 0)) {
        if ((currentOffset?.x ?? 0) < (containerRect?.left ?? 0)) {
          return leftInMinWidth + (currentOffset?.x ?? 0) - (containerRect?.left ?? 0);
        }
        return leftInMinWidth;
      }
      // 实现吸附效果
      if (
        leftColumn &&
        Math.abs(leftColumn?.width - (resizingColumn.width + (tempResizeColumnX ?? 0))) <=
          ABSORB_OFFSET
      ) {
        return (
          RESIZE_DRAG_WIDTH / 2 +
          leftColumn?.width -
          (resizingColumn.width + (tempResizeColumnX ?? 0))
        );
      }
    }
    return RESIZE_DRAG_WIDTH / 2;
  }, [
    resizingColumn,
    columnStore.minPrimaryColumnWidth,
    tempResizeColumnX,
    currentOffset?.x,
    leftColumn,
    containerRect?.left,
  ]);

  const targetFieldGroupInfo = useMemo(
    () => columnStore.groupMapWithRange[targetGroupId ?? ''] || {},
    [columnStore.groupMapWithRange, targetGroupId],
  );

  const dropZoneIndicatorStyle = useMemo<React.CSSProperties>(() => {
    const style: React.CSSProperties = {};
    if (!targetGroupId || !containerRect) {
      return style;
    }

    const groupWidth = targetFieldGroupInfo.width;
    const isPrimary = targetFieldGroupInfo.children.some((child) => child.isPrimary);

    const frozenColumnsWidth = isPrimary ? 0 : frozenColumnsTotalWidth;

    const left =
      containerRect.left + targetFieldGroupInfo.left - scrollLeftAbs + frozenColumnsWidth;

    const rightHeaderLeft = containerRect.left + frozenColumnsWidth;

    const notBorderLeft = rightHeaderLeft > left;

    style.top = containerRect.y;
    style.left = Math.max(left, containerRect.left + frozenColumnsWidth);
    style.width = Math.max(0, Math.min(groupWidth, groupWidth + left - rightHeaderLeft));
    style.height = innerViewportHeight;

    if (notBorderLeft) {
      style.borderLeft = 'none';
    }

    return style;
  }, [
    targetGroupId,
    containerRect,
    innerViewportHeight,
    frozenColumnsTotalWidth,
    scrollLeftAbs,
    targetFieldGroupInfo,
  ]);

  useEffect(() => {
    columnStore.setDragColumnTarget(targetIndex, targetGroupId);
  }, [columnStore, targetIndex, targetGroupId]);

  useEffect(() => {
    if (!enableColumnResizeContextMenu || !isGridView) {
      const scrollLeftOffset = scrollLeftAbs - initialScrollLeftRef.current;
      columnStore.setResizeColumnX(dndX ? dndX + scrollLeftOffset : dndX);
    }
    if (resizingColumn) {
      const minWidth = resizingColumn.isPrimary
        ? columnStore.minPrimaryColumnWidth
        : DEFAULT_COLUMN_MIN_WIDTH;
      if (
        leftColumn &&
        Math.abs(leftColumn?.width - (resizingColumn.width + (tempResizeColumnX ?? 0))) <=
          ABSORB_OFFSET
      ) {
        columnStore.setResizeColumnX(
          Math.max(leftColumn?.width - resizingColumn.width, minWidth - resizingColumn.width),
        );
        return;
      }
      return columnStore.setResizeColumnX(
        Math.max(tempResizeColumnX ?? 0, minWidth - resizingColumn.width),
      );
    }
    columnStore.setResizeColumnX(tempResizeColumnX);
  }, [
    columnStore,
    dndX,
    enableColumnResizeContextMenu,
    isGridView,
    leftColumn,
    resizingColumn,
    scrollLeftAbs,
    tempResizeColumnX,
  ]);

  useEffect(() => {
    if (uiStore.isDragging) {
      initialScrollLeftRef.current = scrollLeftAbs;
    }
  }, [uiStore.isDragging, scrollLeftAbs]);

  useEffect(() => {
    if (
      action === 'select' &&
      typeof selectEndIndex === 'number' &&
      typeof item?.index === 'number'
    ) {
      columnStore.selectColumnRange(item.index, selectEndIndex);
    }
  }, [action, selectEndIndex, item?.index, columnStore]);

  if (!uiStore.isDragging || !containerRect || !initialClientOffset || !initialOffset) {
    return null;
  }

  const isDragGroupItem = item?.isDragGroupItem?.();

  const hideColumnIndicatorWhenNestedGroupDrop = isDragGroupItem && Boolean(targetGroupId);

  return createPortal(
    <div style={{ ...layerStyles, ...configStore.dragLayerStyles }}>
      <div
        style={getItemStyles({
          initialOffset,
          currentOffset,
          item,
          domRect: containerRect,
          maxResizeWidth: resizingColumn?.isPrimary
            ? CONSTANTS.MAX_PRIMARY_COLUMN_WIDTH + CONSTANTS.RESIZE_LINE_OFFSET
            : Infinity,
        })}
      >
        {item?.renderPreview?.()}
        {itemType === dragType.COLUMN_DRAG || isDragGroupItem ? (
          <ColumnPreview style={{ width: item.width, height: innerViewportHeight }} />
        ) : null}
        {itemType === dragType.RESIZE_COLUMN_DRAG && enableColumnResizeContextMenu && isGridView && (
          <Popover.Container
            style={{
              position: 'absolute',
              transform: `translate(${resizeColumnIndicatorLeft}px, 0)`,
            }}
          >
            <Tooltip title={resizingIndicatorTip} keepAlign visible pointer={false} placement="top">
              <ResizeColumnIndicator
                style={{
                  height: innerViewportHeight,
                }}
              />
            </Tooltip>
          </Popover.Container>
        )}
        {itemType === dragType.RESIZE_COLUMN_DRAG &&
          !(enableColumnResizeContextMenu && isGridView) && (
            <ResizeColumnIndicator
              style={{
                height: innerViewportHeight,
                left: initialClientOffset?.x - initialOffset?.x,
              }}
            />
        )}
      </div>
      {indicatorLeft && !hideColumnIndicatorWhenNestedGroupDrop && (
        <DragColumnIndicator
          style={{
            left: indicatorLeft + CONSTANTS.frozenColumnOffset,
            top: targetGroupId ? containerRect.y + HEIGHT.header : containerRect.y,
            height: targetGroupId ? innerViewportHeight - HEIGHT.header : innerViewportHeight,
          }}
        />
      )}
      {targetFieldGroupInfo && <FieldGroupDropZoneIndicator style={dropZoneIndicatorStyle} />}
    </div>,
    document.body,
  );
};

export default observer(CustomDragLayer);
