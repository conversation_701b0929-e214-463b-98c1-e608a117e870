import { observer } from 'mobx-react-lite';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import classnames from 'classnames';
import { Warning16 } from '@ali/we-icons';
import { Tooltip } from '@ali/we-design-next';
import { themeCss } from '@ali/notable-design';
import { useCellHighlightStyle } from '@ali/notable-components';
import $i18n from '@ali/notable-i18n';
import { FieldDTO, isFaasFieldDecorator, mixColors } from '@ali/notable-common';
import { useDesignTokenContext } from '@ali/we-design-token';
import { StyledAvatar } from '../StyledAvatar';
import {
  useConfigStore,
  useGridStore,
  useScrollStore,
  useColumnStore,
} from '../../hooks/useStore';
import { CalculatedColumn, Row } from '../../types';
import { LocalCellStore } from '../../stores/LocalCellStore';
import CONSTANTS from '../../constants/constants';
import { COLOR } from '../../constants/color';
import { constants } from '../../constants';
import { CellWrapper, CellWrapperInner, ContentWrapper } from './CellWrapper';
import { CellSkeleton } from './CellSkeleton';
import { HierarchySelect, HierarchyCellSuffix } from './Hierarchy';
import { CellStatus } from './CellStatus';
import { OpenRecordButton } from './OpenRecordButton';

export interface CellRendererProps {
  row: Row;
  column: CalculatedColumn;
  rowIndex: number;
  extra?: React.ReactElement;
}

function Cell(props: CellRendererProps) {
  const { row, rowIndex, column, extra } = props;
  const gridStore = useGridStore();
  const configStore = useConfigStore();
  const scrollStore = useScrollStore();
  const columnStore = useColumnStore();
  const { adaptViewportFrozenColCount, existPrimaryDocField } = columnStore;
  const { colorScheme } = useDesignTokenContext();
  const [localCellStore] = useState(
    () =>
      new LocalCellStore({
        rootStore: gridStore,
        cellInfo: {
          row,
          column,
          rowIndex,
        },
      }),
  );

  useEffect(() => {
    localCellStore.updateCellInfo({
      row, rowIndex, column,
    });
  }, [localCellStore, row, rowIndex, column]);

  const leftStyle = column.sortedIndex < adaptViewportFrozenColCount ? column.left + constants.frozenColumnOffset : column.left;

  // 📢📢📢 后续都的 row 取的是 local 中的那么 column 也得从 local 中获取
  const { column: localColumn } = localCellStore;
  /**
   * ATTENTION:
   * column.formatter  key 为 isCellSelected，是为了防止 cell 响应 onClick 事件
   * 测试用例：
   *   单选，需要 cell 在 active 的时候，点击背景，打开编辑面板。如果当前不是 active，需要先选中，再次点击，打开
   *   因为目前使用的是 onMouseDown 选中 cell，在 SingleSelectCell 中，响应 click 事件的时候，
   *   拿到的 active 总是 true，于是点击 cell 会立即打开编辑面板，
   *   这里通过 key，在 active 切换的时候，直接将 dom 重新创建。
   */
  const formatterKey = localColumn.gridFormatterConfig?.preventCellInteractiveWhenNotActive
    ? String(localCellStore.isCellSelected)
    : undefined;
  const overContentNotClip = !!localColumn.gridFormatterConfig?.overContentNotClip;

  const { allowUpdate } = localCellStore;
  const ref = useRef<HTMLDivElement>(null);
  const { isCellHighlighted, isCellSelected, isInFillArea, isSearchHit } = localCellStore;
  const style = useMemo(
    () => ({ left: leftStyle, width: localColumn.width }),
    [leftStyle, localColumn.width],
  );
  const isInSelectedArea = localCellStore.isInSelectedArea &&
    (gridStore.uiStore.hasSelection || gridStore.uiStore.isSelectingColumn || !gridStore.isEntireRowSelected(row.id));
  const highlightStyle = useCellHighlightStyle(row.id, localColumn.fieldId, style);
  const cellBgColor = gridStore.fillColorStore.getBg(gridStore.sheetId, row.id, column.key, colorScheme);
  // 要和组件内被选中或命中搜索的背景色进行混合，按照className生效逻辑顺序返回
  const getColor = useCallback(() => {
    if (isCellHighlighted || isSearchHit) {
      return themeCss.color.yellow_4;
    } else if (isInSelectedArea || (!isCellSelected && isInFillArea)) {
      return COLOR.selectedRowBgColor;
    }
    return '';
  }, [isCellHighlighted, isCellSelected, isInFillArea, isInSelectedArea, isSearchHit]);
  const wrapperStyle = useMemo(() => {
    const bgColor = mixColors([cellBgColor ?? '', getColor()]);
    return { ...highlightStyle, background: highlightStyle?.background || bgColor || undefined };
  }, [cellBgColor, getColor, highlightStyle]);
  const { dataType, value } = localCellStore.cell ?? { dataType: undefined, value: null };

  // 是否显示 hierarchy
  const isPrimaryDoc = localColumn.isPrimary && existPrimaryDocField;
  const showHierarchy = existPrimaryDocField ? localColumn.idx === 0 : localColumn.isPrimary;
  // 有父子记录时，所有记录都需要加缩进，子记录再额外缩进
  const hierarchyWidth = gridStore.rowStore.getHierarchyWidth(row.id, showHierarchy);
  const cellWidth = localColumn.width - hierarchyWidth;
  const childrenCount = row.hierarchyInfo?.childrenList?.length;
  const { contextMenuStore } = gridStore;
  const { visible, position } = contextMenuStore;
  const contextMenuVisible = !!(visible && position);
  return (
    <CellWrapper
      ref={ref}
      className={classnames('DataCellWrapper', {
        highlighted: localCellStore.isCellHighlighted,
        'cell-active': localCellStore.isCellSelected, // cell-active 这个 className 被各个 cell 消费，不要修改名称
        'in-fill-area': localCellStore.isInFillArea,
        'in-selected-area': isInSelectedArea,
        'search-hint': localCellStore.isSearchHit,
      })}
      setCellHeightAutoWhenSelected={localColumn.editorOptions?.setCellHeightAutoWhenSelected}
      selected={localCellStore.isCellSelected}
      role="gridcell"
      data-role={`GridCell_${localColumn.type}`}
      data-field-id={localColumn.fieldId}
      data-row-id={row.id}
      data-in-selected-area={localCellStore.isInSelectedArea}
      aria-colindex={localColumn.idx + 1} // aria-colindex is 1-based
      aria-selected={localCellStore.isCellSelected}
      cellHeight={gridStore.cellHeight}
      borderColor={configStore.cellBorderColor}
      boxShadowColor={localCellStore.user?.color || themeCss.color.brand1}
      style={wrapperStyle}
      onMouseDown={localCellStore.handleMouseDown}
      onDoubleClick={localCellStore.handleDoubleClick}
      onContextMenu={localCellStore.handleContextMenu}
      overContentNotClip={overContentNotClip}
    >
      {showHierarchy && <HierarchySelect
        row={row}
        width={hierarchyWidth}
        left={leftStyle}
        childrenCount={childrenCount}
      />}
      <ContentWrapper
        borderColor={configStore.cellBorderColor}
      >
        <CellWrapperInner
          style={{ width: cellWidth }}
          className="cellWrapperInner"
        >
          {
            scrollStore.isFPSExtremelyLow && !showHierarchy ? (
              <CellSkeleton />
            ) : (
              <localColumn.formatter
                key={formatterKey}
                dataType={dataType}
                value={value}
                config={localColumn.config}
                onValueChange={localCellStore.handleRenderCellValueChange}
                readonly={configStore.readonly || !allowUpdate}
                openEditor={gridStore.openEditor}
                openExpandedEditor={gridStore.openExpandedEditor}
                active={localCellStore.isCellSelected}
                sheetId={gridStore.sheetId}
                viewId={gridStore.viewId}
                rowId={row.id}
                fieldId={localColumn.fieldId}
                cellWidth={cellWidth - CONSTANTS.cellPaddingOffset}
                cellHeight={gridStore.cellHeight}
                cellPaddingTop={CONSTANTS.cellPaddingTop}
                edited={localCellStore.isCellEdited || contextMenuVisible}
              />
            )
          }
        </CellWrapperInner>
        {isPrimaryDoc && <OpenRecordButton cellWidth={localColumn.width} isCellSelected={localCellStore.isCellSelected} row={row} rowIndex={rowIndex} />}
        {showHierarchy && !localCellStore.isCellSelected && <HierarchyCellSuffix existPrimaryDocField={existPrimaryDocField} row={row} rowIndex={rowIndex} />}
        {
          showHierarchy && row.hierarchyInfo?.errMsgKey && (
            <Tooltip title={$i18n.t(row.hierarchyInfo.errMsgKey)}>
              <div className="suffix" style={{ marginRight: 8 }}><Warning16 /></div>
            </Tooltip>
          )
        }
        {
          !localCellStore.isCellSelected && extra && <div className="suffix">{extra}</div>
        }
        {
          !localCellStore.isCellSelected && localCellStore.user && (
            <StyledAvatar name={localCellStore.user.name} src={localCellStore.user.avatar} shape="circle" size={28} />
          )
        }
        {isFaasFieldDecorator(column as Partial<FieldDTO>) && (
          <CellStatus sheetId={gridStore.sheetId} recordId={row.id} fieldId={localColumn.fieldId} />
        )}
      </ContentWrapper>
    </CellWrapper >
  );
}

export default observer(Cell);
