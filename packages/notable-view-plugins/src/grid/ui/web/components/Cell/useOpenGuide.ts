import { useCallback, useEffect, useState } from 'react';
import { safeSetStorage, safeGetStorage, primaryDocOpenGuideLocalKey } from '@ali/notable-common';

export const useOpenGuide = (show, contentRef) => {
  const [visible, setVisible] = useState(false);

  const showOpenGuide = useCallback(() => {
    if (!contentRef.current) {
      return;
    }
    const needShow = safeGetStorage(primaryDocOpenGuideLocalKey);
    if (needShow === '1') {
      setVisible(true);
      safeSetStorage(primaryDocOpenGuideLocalKey, '0');
    }
  }, [contentRef]);

  useEffect(() => {
    if (show) {
      showOpenGuide();
    }
    return () => {
      setVisible(false);
    };
  }, [show, showOpenGuide]);

  const hideGuide = useCallback(() => {
    setVisible(false);
  }, []);

  return { visible, hideGuide };
};
