import React from 'react';
import { observer } from 'mobx-react-lite';
import styled from 'styled-components';
import { Category1616, AddM16 } from '@ali/we-icons';
import { Tooltip } from '@ali/we-design-next';
import { themeCss } from '@ali/notable-design';
import $i18n from '@ali/notable-i18n';
import { useNotableCore } from '@ali/notable-core';
import { getRecordNameConfig } from '@ali/notable-components';
import { ListExpand16, ListFold16 } from '@ali/we-icons-3';
import { useColumnStore, useGridStore } from '../../hooks/useStore';
import { Row } from '../../types';
import { LocalRowWithInfo } from '../../utils';
import { constants } from '../../constants';
import useRowRightClick from '../SingleRow/hooks/useRowRightClick';

interface HierarchySelectProps {
  row: Row;
  width: number;
  left: number;
  childrenCount: number | undefined;
}

interface HierarchySelectWrapperProps {
  height: number;
  width: number;
  left: number;
  showBorder: boolean;
  borderColor: string;
}

const HierarchyLineMarkOffset = 8;

const HierarchySelectWrapper = styled.div<HierarchySelectWrapperProps>`
  height: ${(props) => `${props.height}px`};
  width: ${(props) => `${props.width}px`};
  min-width: ${(props) => `${props.width}px`};
  left: ${(props) => `${props.left}px`};
  align-items: center;
  display: inline-flex;
  justify-content: flex-end;
  overflow: visible;

  ${({ showBorder, borderColor }) => {
    if (showBorder) return `border-bottom: 1px solid ${borderColor};`;
    return '';
  }};

  svg {
    color: ${themeCss.color.level3};
  }

  svg:hover {
    color: ${themeCss.color.blue_1};
  }
`;

export const HierarchySelect = observer((props: HierarchySelectProps) => {
  const { width, left, childrenCount, row } = props;
  const gridStore = useGridStore();
  const { cellBorderColor } = gridStore.configStore;

  const hasChildren = !!childrenCount;

  const ArrorIcon = gridStore.rowStore.isRowFolded(row.id) ? ListFold16 : ListExpand16;
  const showBorder = !row?.hierarchyInfo || !!row.hierarchyInfo.isLastRowInLevel;

  return (
    <HierarchySelectWrapper
      height={gridStore.cellHeight}
      left={left}
      width={width}
      showBorder={showBorder}
      borderColor={cellBorderColor}
    >
      {hasChildren && (
        <Tooltip title={$i18n.t('notable_components_grid_hierarchy_count', { count: childrenCount })} placement="bottom">
          <div style={{ display: 'flex' }}>
            <ArrorIcon
              width={24}
              size={16}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                gridStore.rowStore.toggleRowFolded(row.id);
              }}
              onDoubleClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
              onMouseDown={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
            />
          </div>
        </Tooltip>
      )}
    </HierarchySelectWrapper>
  );
});

const HierarchyLineMark = styled.div<HierarchyLineProps>`
  height: ${(props) => `${props.height}px`};
  width: 0;
  border-left: 1px dashed ${themeCss.color.line_heavy};
  position: relative;
  left: ${(props) => `${props.left + 0.5}px`};
  top: ${(props) => `${props.top}px`};
  align-self: start;
  z-index: 100;
  position: absolute;
`;

interface HierarchyLineProps {
  height: number;
  top: number;
  left: number;
}

interface RenderRowProps {
  data: LocalRowWithInfo | undefined;
}

export const RenderHierarchyMark = observer((props: RenderRowProps) => {
  const { data } = props;
  const gridStore = useGridStore();
  const { noLeftBorder } = useColumnStore();

  if (!data) {
    return null;
  }
  const primaryColumn = gridStore.columnStore.frozenColumns.find((c) => c.isPrimary);
  let targetColumn = primaryColumn;
  if (primaryColumn?.type === 'primaryDoc') {
    targetColumn = gridStore.columnStore.frozenColumns[0];
  }
  if (!targetColumn) {
    return null;
  }


  const { info, row } = data;
  const left = targetColumn.left + (info.fromLeft ?? 0) + constants.frozenColumnOffset + (noLeftBorder ? -1 : 0);

  const hasChildren = !!row?.hierarchyInfo?.childrenList?.length;
  if (!hasChildren || !!gridStore.rowStore.hierarcyCollapseStatus[row.id]) {
    return null;
  }

  const hierarchyWidth = gridStore.rowStore.getHierarchyWidth(row.id, true);

  const childrenCount = gridStore.rowStore.displayedChildrenCount[row?.id ?? ''] ?? 0;

  const top = info?.fromTop || 0;

  if (info?.isFolded) {
    return null;
  }

  return (
    <HierarchyLineMark
      height={childrenCount * gridStore.cellHeight - HierarchyLineMarkOffset}
      top={top + gridStore.cellHeight / 2 + HierarchyLineMarkOffset}
      left={left + hierarchyWidth - 12}
    />
  );
});

const HierarchyCellSuffixWrapper = styled.div`
  height: 100%;
  align-items: center;
  display: inline-flex;
  justify-content: flex-end;
  overflow: visible;
  margin: 0 4px 0 0;

  .split {
    width: 0;
    border-left: 1px solid ${themeCss.color.line_light};
    height: 10px;
    margin: 0 4px;
  }

  .iconWrap {
    align-items: center;
    display: inline-flex;
    justify-content: flex-end;
    color: ${themeCss.color.level3};

    svg {
      color: ${themeCss.color.level3};
    }

    :hover {
      color: ${themeCss.color.blue_1};

      svg {
        color: ${themeCss.color.blue_1};
      }
    }
  }
`;

export const HierarchyCellSuffix = observer((props: { row: Row; rowIndex: number; existPrimaryDocField: boolean }) => {
  const { row, rowIndex, existPrimaryDocField } = props;
  const { globalDataController: { settings: { readonly, disabledLimitFreeTag } }, frameworkController } = useNotableCore();
  const gridStore = useGridStore();
  const { recordName } = getRecordNameConfig(frameworkController.currentSheet);

  const top = gridStore.rowStore.rowsWithInfo[rowIndex]?.info?.fromTop || 0;
  const { handleContextMenu } = useRowRightClick(row, top, readonly);

  if (gridStore.viewType !== 'Grid' && gridStore.viewType !== 'Gantt') {
    return null;
  }

  const canAdd =
    !readonly &&
    gridStore.rowStore.canAddSubRecord(row.id) &&
    gridStore.configStore.canRecordCreate;
  const hasChildren = !!row.hierarchyInfo?.childrenList?.length;
  const showAddButton = canAdd && (hasChildren || gridStore.isRowHovering(row.id));
  const showCollabseButton = hasChildren && !existPrimaryDocField;

  if (!showAddButton && !showCollabseButton) {
    return null;
  }

  return (
    <HierarchyCellSuffixWrapper
      onMouseDown={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
      onDoubleClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
      onContextMenu={(e) => {
        handleContextMenu(e);
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      {showAddButton && (
        <Tooltip
          title={disabledLimitFreeTag ? $i18n.t('record_add_sub_alias', { recordName }) : `${$i18n.t('record_add_sub_alias', { recordName })}(${$i18n.t(
            'badge_limited_time_free',
          )})`}
          placementOffset={4}
          placement="bottom"
        >
          <div
            className="iconWrap"
            onClick={() => {
              gridStore.insertSubRecord(row, 0);
            }}
          >
            <AddM16 size={16} />
          </div>
        </Tooltip>
      )}
      {showCollabseButton && (
        <>
          <div className="split" />
          <div
            className="iconWrap"
            onClick={() => {
              gridStore.rowStore.toggleRowFolded(row.id);
            }}
            style={{ fontFamily: 'Helvetica Neue' }}
          >
            <Category1616 size={16} />
            {row.hierarchyInfo?.childrenList?.length ?? 0}
          </div>
        </>
      )}
    </HierarchyCellSuffixWrapper>
  );
});
