import React, { useRef, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { useNotableCore } from '@ali/notable-core';
import { FullWindow16, PopView16, SpliteView16 } from '@ali/we-icons-3';
import $i18n from '@ali/notable-i18n';
import styled from 'styled-components';
import { themeVars } from '@ali/notable-design';
import { Tooltip, Popover, Button } from '@ali/we-design-3';
import classNames from 'classnames';
import { useConfigStore, useGridStore } from '../../hooks/useStore';
import { Row } from '../../types';
import { useOpenGuide } from './useOpenGuide';

const ButtonContainer = styled.div`
  width: 0;
  position: relative;
`;

const OpenButton = styled.div`
  height: 25px;
  display: flex;
  border-radius: ${themeVars.radius.radius_s};
  background-color: ${themeVars.color.fg_ultra_light};
  border: 0.5px solid ${themeVars.color.button_secondary_stroke};
  cursor: pointer;
  box-shadow: ${themeVars.shadow.shadow_button_effect};
`;

const IconWrapper = styled.div`
  width: 14px;
  height: 14px;
  margin-right: 4px;
  display: flex;
  align-items: center;
  color: ${themeVars.color.level_2};
  svg {
    width: 14px;
    height: 14px;
  }
`;

const StyledSpan = styled.span`
  font-size: 12px;
  line-height: 16px;
  color: ${themeVars.color.level_2};
`;

const ButtonWrapper = styled.div`
  position: absolute;
  right: 4px;
  height: 100%;
  display: flex;
  align-items: center;

  opacity: 0;

  &.isShow {
    opacity: 1;
  }

  &.isSelected {
    right: 3px;

    ${OpenButton} {
      border: none;
      box-shadow: none;
    }

    ${IconWrapper} {
      margin-right: 0px;
    }

    ${StyledSpan} {
      display: none;
    }
  };
  &.isMini {
    ${IconWrapper} {
      margin-right: 0px;
    }
  }
`;

const OpenButtonInner = styled.div`
  display: flex;
  align-items: center;
  padding: 4px 6px;
  &:hover {
    background: ${themeVars.color.overlay_light};
  }
`;

const Content = styled.div`
  padding: 16px;
  width: 280px;
  border-radius: ${themeVars.radius.radius_xl};
  background: ${themeVars.color.bg_default};
  box-shadow: ${themeVars.shadow.shadow_m};
  backdrop-filter: blur(30px);
`;

const Title = styled.div`
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: ${themeVars.color.level_1};
`;

const Desc = styled.div`
  font-size: 14px;
  line-height: 20px;
  color: ${themeVars.color.level_2};
  margin-top: 8px;
`;

const Footer = styled.div`
  margin-top: 12px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
`;

export const OpenRecordButton = observer((props: { row: Row; isCellSelected: boolean; rowIndex: number; cellWidth: number }) => {
  const { row, rowIndex, isCellSelected, cellWidth } = props;

  const gridStore = useGridStore();
  const notableCore = useNotableCore();
  const { disableExpandRow } = useConfigStore();

  const guideRef = useRef<HTMLDivElement>(null);
  const isRowHovering = gridStore.isRowHovering(row.id);
  const isCellInRowSelected = gridStore.isCellInRowSelected(row.id);
  const view = notableCore.viewController.getView(gridStore.sheetId, gridStore.viewId);
  const recordOpenMethod = view?.recordOpenMethod || 'side';

  const disableShow =
    disableExpandRow ||
    (gridStore.viewType !== 'Grid' && gridStore.viewType !== 'Gantt');
  const { visible: guideVisible, hideGuide } = useOpenGuide(
    isCellInRowSelected && !disableShow,
    guideRef,
  );
  const [mini, setMini] = React.useState(cellWidth < 200);

  useEffect(() => {
    setMini(cellWidth < 200);
  }, [cellWidth]);

  if (disableShow) {
    return null;
  }

  const renderIcon = () => {
    switch (recordOpenMethod) {
      case 'modal':
        return <PopView16 />;
      case 'full-page':
        return <FullWindow16 />;
      default:
        return <SpliteView16 />;
    }
  };

  const getText = () => {
    // switch (recordOpenMethod) {
    //   case 'modal':
    //     return $i18n.t('notable_notableComponents_RecordOpenModal_description');
    //   case 'full-page':
    //     return $i18n.t('notable_notableComponents_RecordOpenFullPage_description');
    //   default:
    //     return $i18n.t('notable_notableComponents_RecordOpenSide_description');
    // }
    // 先统一文案
    return $i18n.t('notable_common_expand_row');
  };

  const renderContent = () => {
    return (
      <Content>
        <Title>{$i18n.t('we_notable_primary_doc_open_guide_title')}</Title>
        <Desc>{$i18n.t('we_notable_primary_doc_open_guide_description')}</Desc>
        <Footer>
          <Button
            type="primary"
            onMouseDown={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              hideGuide();
            }}
          >
            {$i18n.t('notable_notableViewFramework_IKnow')}
          </Button>
        </Footer>
      </Content>
    );
  };

  return (
    <ButtonContainer>
      <ButtonWrapper className={classNames({
        isSelected: isCellSelected,
        isMini: mini,
        isShow: guideVisible || isRowHovering,
      })}
      >
        <Popover
          placement="bottomLeft"
          placementOffset={[5, -24]}
          visible={guideVisible}
          animation={false}
          content={renderContent()}
        >
          <OpenButton
            ref={guideRef}
            id={rowIndex === 0 ? 'notable-grid-view-expand' : ''}
            onMouseDown={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            onDoubleClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            onContextMenu={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            onClick={(e) => {
              e.stopPropagation();
              gridStore.resetSelectedPosition();
              gridStore.clearColumnSelection();
              gridStore.handleExpand(row.id, recordOpenMethod);
            }}
          >
            <Tooltip placement="top" title={getText()}>
              <OpenButtonInner>
                <IconWrapper>{renderIcon()}</IconWrapper>
                {!mini ? <StyledSpan>{$i18n.t('we_notable_import_view_sheet')}</StyledSpan> : null}
              </OpenButtonInner>
            </Tooltip>
          </OpenButton>
        </Popover>
      </ButtonWrapper>
    </ButtonContainer>
  );
});
