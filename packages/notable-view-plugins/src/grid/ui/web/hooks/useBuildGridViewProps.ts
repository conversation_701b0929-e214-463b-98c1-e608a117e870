/* eslint-disable function-paren-newline */
import { FieldId, FieldVM, LocalView } from '@ali/notable-model';
import { useEffect, useMemo, useRef } from 'react';
import { fromPairs, get } from 'lodash-es';
import { GenericFieldPlugin } from '@ali/notable-common';
import { useNotableCore } from '@ali/notable-core';
import { modelFieldToViewColumn } from '@ali/notable-field-plugins';
import { sortSameFieldGroupsTogether } from '@ali/notable-components';
import { useCurrentViewQueryModel } from '@ali/notable-react';
import { useGetContextLocale } from '@ali/notable-i18n';
import { GridViewApi } from '../types';

const mappingAggregateFun = (
  id: FieldId,
  aggregateType: Record<string, string>,
  aggregateValue: Record<string, string | number | null>,
) => ({
  type: aggregateType[id],
  value: aggregateValue[id],
});

// 第一个数字是展示 100% 并能看到字段图标的长度
const RUNING_TASK_MIN_FIELD_WIDTH_MAP = {
  'zh-CN': 190,
  'zh-HK': 190,
  'zh-TW': 190,
  'ja-JP': 193,
  'en-US': 256,
  'ko-KR': 195,
  'tr-TR': 339,
  'pt-BR': 344,
  'th-TH': 218,
  'id-ID': 314,
  'ms-MY': 399,
};

function useBuildGridViewProps(
  model: LocalView<'Grid' | 'Gantt'>,
  fieldPlugins: GenericFieldPlugin[],
  isMobile?: boolean,
) {
  const notableCore = useNotableCore();
  const viewContent = useCurrentViewQueryModel();
  const { rowExpanded } = notableCore.layoutController;
  const gridViewRef = useRef<GridViewApi>(null);
  const lang = useGetContextLocale();
  const { selectedCellPosition } = notableCore.layoutController;
  useEffect(() => {
    gridViewRef.current?.unselectAndScrollToCellEvent(selectedCellPosition);
  }, [selectedCellPosition]);
  const { aiGenerationController } = notableCore.decoratorController;
  const { currentSheetId, currentSheet } = notableCore.frameworkController;
  const { columnTasks } = aiGenerationController;
  const currentSheetTasks = useMemo(() => {
    return columnTasks.filter((task) => task.sheetId === currentSheetId);
  }, [columnTasks, currentSheetId]);

  const { buildFieldToGroupMap, enable: fieldGroupEnabled } = notableCore.fieldGroupController;

  const fieldGroupMap = useMemo(() => {
    return notableCore.fieldGroupController.getFieldGroupMap(currentSheet?.id ?? '');
  }, [currentSheet, notableCore.fieldGroupController]);

  const fieldToGroupMap = useMemo(() => {
    return buildFieldToGroupMap(fieldGroupMap);
  }, [fieldGroupMap, buildFieldToGroupMap]);

  const fieldMinWidthMap = useMemo(() => {
    if (isMobile) return undefined;
    if (currentSheetTasks.length === 0) return undefined;
    return fromPairs(currentSheetTasks.map((task) => [task.fieldId, RUNING_TASK_MIN_FIELD_WIDTH_MAP[lang] ?? 260]));
  }, [currentSheetTasks, isMobile, lang]);

  // TODO: extract
  const { allColumnsIncludingHidden, columns } = useMemo(() => {
    const metaFields = [
      ...model.columns.filter((column) => column.isPrimary),
      ...model.columns.filter((column) => !(column.isPrimary)),
      // ...model.columns.filter((column) => column.isPrimary && column.type !== 'primaryDoc'),
      // ...model.columns.filter((column) => !(column.isPrimary && column.type !== 'primaryDoc')),
    ];
    const { widthMap } = model.custom;
    const originIndexes = fromPairs(metaFields.map((f, idx) => [f.id, idx]));

    const allColumn = sortSameFieldGroupsTogether(metaFields, (f) => fieldToGroupMap[f.id])
      .map((field, idx) => modelFieldToViewColumn({
        field,
        widthMap,
        fieldPlugins,
        idx: originIndexes[field.id],
        sortedIndex: idx,
        isMobile,
        fieldMinWidthMap,
        fieldToGroupMap,
      }));

    return {
      allColumnsIncludingHidden: allColumn,
      columns: allColumn.filter((column) => !get(model.custom, ['hiddenFields', column.key])),
    };
  }, [fieldPlugins, model.columns, model.custom, fieldMinWidthMap, fieldToGroupMap, fieldGroupEnabled]);

  const aggregate = useMemo(
    () =>
      model.columns.reduce(
        (pre, { id }: FieldVM) => ({
          ...pre,
          [id]: mappingAggregateFun(id, model.aggregate, viewContent.aggregate),
        }),
        {},
      ),
    [model, viewContent],
  );

  useEffect(() => {
    if (rowExpanded) {
      gridViewRef.current?.stopGridFromListeningToHotkeys();
    } else {
      gridViewRef.current?.unstopGridFromListeningToHotkeys();
    }
  }, [rowExpanded]);

  return {
    gridViewRef,
    allColumnsIncludingHidden,
    columns,
    aggregate,
    fieldGroupMap,
  };
}

export default useBuildGridViewProps;
