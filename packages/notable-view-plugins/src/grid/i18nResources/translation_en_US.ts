const resource: {[key: string]: string} = {
  "edit_fieldGroup_desc": "Please enter a group description",
  "notable_notableComponents_added_to_fieldGroup": "Moved to group",
  "notable_notableComponents_edit_fieldGroup_desc": "Group Description",
  "notable_notableComponents_hide_fieldGroup": "Hide Group",
  "notable_notableComponents_freezeTo_fieldGroup": "Freeze to This Group",
  "group_removed": "Removed from group",
  "we_notable_move_out": "Remove",
  "notable_notableComponents_fieldGroup_dissolved": "Group has been dissolved",
  "we_notable_confirm_move_out_of_field_group": "Confirm removal from the group?",
  "auto_delete_field_group_after_last_field_removed": "After removing the last field, the group will be automatically deleted",
  "notable_notableComponents_modify_fieldGroup": "Modify group",
  "notable_notableComponents_create_fieldGroup": "Create group",
  "notable_notableComponents_dissolve_fieldGroup": "Dissolve group",
  "notable_notableComponents_moveOutOf_fieldGroup": "Remove from group",
  "notable_notableComponents_moveInto_fieldGroup": "Add to group",
  "waiting_status": "Waiting",
  "ai_in_progress_save_later": "AI is currently processing {{name}}, and cannot generate at the moment. It is advised to click 'Save Settings Only' and try again later.",
  "gen_fail": "Generation Failed",
  "update_content": "Update Content",
  "generating": "Generating",
  "ai_gen_in_progress": "AI results generation in progress ({{progress}}%)",
  "ai_gen_cancel_failed": "Failed to cancel AI generation, please try again later",
  "task_terminating": "Task is terminating",
  "we_notable_field_flow_cancel": "Terminate",
  "abort_generation": "Do you want to stop the generation?",
  "cell_content_preserve": "The generated cell content will continue to be saved",
  "notable_notableGridView_TableView": "Grid View",
  "notable_notableViewPlugins_GridViewIntro": "Data Detail Specification Classification",
  "notable_notableGridView_Statistics": "Statistics",
  "notable_notableGridView_TheContentIsEmpty": "(Empty)",
  "notable_notableGridView_InsertRowsUpPrefix": "Insert",
  "notable_notableGridView_InsertRowsUpSuffix": "above",
  "notable_notableGridView_InsertRowsDownPrefix": "Insert",
  "notable_notableGridView_InsertRowsDownSuffix": "below",
  "notable_notableViewPlugins_Comments": "Comments",
  "notable_notableGridView_ClearArea": "Clear selected area",
  "notable_notableViewPlugins_OpenHistory": "Open record history",
  "notable_notableViewPlugins_AddToFilter": "Filter according to {{name}}",
  "notable_notableGridView_DeleteMutipleRow": "Delete {{count}} rows",
  "notable_notable_grid_moved_because_of_group": "This record doesn't belong to current group and will be rearranged if you click on other areas.",
  "notable_notable_grid_moved_because_of_filter": "This record is filtered and will be hidden if you click on other areas.",
  "notable_notable_grid_moved_because_of_sort": "This record is being adjusted by sorting and will be rearranged if you click on other areas.",
  "notable_notableGridView_TheDataVolumeIsToo": "The data volume is too large. All options are temporarily unavailable.",
  "we_notable_confirm_deletion_of_field_with_extra_info": "Are you sure you want to delete the field “{{fieldName}}”? After deletion, {{extraInfo}}",
  "we_notable_multi_field_used_as_kanban_view_grouping_basis": "'{{fieldNames}}' has been used as a grouping basis for Kanban views. After deletion, the grouping basis will be adjusted.",
  "we_notable_restore_original_field_by_undoing": "You can restore the original field by undoing{{shortcut}}.",
  "notable_notableGridView_DeleteAFieldOrColumn": "Delete a field or column",
  "we_notable_cancel": "Cancel",
  "we_notable_delete": "Delete",
  "notable_notableGridView_FillColorOfColumn": "Fill Entire Column",
  "notable_notableGridView_ModifyFieldsOrColumns": "Edit fields/Columns",
  "notable_notableGridView_InsertAFieldOrColumn": "Insert a field or column to the left",
  "notable_notableGridView_InsertAFieldOrColumn1": "Insert a field or column to the right",
  "notable_notableGridView_CopyFieldsOrColumns": "Copy fields or columns",
  "notable_notableGridView_PositiveSequence": "Positive sequence",
  "notable_notableGridView_Reverse": "Reverse",
  "notable_notableGridView_HideFieldsOrColumns": "Hide fields or columns",
  "notable_notableGridView_FrozenFieldsOrColumns": "Freeze to this field/column",
  "notable_notableGridView_FilterByColumnname": "Filter by {{columnName}}",
  "notable_notableGridView_GroupByColumnname": "Group by {{columnName}}",
  "notable_notableGridView_DragToSelectFrozenArea": "Drag to adjust frozen area",
  "notable_notable_grid_record_delete_by_others": "This record is no longer viewed or deleted by other collaborators.",
  "notable_notable_grid_tips": "Tips",
  "we_notable_save_and_close": "Save and close",
  "we_notable_still_close": "Still Close",
  "we_notable_content_not_saved_notice": "The modified content has not been saved.",
  "we_notable_content_not_saved_reconfirm": "After closing, your changes will not be saved. Confirm closing?",
  "we_notable_already_selected": "Selected",
  "notable_notableViewPlugins_XRecord": "{{count}} records",
  "we_notable_paste_xx_records_hidden": "{{count}} pasted records hidden",
  "notable_notableViewFramework_TheFirstColumnIsThe": "The first column of fields is the index of each piece of data and cannot be deleted, moved, or hidden.",
  "notable_notableViewFramework_SortNotAllowMoveRow": "View has been set to sort, unable to drag and drop to adjust record order",
  "we_notable_continue": "Continue",
  "we_notable_notice": "Notice",
  "delete_row_max_limit": "The number of rows to be deleted exceeds the upper limit of {{num}}. Continue to delete the first {{num}} rows.",
  "upgrade_tips": "Click to upgrade old data",
  "upgrade_ongoing": "Upgrading",
  "upgrade_confirm": "Upgrade ",
  "upgrade_title": "Upgrade old data ",
  "upgrade_content": "Upgrading will change cell data, which may trigger formula recalculation, automation, etc. ",
  "upgrade_done_refresh_tips": "Upgrade done, please refresh the page ",
  "upgrade_done_tips": "Upgrade done ",
  "we_notable_partition_sheet_edit_disabled": "Historical period data table does not support editing",
  "notable_biz_advanced_permission_none": "No permission",
  "notable_notableKanbanView_KanbanView": "Kanban View",
  "notable_notableCalendarView_CalendarView": "Calendar View",
  "notable_notableGalleryView_AlbumView": "Gallery View",
  "notable_notableGridView_InsertViewByField": "Create {{columnName}} by {{columnName}}",
  "notable_search_the_record_want_to_assoc": "Search related records",
  "notable_only_view_selected_records": "View only selected records",
  "notable_no_selected_records_yet": "No selected records yet",
  "notable_no_records": "No records",
  "notable_notableViewFramework_Ok": "Determine",
  "edit_field_desc": "Edit Field/Column Description",
  "field_desc": "Field/Column Description",
  "field_desc_placeholder": "Please enter field/column description",
  "notable_notableViewFramework_subRecord_error_multi": "The current record is associated with more than one parent record",
  "notable_notableViewFramework_subRecord_error_maxLevel": "The maximum number of sub-record levels is 5, and the excess will not be displayed.",
  "notable_notableViewFramework_subRecord_error_cycle": "Current record has circular Association, please check child Records",
  "notable_formula_invalid_result_field_type_desc": "The reference field type has been changed, please update the field format",
  "notable_formula_invalid_filter_up": "Lookup Reference Invalidated",
  "notable_formula_invalid_lookup": "Associated reference has expired",
  "notable_formula_invalid_to_config": "View Configuration",
  "badge_limited_time_free": "LIMITED TIME FREE",
  "copy": "Copy",
  "cut": "Shear",
  "paste": "Paste",
  "we_notable_ccp_fail": "Paste failed",
  "we_notable_field_decorator_range_refresh": "AI Update",
  "record_add_sub_alias": "Add Child {{recordName}}",
  "record_duplicate_alias": "Copy {{recordName}}",
  "record_open_alias": "Expand {{recordName}}",
  "record_delete_alias": "Delete {{recordName}}",
  "record_hierarchy_alias": "Parent {{recordName}}",
  "notable_search_the_record_want_to_assoc_alias": "Search related {{recordName}}",
  "notable_only_view_selected_records_alias": "View only selected {{recordName}}",
  "notable_no_selected_records_yet_alias": "No selected {{recordName}}",
  "notable_no_records_alias": "No {{recordName}} available",
  "notable_grid_add_row_tooltip": "You can also click a cell and press Shift + Enter to add.",
  "notable_grid_add_column_tooltip": "Add Field/Column",
  "we_notable_view_same_as_left_column_width": "{{widthInfo}}, same width as left column ",
  "we_notable_view_sync_column_width_confirm_title": "Are you sure you want to synchronize the column width of {{name}} to all table views? ",
  "we_notable_view_sync_column_width_confirm_content": "After synchronization, the column width in all table views that display this column will be equal to {{width}}px. This operation will keep the width of the same column consistent across all table views ",
  "we_notable_view_sync_column_width": "Synchronize Column Width ",
  "auto_adjust_col_width": "Auto adjust column width",
  "we_notable_view_auto_adjust_all_col_widths": "Auto Adjust All Column Widths",
  "we_notable_view_col_width_adj_options": "Column Width Adjustment Options",
  "we_notable_view_same_width_as_right_col": "Same Width as Right Column",
  "we_notable_view_adjust_col_width": "Adjust Column Width",
  "we_notable_view_same_width_as_left_col": "Same Width as Left Column",
  "we_notable_view_right_click": "Right Click",
  "we_notable_view_evenly_distribute_all_cols_width": "Evenly Distribute All Column Widths",
  "we_notable_view_double_click": "Double Click",
  "we_notable_view_auto_adjust_col_width": "Automatically adjust width based on column header and content",
  "we_notable_view_auto_adjust_all_cols_width": "Automatically adjust the width of all columns based on column headers and content",
  "we_notable_view_sync_col_width_across_views": "Synchronize the column with a width of {{width}}px in all table views",
  "notable_notableGridView_DeleteAFieldOrColumn_primary_field_notice": "This column is the primary field and cannot be deleted",
  "notable_notableGridView_DeleteAFieldOrColumn_primary_field_learnMore": "Learn More",
  "notable_notableComponents_md_preview_tips": "Display the rendering effect of Markdown content",
  "notable_field_button_status_reset": "Reset",
  "notable_field_button_open_config_modal": "Set up the operation",
  "we_notable_import_view_sheet": "View",
  "we_notable_primary_doc_open_guide_title": "Click to view and open the document ",
  "we_notable_guide_primary_doc_guide_title_description": "Supports inserting rich content such as videos, maps, code blocks, etc. Drag and drop layout freely, design pages like building a web page. Switch tables/documents at any time to meet different scene needs! ",
  "we_notable_guide_primary_doc_guide_title": "The game has been upgraded! Each record is a DingTalk document ",
  "notable_form_setting_partition_help_link": "Learn More",
  "we_notable_mobile_guide_upgrade_immediately": "Upgrade Now ",
  "notable_components_grid_hierarchy_count": "{{count}} child records ",
  "notable_notableComponents_RecordOpenModal_description": "Open document in pop-up window ",
  "notable_notableComponents_RecordOpenSide_description": "Open document in right sidebar ",
  "notable_notableComponents_RecordOpenFullPage_description": "Open document full page ",
  "we_notable_primary_doc_open_guide_description": "Try clicking \"View\" next time to continue editing your document at any time ",
  "notable_notableViewFramework_primary_doc_tip": "This is the primary field of the table, used to identify each record in the table. It cannot be deleted or hidden ",
  "notable_common_expand_row": "Expand record"
};

export default resource