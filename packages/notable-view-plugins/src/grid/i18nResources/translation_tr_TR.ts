const resource: {[key: string]: string} = {
  "edit_fieldGroup_desc": "Lütfen grup açıklaması giriniz",
  "notable_notableComponents_added_to_fieldGroup": "Gruba taşındı",
  "notable_notableComponents_edit_fieldGroup_desc": "Grup Açıklaması",
  "notable_notableComponents_hide_fieldGroup": "Grubu Gizle",
  "notable_notableComponents_freezeTo_fieldGroup": "Bu Gruba Kadar Dondur",
  "group_removed": "<PERSON><PERSON><PERSON><PERSON> çıkarıldı",
  "we_notable_move_out": "<PERSON><PERSON><PERSON>",
  "notable_notableComponents_fieldGroup_dissolved": "Grup dağıtıldı",
  "we_notable_confirm_move_out_of_field_group": "<PERSON>rup<PERSON> çı<PERSON>ılsın mı?",
  "auto_delete_field_group_after_last_field_removed": "Son alan kaldırıldıktan sonra grup otomatik olarak silinecek",
  "notable_notableComponents_modify_fieldGroup": "<PERSON><PERSON><PERSON> düzen<PERSON>",
  "notable_notableComponents_create_fieldGroup": "Grup oluştur",
  "notable_notableComponents_dissolve_fieldGroup": "<PERSON>rubu dağıt",
  "notable_notableComponents_moveOutOf_fieldGroup": "Gruptan çıkar",
  "notable_notableComponents_moveInto_fieldGroup": "Gruba ekle",
  "waiting_status": "Beklemede",
  "ai_in_progress_save_later": "AI, {{name}} işlemini yürütüyor ve şu anda üretimi gerçekleştiremiyor. Ayarları yalnızca kaydetmek için tıklayın ve daha sonra tekrar deneyin.",
  "gen_fail": "Oluşturma Başarısız",
  "update_content": "Güncelleme İçeriği",
  "generating": "Oluşturuluyor",
  "ai_gen_in_progress": "AI sonuçları oluşturma işlemi devam ediyor ({{progress}}%)",
  "ai_gen_cancel_failed": "AI üretimi iptal edilemedi, lütfen daha sonra tekrar deneyin",
  "task_terminating": "Görev sonlandırılıyor",
  "we_notable_field_flow_cancel": "Sonlandırma",
  "abort_generation": "Üretimi durdurmak istiyor musunuz?",
  "cell_content_preserve": "Oluşturulan hücre içeriği kaydedilmeye devam edilecek",
  "notable_notableGridView_TableView": "Masa görünümü",
  "notable_notableViewPlugins_GridViewIntro": "Veri detay özellikleri sınıflandırma",
  "notable_notableGridView_Statistics": "İstatistikler",
  "notable_notableGridView_TheContentIsEmpty": "(Içerik boş)",
  "notable_notableGridView_InsertRowsUpPrefix": "Satır",
  "notable_notableGridView_InsertRowsUpSuffix": "yukarı ekle",
  "notable_notableGridView_InsertRowsDownPrefix": "Satır",
  "notable_notableGridView_InsertRowsDownSuffix": "aşağı ekle",
  "notable_notableViewPlugins_Comments": "Yorumlar",
  "notable_notableGridView_ClearArea": "Seçimi temizle",
  "notable_notableViewPlugins_OpenHistory": "Kayıt geçmişini görüntüle",
  "notable_notableViewPlugins_AddToFilter": "{{name}} tarafından filtre",
  "notable_notableGridView_DeleteMutipleRow": "Sil {{count}} satır",
  "notable_notable_grid_moved_because_of_group": "Bu kayıt artık bu gruba ait değil, başka bir alana tıkladıktan sonra kaldırılacak",
  "notable_notable_grid_moved_because_of_filter": "Bu kayıt filtrelendi ve başka bir alana tıklayarak kaldırılacak",
  "notable_notable_grid_moved_because_of_sort": "Bu kayıt sıralama nedeniyle taşınacak ve başka bir alana tıklayarak taşınacak",
  "notable_notableGridView_TheDataVolumeIsToo": "Çok fazla veri, geçici olarak kullanılamıyor",
  "we_notable_confirm_deletion_of_field_with_extra_info": "“{{fieldName}}” alanını silmek istediğinizden emin misiniz? Silindikten sonra, {{extraInfo}}",
  "we_notable_multi_field_used_as_kanban_view_grouping_basis": "'{ {fieldNames}}'kanban görünümleri için gruplama temeli olarak kullanılmıştır. Silme işleminden sonra gruplama temeli ayarlanacaktır.",
  "we_notable_restore_original_field_by_undoing": "{{shortcut}} geri alarak orijinal alanı geri yükleyebilirsiniz.",
  "notable_notableGridView_DeleteAFieldOrColumn": "Alanı/sütunu sil",
  "we_notable_cancel": "İptal et",
  "we_notable_delete": "Sil",
  "notable_notableGridView_FillColorOfColumn": "Tüm dolgu",
  "notable_notableGridView_ModifyFieldsOrColumns": "Alanı/sütunu düzenle",
  "notable_notableGridView_InsertAFieldOrColumn": "Sola alan/sütun ekle",
  "notable_notableGridView_InsertAFieldOrColumn1": "Alan/sütun sağa ekle",
  "notable_notableGridView_CopyFieldsOrColumns": "Alanları/sütunları kopyala",
  "notable_notableGridView_PositiveSequence": "Pozitif sıra",
  "notable_notableGridView_Reverse": "Ters sipariş",
  "notable_notableGridView_HideFieldsOrColumns": "Alanları/sütunları gizle",
  "notable_notableGridView_FrozenFieldsOrColumns": "Bu alana dondur/sütun",
  "notable_notableGridView_FilterByColumnname": "{{columnName}} tarafından filtre",
  "notable_notableGridView_GroupByColumnname": "Grup tarafından {{columnName}}",
  "notable_notableGridView_DragToSelectFrozenArea": "Dondurulmuş alanı ayarlamak için sürükleyip bırakın",
  "notable_notable_grid_record_delete_by_others": "Bu kayıt artık diğer işbirlikçiler tarafından görüntülenmiyor veya silinmiyor.",
  "notable_notable_grid_tips": "Istemi",
  "we_notable_save_and_close": "Kaydet ve kapat",
  "we_notable_still_close": "Hala yakın",
  "we_notable_content_not_saved_notice": "Değişiklikler kaydedilmedi",
  "we_notable_content_not_saved_reconfirm": "Kapandıktan sonra değişiklikleriniz kaydedilmeyecek. Kapatmayı onayla?",
  "we_notable_already_selected": "Seçilen",
  "notable_notableViewPlugins_XRecord": "{{count}} kayıtları",
  "we_notable_paste_xx_records_hidden": "{{count}} yapıştırılan kayıtlar gizli",
  "notable_notableViewFramework_TheFirstColumnIsThe": "Alanların ilk sütunu her bir veri parçasının indeksidir ve silinemez, taşınabilir veya gizlenemez.",
  "notable_notableViewFramework_SortNotAllowMoveRow": "Görünüm, kayıt siparişini ayarlamak için sürükleyip bırakamayacak şekilde ayarlandı",
  "we_notable_continue": "Devam et",
  "we_notable_notice": "Uyarı",
  "delete_row_max_limit": "Silinecek satır sayısı {{num}} üst sınırını aşmaktadır. İlk {{num}} satırlarını silmeye devam edin.",
  "upgrade_tips": "Eski veriler tespit edildi, bir tıklama ile yükseltme seçeneği. ",
  "upgrade_ongoing": "Yükseltme tamamlanıyor, lütfen bekleyin. ",
  "upgrade_confirm": "Yükseltme ",
  "upgrade_title": "Eski veriler bir tıklama ile yükseltildi. ",
  "upgrade_content": "Lütfen dikkat edin, yükseltme hücre verilerini değiştirir ve formül tekrar hesaplaması, otomatik işlemler gibi eylemleri tetikleyebilir. ",
  "upgrade_done_refresh_tips": "Yükseltme tamamlandı, lütfen sayfayı yenileyin. ",
  "upgrade_done_tips": "Yükseltme tamamlandı ",
  "we_notable_partition_sheet_edit_disabled": "Tarihi dönem veri tablosu düzenlemeyi desteklemiyor",
  "notable_biz_advanced_permission_none": "Izin yok",
  "notable_notableKanbanView_KanbanView": "Kanban görünümü",
  "notable_notableCalendarView_CalendarView": "Takvim görünümü",
  "notable_notableGalleryView_AlbumView": "Kitap görünümü",
  "notable_notableGridView_InsertViewByField": "{{columnName}} ile {{viewName}} oluştur",
  "notable_search_the_record_want_to_assoc": "İlgili kayıtları arayın",
  "notable_only_view_selected_records": "Sadece seçilen kayıtları gör",
  "notable_no_selected_records_yet": "Seçilen kayıt yok",
  "notable_no_records": "Kayıt yok",
  "notable_notableViewFramework_Ok": "Tamam",
  "edit_field_desc": "Düzenleme alanı/sütun açıklaması",
  "field_desc": "Alan/sütun açıklaması",
  "field_desc_placeholder": "Lütfen alan/sütun açıklamasına girin",
  "notable_notableViewFramework_subRecord_error_multi": "Mevcut kayıt birden fazla ana kayıt ile ilişkilidir",
  "notable_notableViewFramework_subRecord_error_maxLevel": "Alt kayıt seviyelerinin maksimum sayısı 5'dir ve fazlalık görüntülenmeyecektir.",
  "notable_notableViewFramework_subRecord_error_cycle": "Mevcut kayıt dairesel dernek var, lütfen çocuk kayıtlarını kontrol edin",
  "notable_formula_invalid_result_field_type_desc": "Referans alan tipi değiştirildi, lütfen alan formatını güncelleyin",
  "notable_formula_invalid_filter_up": "Arama referansı geçersiz",
  "notable_formula_invalid_lookup": "Ilişkili referans süresi doldu",
  "notable_formula_invalid_to_config": "Yapılandırmayı görüntüle",
  "badge_limited_time_free": "Muafiyet",
  "copy": "Kopyala",
  "cut": "Kesme",
  "paste": "Yapıştır",
  "we_notable_ccp_fail": "Yapıştır başarısız oldu",
  "we_notable_field_decorator_range_refresh": "AI Güncellemesi",
  "record_add_sub_alias": "Alt {{recordName}} Ekle",
  "record_duplicate_alias": "{{recordName}} Kopyala",
  "record_open_alias": "{{recordName}} Genişlet",
  "record_delete_alias": "{{recordName}} Sil",
  "record_hierarchy_alias": "Üst {{recordName}}",
  "notable_search_the_record_want_to_assoc_alias": "İlgili {{recordName}} ara",
  "notable_only_view_selected_records_alias": "Yalnızca seçili {{recordName}} gör",
  "notable_no_selected_records_yet_alias": "Seçilen {{recordName}} yok",
  "notable_no_records_alias": "{{recordName}} yok",
  "notable_grid_add_row_tooltip": "Hücreye tıklayıp Shift + Enter tuşuna basarak da ekleyebilirsiniz.",
  "notable_grid_add_column_tooltip": "Alan/Sütun Ekle",
  "we_notable_view_same_as_left_column_width": "{{genişlikBilgisi}}, sol sütunla aynı genişlikte ",
  "we_notable_view_sync_column_width_confirm_title": "{{Name}} sütun genişliklerini tüm tablo görünümlerine senkronize etmek istediğinizden emin misiniz?",
  "we_notable_view_sync_column_width_confirm_content": "Synchronizasyon tamamlandıktan sonra, bu sütunu gösteren tüm tablo görünümünde sütun genişliği {{width}} px olacak ve bu işlem aynı sütunun farklı tablo görünümlerindeki genişliğini aynı tutacaktır. ",
  "we_notable_view_sync_column_width": "Sütun genişliğini eşitle ",
  "auto_adjust_col_width": "Sütun genişliğini otomatik ayarla",
  "we_notable_view_auto_adjust_all_col_widths": "Tüm Sütun Genişliklerini Otomatik Ayarla",
  "we_notable_view_col_width_adj_options": "Sütun Genişliği Ayarlama Seçenekleri",
  "we_notable_view_same_width_as_right_col": "Sağ Sütun ile Aynı Genişlik",
  "we_notable_view_adjust_col_width": "Sütun Genişliğini Ayarla",
  "we_notable_view_same_width_as_left_col": "Sol Sütun ile Aynı Genişlik",
  "we_notable_view_right_click": "Sağ Tık",
  "we_notable_view_evenly_distribute_all_cols_width": "Tüm Sütun Genişliklerini Eşit Dağıt",
  "we_notable_view_double_click": "Çift Tıklama",
  "we_notable_view_auto_adjust_col_width": "Sütun başlığı ve içeriğine göre genişliği otomatik ayarla",
  "we_notable_view_auto_adjust_all_cols_width": "Sütun başlıkları ve içeriğine göre tüm sütunların genişliğini otomatik olarak ayarla",
  "we_notable_view_sync_col_width_across_views": "Bu sütunun tüm tablo görünümünde genişliği {{width}} px olarak eşleştirin ",
  "notable_notableGridView_DeleteAFieldOrColumn_primary_field_notice": "Bu sütun ana sütündür ve silinemez",
  "notable_notableGridView_DeleteAFieldOrColumn_primary_field_learnMore": "Daha Fazla Öğren",
  "notable_notableComponents_md_preview_tips": "Markdown içeriğinin render etme etkisini göster",
  "notable_field_button_status_reset": "Yeniden ayarla",
  "notable_field_button_open_config_modal": "İşlemin uygulanması ayarla",
  "we_notable_import_view_sheet": "Görünüm",
  "we_notable_primary_doc_open_guide_title": "Dokümanı açmak için tıklayın. ",
  "we_notable_guide_primary_doc_guide_title_description": "Videolar, haritalar ve kod blokları gibi zengin içeriklerin eklenmesini destekler. Sürükle ve bırak dizgi serbestçe ve web sayfaları oluşturmak gibi tasarım sayfaları. Farklı senaryoların ihtiyaçlarını karşılamak için her zaman tabloları/belgeleri değiştirin!",
  "we_notable_guide_primary_doc_guide_title": "Oyunu yükseltin! Her satır kaydı DingTalk belgeleridir. ",
  "notable_form_setting_partition_help_link": "Daha fazla bilgi edinin",
  "we_notable_mobile_guide_upgrade_immediately": "Hemen yükseltin ",
  "notable_components_grid_hierarchy_count": "Toplam {{count}} alt kayıtlı ",
  "notable_notableComponents_RecordOpenModal_description": "Pencere açılırken belge açılır ",
  "notable_notableComponents_RecordOpenSide_description": "Sağ kenar menüsünde belge açın ",
  "notable_notableComponents_RecordOpenFullPage_description": "Sayfa tamamen açın ",
  "we_notable_primary_doc_open_guide_description": "İleriye doğru ilerleyin ve belgenizi düzenlemeye devam edin. ",
  "notable_notableViewFramework_primary_doc_tip": "Bu, tablonun ana alanıdır ve tablodaki her kaydı belirten bir etikettir. Silinemez veya gizlenebilir. ",
  "notable_common_expand_row": "Kaydı genişlet"
};

export default resource