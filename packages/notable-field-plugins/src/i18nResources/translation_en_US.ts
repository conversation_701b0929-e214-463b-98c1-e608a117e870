const resource: {[key: string]: string} = {
  "neg_value_err": "The value cannot be negative, please re-enter",
  "notable_notableFields_GouXuan": "Checkbox",
  "notable_notableFields_Date": "Date",
  "notable_notableFields_WebSite": "Link",
  "notable_notableFields_MultiSelect": "Multiple select",
  "notable_notableFields_Digital": "Number",
  "notable_notableFields_Score": "Score",
  "notable_notableFields_Radio": "Single select",
  "notable_notableFields_Text": "Text",
  "notable_notableFields_Email": "Email",
  "notable_notableFields_Email_Invalid": "Invalid email address",
  "notable_notableFields_Formula": "Formula",
  "notable_notableFields_Lookup": "Lookup",
  "notable_notableFields_Association": "Association",
  "notable_notableFields_CreatedTime": "Created time",
  "notable_notableFields_UpdatedTime": "Last update time",
  "notable_notableFields_Creator": "Created by",
  "notable_notableFields_Updater": "Last modified by",
  "notable_notableFields_IDCard": "Chinese ID Card",
  "notable_notableFields_address": "Chinese Address",
  "notable_notableFields_autoNumber": "Numbering",
  "notable_notableFields_group": "Group",
  "notable_notableFields_progress": "Progress",
  "notable_notableFields_barcode": "Barcode",
  "notable_notableComponents_EnterALink": "Enter a link",
  "notable_notableComponents_Determine": "Submit",
  "notale_notableFields_select_placeholder": "Please Select",
  "notable_notableFields_noPermissionPlaceholder": "No content",
  "notable_notableComponents_EnterOptions": "Please enter option content",
  "notable_notableComponents_Enter": "Enter",
  "notable_search_the_record_want_to_assoc": "Search related records",
  "notable_notableComponents_Cancel": "Cancel",
  "notable_fields_association_title": "Record of association table \"{{name}}\"",
  "notable_field_both_way_link": "Both-way Link",
  "notable_field_one_way_link": "One-way Link",
  "notable_auto_num_field_delete_confirm": "The existing numbers will not be retained after deletion, so please proceed with caution.",
  "notable_auto_num_field_change_to_other_confirm": "The existing numbers will not be retained after the modification, so please proceed with caution.",
  "we_notable_save": "Save",
  "we_notable_cancel": "Cancel",
  "we_notable_field_geolocation": "Geolocation",
  "we_notable_search_location": "Search Location",
  "we_notable_no_search_results": "No search results found",
  "we_notable_select_geolocation": "Select geolocation",
  "we_notable_mobile_locate_only": "Mobile geolocate only",
  "we_notable_get_current_location": "Click to locate",
  "we_notable_geolocation_positioning": "Positioning",
  "we_notable_geolocation_positioning_failed": "Positioning failed",
  "we_notable_geolocation_refresh": "Repositioning",
  "we_notable_updated": "Updated",
  "notable_notableFieldPlugins_SelectAnAssociatedField": "Please select associated fields",
  "notable_notableFieldPlugins_SelectAValueField": "Please select a value field",
  "notable_notableFieldPlugins_SelectASummaryMethod": "Please select a summary method",
  "notable_notableFieldPlugins_ErrorInFormula": "Error in formula",
  "notable_formula_error_invalid_result_field_type": "The calculation result does not match the current column field format.",
  "notable_notableFieldPlugins_Lookup": "Find Reference",
  "notable_notableFieldPlugins_SelectATableToReference": "Please select a data table to reference",
  "notable_notableFieldPlugins_SelectAFieldToReference": "Please select a field to reference",
  "notable_notableFieldPlugins_SetTheCompleteSearchCriteria": "Please set full lookup criteria",
  "notable_notableFields_Select_Config_DisplayMode": "Show Style",
  "notable_notableFields_Select_Config_DisplayMode_Flat": "Tile",
  "notable_notableFields_Select_Config_DisplayMode_Dropdown": "Dropdown",
  "notable_please_select_association_sheet": "Please select the table you want to associate",
  "notable_notableComponents_EnterContent": "Enter content",
  "notable_field_create_tips": "The system is automatically filled and modification is not supported.",
  "notable_unnamed_record": "Unnamed record",
  "notable_notableFieldPlugins_NoAssociatedRecords": "No associated record",
  "notable_notableFieldPlugins_AddAnAssociatedRecordFrom": "Add associated records from {{name}}",
  "notable_notableFieldPlugins_SelectAssociatedRecord": "Please select a record",
  "notable_notableComponents_NoResultsPressEnterTo": "No option, Enter content to add",
  "notable_notableComponents_NotFoundOption": "Option not found",
  "notable_notableComponents_Currency": "Currency",
  "notable_field_select_tips": "Please select from the drop-down menu",
  "notable_notableComponents_SearchOptions": "Search Options",
  "notable_notableComponents_SearchAndAddOptions": "Search or add options",
  "notable_field_number_tips": "Please enter a number",
  "notable_linkfield_label_placeholder": "Please enter the display text",
  "notable_linkfield_link_placeholder": "Please enter a link",
  "notable_notableComponents_FileUploading": "File uploading...",
  "notable_message_file_upload_success": "Upload success",
  "notable_field_date_tips": "Please select a date",
  "notable_notableComponents_ShiftEnterLineBreakEnter": "Shift + Enter line break Enter end editing",
  "notable_notableComponents_EditLink": "Edit Link",
  "notable_notableFieldPlugins_CalcValueCantEdit": "The data comes from the calculation result of the formula and cannot be modified.",
  "notable_notableCore_Attachment": "Annex",
  "notable_notableFieldPlugins_AssociatedTableDeleted": "The associated data table has been deleted, please reset",
  "notable_notableCore_Option": "Option one",
  "notable_notableCore_Option2": "Option 2",
  "notable_cell_limitation_exceeded_field_text": "Content exceeds {{length}} word limit, unable to save",
  "notable_cell_limitation_exceeded_tip_field_select": "Exceeded {{count}} options, unable to save",
  "notable_cell_limitation_exceeded_tip_field_association": "Unable to associate, maximum {{count}} records can be associated",
  "notable_cell_limitation_exceeded_tip_field_department": "Exceeded {{count}} Department limit, unable to save",
  "notable_cell_limitation_exceeded_field_rich_text": "Rich text exceeds {{length}} word limit. Unable to save",
  "notable_notableCommon_OptionOrder": "Option Order",
  "notable_notableCommon_OptionReverseOrder": "Option reverse order",
  "notable_notableCommon_Sequence": "Sequence",
  "notable_notableCommon_ReverseOrder": "Reverse order",
  "number_filed_only_allow_number": "Only numbers allowed",
  "notable_components_attachmentOnlyCamera": "Can only be uploaded via mobile shooting",
  "notable_NotableCommon_IDCardBirthdayInvalid": "The birthdate check of the Chinese ID Card number did not pass.",
  "notable_NotableCommon_IDCardValidateCodeInvalid": "Chinese ID Card number check code verification not passed",
  "notable_NotableCommon_IDCardFormatInvalid": "Chinese ID Card number format verification fails",
  "notable_notableFields_Telephone": "Telephone",
  "notable_notableFields_TelephoneInvalid": "You can only enter phone numbers",
  "we_notable_field_sign": "Sign",
  "notable_notableFields_Address_Select_Placeholder": "Please select the province",
  "notable_notableFields_Address_Composed_Select_Placeholder": "Please select province/city/District/Street",
  "notable_notableFields_Address_Composed_Title2": "Detailed address",
  "notable_notableFields_Address_Composed_Placeholder": "Please enter detailed address",
  "notable_notableFields_Address_Cascader_Province": "Province",
  "notable_notableFields_Address_Cascader_City": "City",
  "notable_notableFields_Address_Cascader_District": "Districts",
  "notable_notableFields_Address_Cascader_Street": "Street",
  "notable_notableFields_Address_Form_Title2": "Province/city/District/Street",
  "notable_notableFields_Address_Error_invalidAddress": "Please select complete address information",
  "notable_notableFields_Address_Error_invalidDetail": "Verbose address cannot be empty",
  "we_notable_metion": "User",
  "we_notable_attachment_new": "Images and Attachments",
  "notable_field_rating_icon_type": "Icon",
  "notable_field_rating_value": "Score",
  "notable_field_rating_min_value": "Min Score",
  "notable_field_rating_max_value": "Max value",
  "notable_field_rating_bipolar_message": "Bipolar message",
  "notable_components_allowSelectMultiMention": "Allow multiple people to be selected",
  "notable_components_format": "Format",
  "we_notable_geolocation_option_mobile_locate_only": "Only mobile real-time positioning",
  "notable_notableComponents_SelectADisplayFormat": "Select a display format",
  "notable_notableComponents_Decimal_Places": "Decimal places",
  "notable_notableComponents_AddressType1": "Province, City District",
  "notable_notableComponents_AddressType2": "Provincial City District-Street",
  "notable_notableComponents_NewOptions": "Add options",
  "notable_notableFields_select_empty_add": "Add Option",
  "notable_form_ref_options_hint": "Option editing is disabled when options reference is enabled.",
  "notable_notableFormView_multi_select_limit": "Option Restrictions",
  "notable_notableFormView_multi_select_limit_min_title": "Least choices",
  "notable_notableFormView_multi_select_limit_max_title": "Most Choices",
  "notable_notableFormView_multi_select_limit_unlimit": "Unlimited",
  "notable_notableFormView_multi_select_limit_min_desc": "At least {{min}} items",
  "notable_notableFormView_multi_select_limit_min_desc_short": "Minimum {{min}}",
  "notable_notableFormView_multi_select_limit_max_desc": "at most {{ max }} items",
  "notable_notableFormView_multi_select_limit_max_desc_short": "Maximum {{max}} items",
  "notable_notableFormView_multi_select_limit_cur_desc": "{{cur}}/{{max}} currently selected",
  "notable_notableFormView_multi_select_limit_error_tip": "{{fieldName}} does not meet the option limit",
  "notable_notableViewFramework_select_extra_add": "Add Other",
  "notable_notableViewFramework_select_extra_name": "Other",
  "notable_notableViewFramework_select_extra_placeholder": "Please add a description",
  "notable_notableFormView_select_dropdown_warning_message": "Drop-down style does not support \"other\" option and insert picture",
  "notable_notableFormView_select_extra_invalid": "The current \"Other\" option is invalid, please delete and reconfigure",
  "notable_style_update_image": "Replace Image",
  "we_notable_delete": "delete",
  "notable_notableComponents_FileUploading_Type_Error": "Please select the correct picture file",
  "notable_notableComponents_FileUploading_MaxSize": "Maximum size of image is no more than {{value}}MB",
  "notable_notableComponents_FileUploadFailed": "File upload failed...",
  "notable_notableComponents_ThePasteContentCannotBe": "The paste content cannot be entered into cells of this type.",
  "notable_notableComponents_NumberFormat": "Number Format",
  "notable_notableComponents_progress_precision": "Decimal Places",
  "notable_notableComponents_progress_color": "Color",
  "notable_notableComponents_progress_customize": "Custom progress bar value",
  "notable_notableComponents_progress_min": "Start Value",
  "notable_notableComponents_progress_max": "Target Value",
  "notable_notableComponents_progress_range_null": "Please enter a value",
  "notable_notableComponents_progress_range_invalid": "The target value should be greater than the start value",
  "early": "Early.",
  "late": "Late",
  "we_notable_field_group_tips": "Please select a group",
  "we_notable_field_department_tips": "Please select department",
  "notable_notableFields_group_search_tip": "Search Group",
  "notale_notableFields_group_empty": "No Group Results",
  "notable_notableFields_group_max_count": "Select up to {{count}} groups",
  "notable_delete_association_field_extra_info": "Bidirectional correlation fields in other data tables will also be removed synchronously.",
  "notable_cell_limitation_exceeded_tip_field_person": "{{count}} person limit exceeded, unable to save",
  "notable_components_allowSelectMultiGroup": "Allow multiple groups to be selected",
  "notable_cell_limitation_exceeded_tip_field_attachment": "Maximum number of attachments exceeded {{count}}, unable to save",
  "we_notable_schedule_remind_search_empty_tips": "No search results for the time being",
  "we_notable_field_mention_tips": "Please select members",
  "we_notable_open_dingtalk": "Open DingTalk",
  "we_notable_attach_watermark_camera_failed": "Watermark camera shooting failed, please try again",
  "we_notable_attach_file_watermark_camera_only_in_dingtalk": "Only watermark camera is supported within the DingTalk",
  "we_notable_attach_file_watermark_camera": "Watermark Camera",
  "we_notable_attach_only_camera_text": "Tap to shoot",
  "we_notable_attach_upload_text": "Click Upload Image/File",
  "we_notable_attach_photo_or_file": "Album/File",
  "we_notable_attach_capturing": "Shooting",
  "dingtalk_docs": "DingTalk Docs",
  "we_notable_attach_file_upload_loading": "File uploading...",
  "we_notable_attach_file_upload_fail": "File Upload failed...",
  "we_notable_attach_file_limit": "File sizes below {{limit}}M are only supported",
  "we_notable_warning": "Warning",
  "we_notable_attach_delete_tip": "You are about to delete the attachment, please confirm whether to continue?",
  "we_notable_save_team_cancel_text": "Cancel",
  "we_notable_continue": "Continue",
  "we_notable_h5_attach_unknow_size": "Unknown size",
  "we_notable_richText": "Rich text",
  "notable_field_department": "Department",
  "we_notable_phone_call": "Call",
  "we_notable_sms": "Message",
  "notable_field_button": "Button",
  "notable_field_button_default_text": "Click button",
  "notable_field_button_text_error1": "Button text cannot be empty",
  "notable_field_button_text_error2": "Button text no more than 15 words",
  "notable_field_button_text_error3": "No more than 30 words.",
  "notable_field_button_execute_error": "Operation execution failed, please try again later or query the automation process execution record for more information",
  "notable_field_button_execute_timeout": "Operation execution timed out, please try again later",
  "notable_field_button_execute_success": "Operation executed successfully",
  "we_notable_already_associated": "Associated",
  "we_notable_redirect_to_asso_table": "Click to jump to associated table",
  "we_notable_field_used_for_kanban_grouping_change_will_adjust": "This field has been used for grouping in the Kanban view. Changing this field will adjust the basis for categorization and clear unmatched data.",
  "we_notable_restore_original_field_by_undoing": "You can restore the original field by undoing{{shortcut}}.",
  "we_notable_field_used_as_kanban_view_grouping_basis": "This field has been used as a basis for grouping in the Kanban view. Deleting it will adjust the basis for grouping.",
  "we_notable_confirm_deletion_of_field_with_extra_info": "Are you sure you want to delete the field “{{fieldName}}”? After deletion, {{extraInfo}}",
  "notable_empty_content": "No content available",
  "notable_field_barcode_only_mobile_scan": "Only mobile scanning is allowed for entry",
  "notable_field_barcode_only_scan_in_dingtalk_mobile": "Barcode scanning is only supported on DingTalk mobile app",
  "notable_field_barcode_click_to_scan": "Tap to scan",
  "notable_field_barcode_enter_or_scan": "Enter or scan the barcode",
  "notable_notableKanbanView_AddGroup_Placeholder": "Please enter a group title",
  "we_notable_group_with_member_already_exists_try_again": "A group with that member already exists, please choose another member",
  "we_notable_search_person": "Search Personnel",
  "we_notable_select_person": "Select Personnel",
  "we_notable_field_flow_name": "Flow",
  "we_notable_field_flow_paste_tip": "Paste failed, the data cannot be pasted as a flow Field",
  "calcing": "In calculation...",
  "notable_drawboard_export_failed": "Signature export failed, please upgrade DingTalk or browser version",
  "notable_form_options_not_found": "There are no options that meet the conditions.",
  "notable_complete": "Complete",
  "notable_notableFormView_Open": "Open",
  "notable_components_pasteOrDndHere": "Paste or drop files here",
  "we_notable_rich_text": "Rich Text",
  "notable_form_fields_telephone_helperText": "Automatically verifying phone number!",
  "notable_form_fields_telephone_helperText_icon_tip": "Supports validation of 11-digit mainland China mobile numbers, landline numbers with the format 01XX-7 digits, 01X-8 digits, international numbers (+CC) 11 digits, and (1X)YYYY-ZZZZ",
  "notable_notableFields_TelephoneInvalid_v2": "Incorrect phone format",
  "notable_notableFormView_attachment_SampleFile": "Sample File",
  "notable_notableFormView_attachment_UploadInProgressPercentage": "Uploading {{percentage}}%",
  "notable_notableFormView_attachment_UploadLimit": "Upload Limit Exceeded",
  "notable_notableFormView_attachment_upload_sample_file": "Add Sample File",
  "notable_notableFormView_attachment_SampleUploadFailed": "Upload Failed",
  "notable_notableFormView_attachment_UploadLimitTip": "Upload up to {{count}} sample files",
  "notable_notableFormView_attachment_UploadLimitTitle": "Attachment Limit Exceeded",
  "notable_notableFormView_attachment_upload_limit_cur_desc": "Currently uploaded {{cur}}/{{max}} items",
  "notable_notableFormView_attachment_upload_limit_max_desc": "Upload up to {{max}} items",
  "we_notable_retry": "Retry",
  "download_action": "Download",
  "notable_field_button_execute_not_trigger2": "Trigger condition not met",
  "notable_form_setting_partition_help_link": "Learn More",
  "we_notable_learn_more": "Learn more",
  "we_notable_guide_field_type_selector_address_guide_title": "Select province/city/district or street",
  "we_notable_guide_field_type_selector_unidirectional_link_guide_title": "Quickly link records from other data tables in the current table, and click to view the full row information of the record",
  "we_notable_guide_field_instructions_unidirectional_link_guide_title": "Quickly link records from other data tables in the current table, and click to view the full row information of the record",
  "we_notable_guide_field_type_selector_bidirectional_link_guide_title": "Two-way association is an upgraded version of one-way association, where the associated record will automatically link to the current record. Deleting a record in Table A will also delete the related record in Table B.",
  "we_notable_guide_field_instructions_bidirectional_link_guide_title": "Two-way association is an upgraded version of one-way association, where the associated record will automatically link to the current record. Deleting a record in Table A will also delete the related record in Table B.",
  "we_notable_guide_field_type_selector_auto_num_guide_title": "Generate a unique and auto-incrementing identifier for each record, with customizable numbering rules and formats",
  "we_notable_guide_field_type_selector_barcode_guide_title": "Automatically scan and recognize information in barcodes and QR codes on mobile devices",
  "we_notable_guide_field_type_selector_checkbox_guide_title": "Check or uncheck to quickly mark the status",
  "we_notable_guide_field_type_selector_createdTime_guide_title": "The creation time of the record is automatically generated by the system and cannot be modified",
  "we_notable_guide_field_type_selector_creator_guide_title": "The creator of the record is automatically generated by the system and cannot be modified",
  "we_notable_guide_field_type_selector_currency_guide_title": "Supports multiple national currencies",
  "we_notable_guide_field_type_selector_date_guide_title": "Enter a date (e.g., {{today}}) or select a date from the calendar, supporting display of date and time in multiple formats",
  "we_notable_guide_field_type_selector_flow_guide_title": "Personalized design business flow, real-time progress tracking, optimize the flow",
  "we_notable_guide_field_type_selector_department_guide_title": "Enter departments, multiple selections allowed",
  "we_notable_guide_field_type_selector_email_guide_title": "Enter the email address of the person",
  "we_notable_guide_field_instructions_filter_up_guide_title": "Quickly search and reference data based on specified conditions to easily meet filtering and statistical needs",
  "we_notable_guide_field_type_selector_filter_up_guide_title": "Quickly find and reference data based on specified conditions, easily meeting the needs for filtering and data statistics",
  "we_notable_guide_field_type_selector_formula_guide_title": "Calculate fields based on a wide range of function formulas",
  "we_notable_guide_field_instructions_formula_guide_title": "Learn how to use formulas",
  "we_notable_guide_field_type_selector_geolocation_guide_title": "Enter location information or use real-time mobile location",
  "we_notable_guide_field_type_selector_group_guide_title": "Fill in internal or external groups, multiple groups can be selected, quickly access communication groups with one click",
  "we_notable_guide_field_type_selector_idCard_guide_title": "Enter the person's ID number, format can be verified",
  "we_notable_guide_field_type_selector_link_guide_title": "Enter the webpage address, allowing custom text for the URL",
  "we_notable_guide_field_type_selector_lookup_guide_title": "Quickly reference data across tables and perform subsequent calculations",
  "we_notable_guide_field_type_selector_multiSelect_guide_title": "Select one or more predefined options from the list, or reference multi-select options from other data tables",
  "we_notable_guide_field_type_selector_number_guide_title": "Enter a number, supporting display in decimal, percentage, etc.",
  "we_notable_guide_field_type_selector_progress_guide_title": "Visually display project progress with a progress bar, supporting various formats and colors",
  "we_notable_guide_field_type_selector_starRating_guide_title": "Rate within a set range, supporting various graphics",
  "we_notable_guide_field_type_selector_select_guide_title": "Select an option from a predefined list, or reference a single-select option from other data tables",
  "we_notable_guide_field_type_selector_telephone_guide_title": "Enter a mobile phone number, format can be verified",
  "we_notable_guide_field_type_selector_text_guide_title": "Enter text",
  "we_notable_guide_field_type_selector_updatedTime_guide_title": "The last modified time of the record is automatically generated by the system and cannot be modified",
  "we_notable_guide_field_type_selector_updater_guide_title": "The last modifier of the record is automatically generated by the system and cannot be modified",
  "we_notable_guide_field_type_selector_person_guide_title": "Enter DingTalk contacts to add multiple people simultaneously or automatically sync information such as direct superiors, departments, and employee numbers",
  "we_notable_guide_sync_data_from_notable_guide_title": "Sync data from another multidimensional table, choose between automatic or manual sync",
  "we_notable_guide_field_type_selector_attachment_guide_title": "Add images, documents, or other files for viewing or download",
  "we_notable_guide_field_instructions_attachment_guide_title": "Support uploading images, videos, PDFs, etc., and click the file to preview after upload is complete",
  "we_notable_guide_field_type_selector_richText_guide_title": "Support rich text formats and mixed text and images",
  "we_notable_guide_field_type_selector_sign_guide_title": "Easily draw signature images",
  "we_notable_guide_field_type_selector_button_guide_title": "Clicking the button can trigger an automated operation",
  "notable_notableFields_object": "Object",
  "notable_search_the_record_want_to_assoc_alias": "Search related {{recordName}}",
  "notable_notableGridView_ExpandCell": "Expand cell",
  "notable_notableFormView_DefaultValue": "Default value",
  "notable_add_record_person": "The person who added this record ",
  "notable_add_record_datetime": "Date this record was added ",
  "we_notable_document": "Documentation"
};

export default resource