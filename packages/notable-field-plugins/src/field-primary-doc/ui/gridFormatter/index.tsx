import React from 'react';
import { useNotableCore } from '@ali/notable-core';
import { themeVars } from '@ali/notable-design';
import { observer } from 'mobx-react-lite';
import type { StringCell, GridFormatter } from '@ali/notable-common';
import { TextGridFormatter } from '../../../common';

type GridFormatterProps = Pick<React.ComponentProps<GridFormatter<'', StringCell>>, 'rowId' | 'sheetId'>;

type Props = React.ComponentProps<typeof TextGridFormatter> & GridFormatterProps;

export const FieldPrimaryDocGridFormatter = observer((props: Props) => {
  const { rowId, sheetId, style, ...rest } = props;
  const core = useNotableCore();
  const { primaryDocRowController: { data } } = core;
  const hasPrimaryDoc = () => {
    if (sheetId && rowId) {
      const info = data[sheetId]?.[rowId];
      return !!info;
    }

    return false;
  };
  const mergedStyle: React.CSSProperties | undefined = hasPrimaryDoc() ? {
    ...style,
    textDecorationLine: 'underline',
    textDecorationColor: themeVars.color.line_light,
    width: 'fit-content',
  } : style;

  return (
    <TextGridFormatter
      style={mergedStyle}
      {...rest}
    />
  );
});
