import React from 'react';
import { PluginIconProps } from '@ali/notable-common';
import { FieldPrimaryKey16 } from '@ali/we-icons-3';
import loadable from '@loadable/component';

const lazySyncIcons = () => import(
  /* webpackChunkName: "sync-text-icon" */
  './SyncIcon'
);

const FieldPrimaryKeySynced16 = loadable(() => lazySyncIcons().then((m) => m.default.FieldPrimaryKeySynced16));

export const Icon = (props: PluginIconProps) => {
  const { synced, colorful, size } = props;
  const style: {
    color?: string;
    width?: number;
    height?: number;
  } = {};
  if (colorful) {
    style.color = '#FA864F';
  }
  if (size) {
    style.width = size;
    style.height = size;
  }
  if (synced) {
    return <FieldPrimaryKeySynced16 style={style} {...props} />;
  }
  return <FieldPrimaryKey16 style={style} {...props} />;
};
