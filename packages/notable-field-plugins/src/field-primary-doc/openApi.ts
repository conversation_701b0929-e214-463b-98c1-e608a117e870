import { CellDataType, FieldTypes, isDateOperator, limitations, OpenApi, OpenApiFieldConfig } from '@ali/notable-common';
import { APIException } from '@ali/zongheng-common';

export function createTextOpenApiConfig<T extends FieldTypes>(
  apiCellValueValidator?: (value: string, ctx: Parameters<NonNullable<OpenApiFieldConfig['convertApiCellValueToCell']>>[2]) => void,
): OpenApiFieldConfig<T> {
  return {
    convertApiFieldOptionToConfig: (_, field) => {
      const { renderFieldConfig } = field?.config || {};
      return renderFieldConfig || { props: {} };
    },
    convertToCoreFilterOperator: (operator) => {
      switch (operator) {
        case 'equal':
        case 'notEqual':
        case 'contain':
        case 'notContain':
        case 'empty':
        case 'notEmpty':
          return OpenApi.commonConvertToCoreFilterOperator(operator);
        default:
          return null;
      }
    },
    convertToCoreFilterCondition: async (_, operator, value, ctx) => {
      if (isDateOperator(operator)) {
        throw new APIException(
          'invalid_argument',
          `operator '${operator}' is invalid condition for field '${ctx.fieldIdentifier}'`,
        );
      }

      const val = value[0];
      if (typeof val !== 'string') {
        throw new APIException(
          'invalid_argument',
          `the value '${JSON.stringify(val)}' is invalid condition for field '${ctx.fieldIdentifier}'`,
        );
      }

      return {
        symbol: operator,
        value: val,
      };
    },
    convertToApiCellValue: (cell) => {
      if (cell.dataType === CellDataType.STRING && typeof cell.value === 'string') {
        return cell.value;
      }
      return null;
    },
    convertApiCellValueToCell: async (value, _, ctx) => {
      if (typeof value !== 'string' && typeof value !== 'number') {
        throw new APIException('invalid_argument', `the value '${value}' is invalid for field '${ctx.fieldIdentifier}'`);
      }

      if (apiCellValueValidator) {
        apiCellValueValidator(String(value), ctx);
      } else if (value && String(value).length > limitations.cellPrimaryDocMaxLength) {
        throw new APIException(
          'invalid_argument',
          `the value is too long for field '${ctx.fieldIdentifier}'. The length limit is ${limitations.cellPrimaryDocMaxLength}`,
        );
      }

      return !value ? null : {
        dataType: CellDataType.STRING,
        value: String(value),
      };
    },
  };
}
