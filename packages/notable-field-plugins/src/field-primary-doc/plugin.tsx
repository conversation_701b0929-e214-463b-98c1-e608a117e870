import {
  <PERSON>rid<PERSON><PERSON>atter,
  FormEditor,
  MobileFormEditor,
  FieldPlugin,
  LoadableGridEditor,
} from '@ali/notable-common';
import { Api } from '@ali/notable-core';
import $i18n from '@ali/notable-i18n';
import { FieldPluginCreator } from '../common/types';
import { MobileCellHoc } from '../common/MobileCellHoc';
import { formEditor } from '../widgets';
import { TextGridFormatter } from '../common';
import {
  LazyDefaultTextEditorPC,
  LazyDefaultTextEditorMobile,
} from '../UIComponent/DefaultValueEditor';
import { LazyFormTextEditorMobileV2 } from '../field-text/ui/lazy';
import { LazyPrimaryDocEditor } from './ui/lazy';
import { createCellValueHooks } from './cellValueHooks';
import { FieldPrimaryDocGridFormatter } from './ui/gridFormatter';
import { PrimaryDocFieldHeadlessPlugin } from './headless';
import { Icon } from './ui/Icon';

export const createPrimaryDocFieldPlugin: FieldPluginCreator<'primaryDoc'> = (_: Api) => new PluginPC();
export const createPrimaryDocFieldPluginMobile: FieldPluginCreator<'primaryDoc'> = (_: Api) =>
  new PluginMobile();

class PluginBase extends PrimaryDocFieldHeadlessPlugin {
  icon = Icon;
  gridEditor = LazyPrimaryDocEditor as LoadableGridEditor;
  gridEditorConfig = {
    needDetectPosition: true,
    setCellHeightAutoWhenSelected: true,
  };
  cellValueHooks = createCellValueHooks();
}

class PluginPC extends PluginBase implements FieldPlugin<'primaryDoc'> {
  formEditor = formEditor.formTextarea as FormEditor;
  gridFormatter = FieldPrimaryDocGridFormatter as GridFormatter;
  searchAlias = ['primaryDoc'];
  defaultValueEditor = LazyDefaultTextEditorPC;
  tooltipConfig = {
    selector: {
      get title() {
        return $i18n.t('we_notable_document');
      },
      get description() {
        return $i18n.t('we_notable_guide_field_type_selector_text_guide_title');
      },
      get image() {
        return 'https://img.alicdn.com/imgextra/i4/O1CN01S1bZX61Qkv6JJe6RY_!!6000000002015-2-tps-1440-800.png';
      },
    },
  };
}

class PluginMobile extends PluginBase implements FieldPlugin<'primaryDoc'> {
  formEditorMobile = LazyFormTextEditorMobileV2 as MobileFormEditor;
  gridFormatter = MobileCellHoc(TextGridFormatter as GridFormatter);
  defaultValueEditorMobile = LazyDefaultTextEditorMobile;
  searchAlias = ['primaryDoc'];
  tooltipConfig = {
    selector: {
      get title() {
        return $i18n.t('notable_notableFields_Text');
      },
      get description() {
        return $i18n.t('we_notable_guide_field_type_selector_text_guide_title');
      },
      get image() {
        return 'https://img.alicdn.com/imgextra/i4/O1CN01S1bZX61Qkv6JJe6RY_!!6000000002015-2-tps-1440-800.png';
      },
    },
  };
}
