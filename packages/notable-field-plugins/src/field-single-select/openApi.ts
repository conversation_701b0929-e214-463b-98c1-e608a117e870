import {
  OpenApiFieldConfig, OpenApi, isDateOperator,
  FilterOperator, ObjectCellValue,
  CellDataType, serializeSingleSelect,
  limitations, findEnumId,
  serializeMultiSelect,
  SelectConfig,
} from '@ali/notable-common';
import { APIException, convertSelectOptions } from '@ali/notable-core';
import { forEach } from 'lodash-es';

export const selectFieldOpenApiConfig: OpenApiFieldConfig<'select' | 'multiSelect'> = {
  getApiFieldType: (field) => {
    if (field.type === 'select') return 'singleSelect';
    return 'multipleSelect';
  },
  convertApiFieldOptionToConfig: (fieldProperty, field) => {
    const { renderFieldConfig } = field?.config || {};
    const { props } = renderFieldConfig || {};
    const { choices } = fieldProperty;
    if (!choices) return { ...renderFieldConfig, props: { enums: [], ...props } };
    const newProps = convertSelectOptions(choices, renderFieldConfig);
    return { ...renderFieldConfig, props: { ...props, ...newProps } as SelectConfig };
  },
  convertConfigToApiFieldOption: (field) => {
    const { props } = field.config.renderFieldConfig || {};
    return {
      choices: (props?.enums || []).map((val) => ({
        id: val.id,
        name: val.value,
      })),
    };
  },
  convertToCoreFilterOperator: (operator) => {
    switch (operator) {
      case 'contain':
        return 'ANY_OF';
      case 'notContain':
        return 'NONE_OF';
      case 'equal':
      case 'notEqual':
      case 'empty':
      case 'notEmpty':
        return OpenApi.commonConvertToCoreFilterOperator(operator);
      default:
        return null;
    }
  },
  convertToCoreFilterCondition: async (field, operator, value, ctx) => {
    if (isDateOperator(operator)) {
      throw new APIException(
        'invalid_argument',
        `operator '${operator}' is invalid condition for field '${ctx.fieldIdentifier}'`,
      );
    }

    const optionIds = new Set<string>();
    const optionEnums = field.config.renderFieldConfig?.props?.enums || [];

    for (const val of value) {
      const option = optionEnums.find((e) => e.id === val || e.value === val);
      if (typeof val !== 'string' || !option) {
        throw new APIException(
          'invalid_argument',
          `the value item '${JSON.stringify(val)}' is invalid condition for field '${ctx.fieldIdentifier}'`,
        );
      }

      optionIds.add(option.id);
    }

    const isListOperator = (['ANY_OF', 'NONE_OF'] as FilterOperator[]).includes(operator);

    return {
      symbol: operator,
      value: (field.type !== 'select' || isListOperator) ? Array.from(optionIds) : Array.from(optionIds)[0],
    };
  },
  convertToApiCellValue: (cell, field, ctx) => {
    const configProps = field?.config.renderFieldConfig?.props;
    if (field.type === 'select') {
      const option = configProps?.enums.find((val) => val.id === (cell.value as ObjectCellValue)?.data);
      if (!option) {
        ctx.logger?.warn(`invalid value for 'select' field '${field?.id}' in sheet '${ctx.sheetId}' and record '${ctx.recordId}'`);
        return null;
      }

      return {
        id: option.id,
        name: option.value,
      } as OpenApi.SingleSelectCellValue;
    } else if (field.type === 'multiSelect') {
      try {
        const selectedIds = JSON.parse((cell.value as ObjectCellValue).data) as string[];
        const options = configProps?.enums.filter((val) => selectedIds.includes(val.id));
        if (options?.length !== selectedIds.length) {
          ctx.logger?.warn(`invalid value for 'multiSelect' field ${field?.id} in sheet ${ctx.sheetId} and record '${ctx.recordId}'`);
          return null;
        }

        return options.map((option) => ({
          id: option.id,
          name: option.value,
        })) as OpenApi.MultiSelectCellValue;
      } catch (e) {
        ctx.logger?.warn(`invalid value for 'multiSelect' field ${field?.id} in sheet ${ctx.sheetId} and record '${ctx.recordId}'`);
        return null;
      }
    }
    return null;
  },
  convertApiCellValueToCell: async (value, field, ctx) => {
    if (field.type === 'select') {
      if (typeof value !== 'string') {
        throw new APIException('invalid_argument', `the value '${value}' is invalid for field '${ctx.fieldIdentifier}'`);
      }
      const enumId = findEnumId(value, field.config.renderFieldConfig?.props?.enums);
      if (!enumId && value === '') {
        return null;
      }
      if (!enumId) {
        throw new APIException('invalid_argument', `the option '${value}' is invalid for field '${ctx.fieldIdentifier}'`);
      }
      return {
        dataType: CellDataType.OBJECT,
        value: serializeSingleSelect(enumId),
      };
    } else if (field.type === 'multiSelect') {
      if (value !== null && !Array.isArray(value)) {
        throw new APIException('invalid_argument', `the value '${value}' is invalid for field '${ctx.fieldIdentifier}'`);
      }
      if (!value?.length) {
        return null;
      }
      const enumIds: Set<string> = new Set();
      forEach(value, (val) => {
        if (typeof val !== 'string') {
          throw new APIException('invalid_argument', `the value '${value}' is invalid for field '${ctx.fieldIdentifier}'`);
        }
        const enumId = findEnumId(val, field.config.renderFieldConfig?.props?.enums);
        if (!enumId) throw new APIException('invalid_argument', `the option '${value}' is invalid for field '${ctx.fieldIdentifier}'`);
        enumIds.add(enumId);
      });
      if (enumIds.size > limitations.cellSelectMaxCount) {
        throw new APIException(
          'invalid_argument',
          `the value is too long for field '${ctx.fieldIdentifier}'. The length limit is ${limitations.cellSelectMaxCount}`,
        );
      }

      return {
        dataType: CellDataType.OBJECT,
        value: serializeMultiSelect(Array.from(enumIds), field.config.renderFieldConfig?.props),
      };
    }
    return null;
  },
};
