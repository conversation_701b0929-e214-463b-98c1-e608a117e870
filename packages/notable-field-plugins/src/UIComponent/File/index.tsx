import React, { memo, useState } from 'react';
import { useMemoizedFn } from 'ahooks';
import { EditorApi, FileValueType, ObjectCellValue, serializeFile } from '@ali/notable-common';
import { Upload, useAttachmentUpload } from '@ali/notable-components';
import { useNotableCore } from '@ali/notable-core';

interface IFileProps {
  fileList: FileValueType[];
  onChange: (value: ObjectCellValue | null) => void;
  onPasteLink?: (e: ClipboardEvent) => Promise<void>;
  selectDentry?: () => Promise<FileValueType[]>;
  className?: string;
  // 是否禁用 paste 文件 默认 false
  disablePaste?: boolean;
  readOnly?: boolean;
  mode?: 'default' | 'form';
  userId: string;
  onlyCamera?: boolean;
  uploadLimit?: number;
}

const File = React.forwardRef<EditorApi, IFileProps>(
  ({
    className,
    fileList,
    onChange,
    onPasteLink,
    selectDentry,
    disablePaste = false,
    readOnly = false,
    mode = 'default',
    onlyCamera = false,
    uploadLimit,
  }, ref) => {
    const notableCore = useNotableCore();
    const { useAttachmentDingRights, settings: { enableSelectDentry } } = notableCore.fileController;
    const [uploading, setUploading] = useState(false);
    const { toUploadFiles } = useAttachmentUpload();

    const { renderRightsIcon } = useAttachmentDingRights();

    const handleChange = useMemoizedFn((files: FileValueType[]) => {
      onChange(serializeFile(files));
    });

    const handleUpload = useMemoizedFn(async (list: FileList) => {
      if (uploading) {
        return;
      }
      setUploading(true);
      toUploadFiles({
        list,
        fileList,
        onChange,
        onFinish: () => {
          setUploading(false);
        },
      });
    });

    return (
      <Upload
        className={className}
        fileList={fileList}
        handleUpload={handleUpload}
        onChange={handleChange}
        onPasteLink={onPasteLink}
        selectDentry={enableSelectDentry ? selectDentry : undefined}
        disablePaste={disablePaste}
        readOnly={readOnly}
        mode={mode}
        ref={ref}
        onlyCamera={onlyCamera}
        uploadLimit={uploadLimit}
        rightsIcon={renderRightsIcon()}
      />
    );
  },
);

export default memo(File);
