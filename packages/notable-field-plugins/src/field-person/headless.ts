import $i18n from '@ali/notable-i18n';
import { isNil, uniq } from 'lodash-es';
import {
  FillByRuleParams,
  AutomationFieldConfig,
  CellDataType,
  deserializePersonCell,
  FieldType,
  FilterOperator,
  HeadlessFieldPlugin,
  IMember,
  limitations,
  ObjectCellValue,
  RowCell,
  serializePersonCell,
  OpenApi,
  OpenApiFieldConfig,
  isDateOperator,
  GetDefaultValueContext,
  FieldDTO,
} from '@ali/notable-common';
import { Api, APIException, NotableCore } from '@ali/notable-core';
import { FieldHeadlessPluginCreator } from '../common/types';
import { TextSortOperator } from '../common';
import { getPersonOutputSchema, getPersonOutputValue } from '../common/automation';
import { LIST_FILTER_OPERATORS } from '../common/filter';
import { toString, toCellValue, createField, getDefaultFieldConfig } from './converter';
import { getNormalizedDefaultValue } from './utils';

export const createPersonFieldHeadlessPlugin: FieldHeadlessPluginCreator<'person'> = (_: Api) =>
  new PersonFieldHeadlessPlugin();

export class PersonFieldHeadlessPlugin implements Partial<HeadlessFieldPlugin<'person'>> {
  type = 'person' as const;
  dataType = CellDataType.OBJECT;
  fieldType = FieldType.BASIC;
  canToPrimary = true;
  get typeName() {
    return $i18n.t('we_notable_metion');
  }
  automation = AutomationConfig;
  openApi = OpenApiConfig as OpenApiFieldConfig<'person'>;
  getFilterOperators = (): FilterOperator[] => LIST_FILTER_OPERATORS;
  getSortOperators = () => TextSortOperator;

  toString = toString;
  toCellValue = toCellValue;
  createField = createField;
  getDefaultFieldConfig = getDefaultFieldConfig;
  getDefaultValue = (context: GetDefaultValueContext) => {
    const fieldConfig = context.targetField?.config as FieldDTO<'person'>['config'] | undefined;
    if (fieldConfig?.decorator) return;
    const config = fieldConfig?.renderFieldConfig;
    const v = config?.props?.defaultValue;
    if (!v) return;
    const members = deserializePersonCell(v);
    const { globalDataController: { currentUser } } = context.core as NotableCore;
    return serializePersonCell(getNormalizedDefaultValue(members, currentUser));
  };

  sync = {
    consumeRefDataValue(values: Array<RowCell['value']>) {
      try {
        const mentions = deserializePersonCell(values as ObjectCellValue[]);
        if (mentions.length === 0) return null;
        return serializePersonCell(mentions);
      } catch {
        return null;
      }
    },
  };

  autoFill = {
    fillByRule: (({ value, filter, userMap }) => {
      if (value) return value;
      const eqFilter = filter?.find((f) => f.symbol === 'EQ');
      if (!eqFilter || !Array.isArray(eqFilter.value)) return null;
      if (!userMap) return null;
      const mentions = eqFilter.value.reduce<IMember[]>((acc, key) => {
        if (typeof key !== 'string') return acc;
        const user = userMap[key];
        if (!user) return acc;
        acc.push({
          key,
          name: user.nick,
          avatar: user.avatarUrl,
        });
        return acc;
      }, []);
      if (mentions.length === 0) return null;
      return serializePersonCell(mentions);
    }) satisfies FillByRuleParams,
  };
}

const AutomationConfig: AutomationFieldConfig<'person'> = {
  getOutputSchema: getPersonOutputSchema,
  getOutputValue: getPersonOutputValue,
  fromInputValue: async (inputValue, _, ctx) => {
    const value = inputValue.array?.user || inputValue.array?.string;
    if (isNil(value)) {
      return null;
    }
    const ids = Array.from(new Set(value));
    if (ids.length > limitations.cellPersonMaxCount) {
      throw new Error(
        `The input value is too long for user field. The length limit is ${limitations.cellPersonMaxCount}`,
      );
    }
    const members = (
      await Promise.all(
        uniq(ids).map(async (id) => {
          try {
            const userInfos = await ctx.hooks.getUserInfoFromStaffIdOrUidCipher(id);
            return userInfos;
          } catch (e) {
            return null;
          }
        }),
      )
    ).filter((v) => !!v) as IMember[];
    return {
      dataType: CellDataType.OBJECT,
      value: serializePersonCell(members),
    };
  },
  getFilterOperators: () => LIST_FILTER_OPERATORS,
  customConfig: {
    getConditionBranchVarValue: (jsonPathVar) => {
      return { array: { user: [jsonPathVar] } };
    },
    fromConditionCompareValue: (inputValue) => {
      const value = inputValue.user || inputValue.array?.user;
      if (isNil(value) || (Array.isArray(value) && !value.filter((v) => !isNil(v)).length)) {
        return null;
      }
      return {
        dataType: CellDataType.OBJECT,
        value: serializePersonCell((Array.isArray(value) ? value : [value]).filter((v) => !isNil(v)).map((v) => ({ key: v, name: v }))),
      };
    },
  },
};

const OpenApiConfig: OpenApiFieldConfig<'person', NotableCore> = {
  getApiFieldType: () => 'user',
  convertApiFieldOptionToConfig: (fieldProperty, field) => {
    const { multiple } = fieldProperty;
    if (typeof multiple !== 'boolean' && multiple !== undefined) {
      throw new APIException('invalid_argument', `user config multiple: ${multiple} is invalid`);
    }
    const { renderFieldConfig } = field?.config || {};
    const { props } = renderFieldConfig || {};
    const allowMultiSelect = fieldProperty.multiple ?? props?.allowMultiSelect ?? true;
    return { ...renderFieldConfig, props: { ...props, allowMultiSelect } };
  },
  convertConfigToApiFieldOption: (field) => {
    const { allowMultiSelect = true } = field.config.renderFieldConfig?.props || {};
    return { multiple: allowMultiSelect };
  },
  convertToCoreFilterOperator: (operator) => {
    switch (operator) {
      case 'equal':
      case 'notEqual':
      case 'empty':
      case 'notEmpty':
        return OpenApi.commonConvertToCoreFilterOperator(operator);
      case 'contain':
        return 'ANY_OF';
      case 'notContain':
        return 'NONE_OF';
      default:
        return null;
    }
  },
  convertToCoreFilterCondition: async (_, operator, value, ctx) => {
    if (isDateOperator(operator)) {
      throw new APIException(
        'invalid_argument',
        `operator '${operator}' is invalid condition for field '${ctx.fieldIdentifier}'`,
      );
    }

    const uids = new Set<string>();
    if (!OpenApi.isUsersConditionValue(value)) {
      throw new APIException(
        'invalid_argument',
        `the value '${JSON.stringify(value)}' is invalid condition for field '${ctx.fieldIdentifier}'`,
      );
    }
    for (const val of value) {
      const { unionId, uid } = val;
      if (uid) {
        uids.add(uid);
      } else if (unionId) {
        const res = await ctx.core.addonController?.serviceHooks.getUidByUnionId?.(unionId);
        if (!res?.success) {
          throw new APIException(
            'not_found',
            `the value '${JSON.stringify(value)}' is invalid condition for field '${ctx.fieldIdentifier}'`,
          );
        }

        uids.add(res.response);
      } else {
        throw new APIException(
          'invalid_argument',
          `the value item '${JSON.stringify(val)}' is invalid condition for field '${ctx.fieldIdentifier}'`,
        );
      }
    }

    return {
      symbol: operator,
      value: Array.from(uids),
    };
  },
  convertToApiCellValue: (cell, _, ctx) => {
    if (cell.dataType === CellDataType.OBJECT) {
      const members = deserializePersonCell(cell.value);
      const uids = members.map(({ key }) => key);
      const userIds = uids.map((uid) => {
        const user = ctx.core.addonController?.rowsProcessor?.getUserId?.(uid);
        return user;
      }).filter((user) => !!user) as OpenApi.UsersCellValue;
      return userIds || null;
    }
    return null;
  },
  convertApiCellValueToCell: async (value, field, ctx) => {
    if (!value) return undefined;
    const { core, fieldIdentifier, options } = ctx;
    if (!Array.isArray(value)) {
      throw new APIException('invalid_argument', `the value '${JSON.stringify(value)}' is invalid for field '${fieldIdentifier}'`);
    }
    if (!value.length) {
      return null;
    }
    const multiple = !(field.config.renderFieldConfig?.props?.allowMultiSelect === false);

    if (options?.useRawPersonValue) {
      const v = multiple ? value : [value[0]] as OpenApi.PrivateUsersValue;

      if (v.length > limitations.cellPersonMaxCount) {
        throw new APIException(
          'invalid_argument',
          `the value is too many for field '${fieldIdentifier}'. The count limit is ${limitations.cellPersonMaxCount}`,
        );
      }

      return {
        dataType: CellDataType.OBJECT,
        value: serializePersonCell(v.map((member) => {
          if (typeof member.uid !== 'string'
            || typeof member.name !== 'string'
            || (!isNil(member.avatarUrl) && typeof member.avatarUrl !== 'string')
            || (!isNil(member.realName) && typeof member.realName !== 'string')) {
            throw new APIException('invalid_argument', `the value '${JSON.stringify(value)}' is invalid person format for field '${fieldIdentifier}'`);
          }
          return {
            key: member.uid,
            name: member.name,
            avatar: member.avatarUrl,
            realName: member.realName,
          };
        })),
      };
    }

    if (!OpenApi.isUsersCellValue(value)) {
      throw new APIException('invalid_argument', `the value '${JSON.stringify(value)}' is invalid for field '${fieldIdentifier}'`);
    }
    if (!core.addonController?.serviceHooks.getUsersInfo) throw new APIException('invalid_argument', 'unsupported field type: person');
    // 这里输入的人员不会重复，所以并行查询
    const ids = await Promise.all(((multiple ? value : [value[0]])).map(async (item) => {
      if (typeof item !== 'object') {
        throw new APIException('invalid_argument', `the value item '${JSON.stringify(item)}' is invalid for field '${fieldIdentifier}'`);
      }
      const { uid, unionId } = item;
      if (uid) {
        return uid;
      }
      if (unionId) {
        // 通过非open api的途径写入unionId时，可能无法获取调用方信息，此时不支持通过传入unionId写入人员字段
        if (!core.addonController?.serviceHooks.getUidByUnionId) {
          throw new APIException('not_found', 'unable to identify API caller');
        }
        const result = await core.addonController.serviceHooks.getUidByUnionId(unionId);
        if (result?.success) {
          return result.response;
        } else {
          throw new APIException('not_found', `the value '${JSON.stringify(value)}' is invalid for field '${fieldIdentifier}'`);
        }
      }
    }));
    const result = await core.addonController.serviceHooks.getUsersInfo(uniq(ids.filter((id) => !!id)) as string[]);
    if (result?.success && result.response.length) {
      if (result.response.length > limitations.cellPersonMaxCount) {
        throw new APIException(
          'invalid_argument',
          `the value is too many for field '${fieldIdentifier}'. The count limit is ${limitations.cellPersonMaxCount}`,
        );
      }

      return {
        dataType: CellDataType.OBJECT,
        value: serializePersonCell(result.response.map((member) => {
          return {
            key: String(member.uid), // 兼容传入的uid是Number的case
            name: member.nick,
            avatar: member.avatarUrl,
          };
        })),
      };
    }
  },
};
