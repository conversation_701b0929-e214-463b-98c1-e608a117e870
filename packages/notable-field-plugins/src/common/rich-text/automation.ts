import {
  createMarkdownToAst,
  createAstToJsonML,
  createJsonMLToAst,
  createAstToMarkdown,
  MarkdownToAst,
  AstToJsonML,
  JsonMLToAst,
  AstToMarkdown,
} from '@ali/we-notable-cangjie-serializer';
import { JsonML, QueryImageOssSignUrl } from './type';
import { markdownNormalizer } from './markdownNormalizer';

const { astExtensions: ast } = MarkdownToAst;
const { plugins: AstToJsonMLPlugins } = AstToJsonML;
const { plugins: JsonMLToAstPlugins } = JsonMLToAst;
const { joinRule } = AstToMarkdown;

/**
 * 自动化场景下markdown语法转为ASL富文本
 * @param markdown 传入富文本
 *
 * 语法解析规则：
 * - 过滤类html段落，识别为文本段落
 * - 过滤未特判的html标签，过滤<>内的内容，保留两个尖括号之间的文本
 * - 调整换行规则，一个\n识别为一次换行，支持段落间插入多个空行
 * - 图片语法下转为文本「[图片]」
 * - 解析notableAsl语法 e.g.<notableAsl>...</notableAsl>
 * - 其余语法对标标准markdown语法
 */
export const convertAutomationMarkdownToAsl = async (markdown: string): Promise<JsonML | null> => {
  const markdownToAst = createMarkdownToAst(['notableAsl', 'strike', 'skipHtml'], {
    mdastExtensions: [ast.nonStandard.skipImage, ast.nonStandard.lineBlank],
  });
  const astToAsl = createAstToJsonML({
    notableAsl: AstToJsonMLPlugins.createNotableAslConverter(),
  });

  const astConstructor = markdownToAst(markdown);
  const result = await astToAsl(astConstructor);
  return result;
};


/**
 * 自动化场景下ASL富文本转为markdown语法
 * @param _asl ASL富文本
 * @param queryImageOssSignUrl 注入业务逻辑，根据resourceId查询富文本图片加签地址
 *
 * 解析规则：
 * - 根据是否注入queryImageOssSignUrl决定是否返回图片加签地址，没有注入的时候图片节点返回文本「[图片]」
 * - 一次换行解析为一个\n，多个空行返回多个\n
 */
export const convertAslToAutomationMarkdown = async (_asl: string, queryImageOssSignUrl?: QueryImageOssSignUrl): Promise<string | null> => {
  try {
    const asl = JSON.parse(_asl);
    if (!asl) return null;
    const jsonMLToAst = createJsonMLToAst({
      img: queryImageOssSignUrl ? JsonMLToAstPlugins.createImageNode(queryImageOssSignUrl) : JsonMLToAstPlugins.createSkipImage(),
      p: JsonMLToAstPlugins.createEmptyParagraphNode(),
    });
    const astToMarkdown = createAstToMarkdown(['notableAsl', 'strike'], {
      join: [joinRule.joinWithSingleNewLine],
      normalization: markdownNormalizer,
    });
    const astConstructor = await jsonMLToAst(asl);
    if (!astConstructor) return null;
    const result = astToMarkdown(astConstructor);
    return result;
  } catch (e) {
    return null;
  }
};
