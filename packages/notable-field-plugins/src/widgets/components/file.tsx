import React, { useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import styled from 'styled-components';
import { useMemoizedFn } from 'ahooks';
import classnames from 'classnames';
import { Message } from '@ali/we-design-next';
import $i18n from '@ali/notable-i18n';
import {
  CellDataType,
  FieldValueType,
  ObjectCellValue,
  deserializeFile,
  isObjectCellValue,
  serializeFile,
  isSubmitPage,
  isPublicSubmitPage,
} from '@ali/notable-common';
import { CCPSelection, useNotableCore } from '@ali/notable-core';
import { getMergedFileValueList, sliceOnlineFileList } from '@ali/notable-components';
import File from '../../UIComponent/File';
import { SampleFile } from '../../UIComponent/File/SampleFile/SampleFile';
import { FormItemPropsIF } from '../../UIComponent/types';
import { ForbiddenAttachment } from '../../common/ForbiddenAttachment';
import { getFormAttachmentUploadLimitStatus, FormAttachmentUploadLimitTip } from '../../common';

const StyledAttachmentWrapper = styled.div`
  min-width: 126px;
  width: 100%;
  height: 100%;
`;

export default observer(
  (
    props: FormItemPropsIF<'file', FieldValueType['file']> & {
      userId: string;
    },
  ) => {
    const notableCore = useNotableCore();
    const { data, sheetId, viewId, rowId, scene, onValueCommit } = props;
    const filesData = data?.value;
    const fileList = useMemo(() => deserializeFile(filesData), [filesData]);
    const onChange = useMemoizedFn((value: ObjectCellValue | null) => {
      onValueCommit?.(data.id, {
        ...data,
        dataType: CellDataType.OBJECT,
        value,
      });
    });
    const config = data?.config?.renderFieldConfig?.props;
    const onlyCamera = !!config?.onlyCamera;
    const sampleFiles = config?.sampleFiles || [];
    const uploadMaxLimit = config?.limit?.max;

    let remainingSlots: number | null = null;
    if (uploadMaxLimit != null && typeof uploadMaxLimit === 'number') {
      remainingSlots = Math.max(uploadMaxLimit - fileList.length, 0);
    }

    const onPasteLink = useMemoizedFn(async (e: ClipboardEvent) => {
      if (!onValueCommit || onlyCamera) {
        Message.error($i18n.t('notable_notableComponents_ThePasteContentCannotBe'));
        return;
      }
      if (!sheetId || !viewId) return;
      const selectedArea = {
        columns: [data.id],
        records: [rowId],
      };
      const ccpSelection: CCPSelection = {
        viewId,
        sheetId,
        columns: selectedArea.columns,
        records: selectedArea.records,
        selectedArea,
      };
      const value = await notableCore.ccpController.transformSinglePastedValue(ccpSelection, e);
      if (isObjectCellValue(value)) {
        const files = deserializeFile(value);
        const merged = getMergedFileValueList(fileList, sliceOnlineFileList(files, remainingSlots));
        onChange(serializeFile(merged));
      } else {
        Message.error($i18n.t('notable_notableComponents_ThePasteContentCannotBe'));
      }
    });

    const { limitText, reachLimit } = useMemo(() => {
      if (isSubmitPage(scene) || isPublicSubmitPage(scene)) {
        return getFormAttachmentUploadLimitStatus(fileList, config);
      }
      return {
        limitText: '',
        reachMaxLimit: false,
        reachLimit: false,
      };
    }, [scene, config, fileList]);

    if (!filesData && onlyCamera) {
      return (
        <StyledAttachmentWrapper>
          {limitText && (
            <FormAttachmentUploadLimitTip className={classnames({ warning: reachLimit })}>
              {limitText}
            </FormAttachmentUploadLimitTip>
          )}
          <ForbiddenAttachment isFormMode />
          {(isSubmitPage(scene) || isPublicSubmitPage(scene)) &&
            Array.isArray(sampleFiles) &&
            sampleFiles.length > 0 && (
              <div style={{ marginTop: 12 }}>
                <SampleFile files={sampleFiles} />
              </div>
          )}
        </StyledAttachmentWrapper>
      );
    }

    return (
      <StyledAttachmentWrapper>
        {limitText && <FormAttachmentUploadLimitTip className={classnames({ warning: reachLimit })}>{limitText}</FormAttachmentUploadLimitTip>}
        <File
          {...props}
          onPasteLink={onPasteLink}
          selectDentry={notableCore.services.selectDentry}
          fileList={fileList}
          onChange={onChange}
          mode={'form'}
          onlyCamera={onlyCamera}
          uploadLimit={uploadMaxLimit}
        />
        {(isSubmitPage(scene) || isPublicSubmitPage(scene)) &&
          Array.isArray(sampleFiles) &&
        sampleFiles.length > 0 && (
          <div style={{ marginTop: 12 }}>
            <SampleFile files={sampleFiles} />
          </div>
        )}
      </StyledAttachmentWrapper>
    );
  },
);
