import { get, pick, set, toNumber } from 'lodash-es';
import $i18n from '@ali/notable-i18n';
import {
  CellDataType,
  FieldType,
  FieldValidationContext,
  logger,
  FieldDTO,
  FormulaFieldConfig,
  HeadlessFieldPlugin,
  AutomationFieldConfig,
  SheetId,
  FieldId,
  FieldTypes,
  GenericFieldPlugin,
  getFormulaLookupResultType,
  isFlagOn,
  AutomationFlag,
  isCalcField,
  ResultFieldType,
  FormulaValue,
} from '@ali/notable-common';
import { detectFormulaResultInfo, FieldAddr, fieldKeyToName, formulaToRpn, makeGetFieldIdByName, Model } from '@ali/notable-model';
import { Api } from '@ali/notable-core';
import { FieldHeadlessPluginCreator } from '../common/types';
import {
  getFieldFxInfo,
  fromFxFieldConditionCompareValue,
  tryCalcFieldConvertToDate,
  getCalcFieldFilterOperators,
  getCalcFieldSortOperators,
  getFxFieldConditionBranchVarValue,
  getFxFieldOutputSchema,
} from './utils';
import formulaSync from './formulaSync';
import { toString, toCellValue, createField, getDefaultFieldConfig } from './converter';

export const createFormulaFieldHeadlessPlugin: FieldHeadlessPluginCreator<'formula'> = (_: Api) =>
  new FormulaFieldHeadlessPlugin();

export class FormulaFieldHeadlessPlugin implements Partial<HeadlessFieldPlugin<'formula'>> {
  type = 'formula' as const;
  dataType = CellDataType.OBJECT;
  fieldType = FieldType.BASIC;
  get typeName() {
    return $i18n.t('notable_notableFields_Formula');
  }
  canToPrimary = true;
  disableCreate = false;
  isCellReadonly = true;

  toString = toString;
  toCellValue = toCellValue;
  createField = createField;
  getDefaultFieldConfig = getDefaultFieldConfig;
  getCopyFieldConfig = (field: FieldDTO<'formula'>, sheetId: string, model: unknown): FieldDTO<'formula'>['config'] => {
    const m = model as Model;
    const configProps = field.config.renderFieldConfig?.props;
    if (!configProps) return field.config;
    // 将其还原为字符串，然后后续创建 field 时会校准
    const formula: string = fieldKeyToName(
      configProps?.rawText ?? '',
      sheetId,
      (id: string, sId: string) => m.getSheet(sId)?.getField(id),
      (sId: string) => m.getSheet(sId),
    ).result;

    return {
      ...field.config,
      renderFieldConfig: {
        ...field.config?.renderFieldConfig,
        props: {
          ...configProps,
          formula,
        },
      },
    };
  };

  sync = formulaSync;
  automation = AutomationConfig;

  validateAndCompleteConfig(
    config: FieldDTO<'formula'>['config'],
    { currentSheet, model }: FieldValidationContext,
  ) {
    logger.log('action_formula_create_field');

    let fxProps: FormulaFieldConfig = get(config, ['renderFieldConfig', 'props']) || {};
    if (!fxProps.formula) {
      fxProps = {
        ...fxProps,
        formula: '',
      };
    } else if (typeof fxProps.formula === 'string' && currentSheet) {
      const parsed = formulaToRpn(fxProps.formula, makeGetFieldIdByName(model as Model, currentSheet.id));
      if (parsed.error) {
        throw { fieldError: $i18n.t('notable_notableFieldPlugins_ErrorInFormula') };
      }

      // 解析其 resultFieldType 相关数据
      const resultFieldInfo = detectResuleFieldInfo(parsed.formula, currentSheet.id, model as Model);

      fxProps = {
        ...fxProps,
        ...parsed,
        ...resultFieldInfo,
      };

      if (parsed.formula?.names?.find((n) => n.sheetId)) {
        logger.log('create_column_ref_formula');
      }
    }
    set(config, ['renderFieldConfig', 'props'], fxProps);

    logger.log('action_formula_create_field_success');
    return config;
  }

  getFilterOperators = getCalcFieldFilterOperators;
  getSortOperators = getCalcFieldSortOperators;

  autoFill = {
    canAutoFill: () => false,
  };
}

const AutomationConfig: AutomationFieldConfig<'formula'> = {
  getOutputSchema: getFxFieldOutputSchema,
  getFilterOperators: (
    sheetId: SheetId,
    field: FieldDTO,
    getField: (sheetId: SheetId, fieldId: FieldId) => FieldDTO | null,
    getPlugin: (type: FieldTypes) => GenericFieldPlugin | null,
  ) => getCalcFieldFilterOperators(sheetId, field, getField, getPlugin, null),
  getOutputValue: async (field, cellValue, ctx) => {
    const { renderFieldConfig } = field.config;
    const result = toString({ renderFieldConfig, value: cellValue, rowCreatedTime: null });
    // 如果输出值为number，且公式预测值为number类型或配置了数字格式，则优先将输出值转为数字处理，如果无法转为数字则仅返回文本格式结果。
    const resultType = getFormulaLookupResultType(field);
    if (resultType === 'number') {
      const { flags } = ctx;
      const enableNumberDrillDown = isFlagOn<AutomationFlag>(flags, AutomationFlag.NUMBER_DRILL_DOWN);
      let value: number | undefined;
      if (typeof cellValue === 'string' || typeof cellValue === 'number') {
        value = toNumber(cellValue);
      }

      if (!Number.isFinite(value) || Number.isNaN(value)) {
        if (enableNumberDrillDown) {
          return { text: result };
        }

        // resultType的推断并不可靠，其可能受数字格式等影响导致误判
        // 未启用下钻时，仍然和第一版一样返回result，否则可能导致历史流程不返回结果
        return result;
      }

      if (enableNumberDrillDown) {
        return {
          text: result,
          number: String(value),
        };
      } else {
        return String(value);
      }
    }
    return result;
  },
  customConfig: {
    getFxInfo: getFieldFxInfo,
    getConditionBranchVarValue: getFxFieldConditionBranchVarValue,
    fromConditionCompareValue: fromFxFieldConditionCompareValue,
    tryConvertToDate: tryCalcFieldConvertToDate,
  },
};


// utils

const detectResuleFieldInfo = (
  formula: undefined | FormulaValue['formula'],
  baseSheetId: string,
  model: Model,
): Pick<FormulaFieldConfig, 'resultFieldType' | 'resultFieldAddr'> => {
  let resultFieldType: undefined | FieldTypes;
  let resultFieldAddr: undefined | FieldAddr;

  if (formula && typeof formula === 'object') {
    const result = detectFormulaResultInfo(formula, baseSheetId, (sId, fId) => {
      const field = (model as Model).getSheet(sId)?.getField(fId);
      if (field) {
        // todo: 只要知道最终引用到了哪个字段 或 结果类型就行，不必其中每个引用都查一下类型
        if (isCalcField(field.type)) {
          const fieldProps = field.config.renderFieldConfig?.props ?? {};
          if ('resultFieldType' in fieldProps) {
            return pick(fieldProps, ['resultFieldType', 'resultSheetId', 'resultFieldId']) as ResultFieldType;
          }
          return;
        }
        return { resultFieldType: field.type, resultSheetId: sId, resultFieldId: fId };
      }
    });

    resultFieldType = result?.resultFieldType;
    if (result && result.resultSheetId && result.resultFieldId) {
      resultFieldAddr = {
        sheetId: result.resultSheetId,
        fieldId: result.resultFieldId,
      };
    }
  }

  return { resultFieldType, resultFieldAddr };
};
