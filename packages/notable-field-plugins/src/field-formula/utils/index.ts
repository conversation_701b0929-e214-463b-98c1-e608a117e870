import {
  TEMPORAL_FORMAT,
  FilterOperator_Date,
  FilterOperator_Equal,
  FilterOperator_Exist,
  FilterOperator_Number,
  FilterOperator_TextContain,
  getFormulaLookupResultType,
  AutomationFieldConfig,
  IIncludeFormulaVolatileDate,
  AutomationFieldConfigCustomConfig,
  FieldDTO,
  getFormulaLookupResultFieldType,
  isObjectCellValue,
  isFxOriginObjectValue,
  getFormulaShowValue,
  isNil,
  FormulaFieldConfig,
  LookupFieldData,
  FilterUpFieldData,
  IFConfig,
  SheetId,
  FilterOperator,
  SortOperator,
  FieldTypes,
  GenericFieldPlugin,
  getConvertToStringParams,
  HeadlessFieldPlugin,
  IFieldOutputValue,
  ArrayUtils,
  getAutomationDateFieldOutputSchema,
  getAutomationDateFieldFormatType,
  TemporalFormat,
  isFlagOn,
  AutomationFlag,
  AutomationNumberDrillDownOutoutSchema,
  NOTABLE_CODE,
  FormulaLookupFieldResultFormat,
  TemporalFormatConfig,
  isSupportedResultFieldType,
} from '@ali/notable-common';
import { get } from 'lodash-es';
import { getCalcValuesFieldInfo } from '@ali/notable-core';
import { getCalcValuesField, getLookupValuesFieldByConfig } from '@ali/notable-model';
import type { FieldId, GetFieldFunc } from '@ali/notable-model';
import { NumberSortOperator, TextSortOperator } from '../../common/sort';
import {
  fromDateConditionCompareValue,
  fromNumberConditionCompareValue,
  fromTextConditionCompareValue,
} from '../../common/automation/fromConditionCompareValue';

const filterOpsMap = {
  number: [
    ...FilterOperator_Equal,
    ...FilterOperator_Number,
  ],
  date: [
    ...FilterOperator_Date,
    ...FilterOperator_Exist,
  ],
  others: [
    ...FilterOperator_Equal,
    ...FilterOperator_TextContain,
    ...FilterOperator_Exist,
  ],
};


export const tryCalcFieldConvertToDate: Required<AutomationFieldConfigCustomConfig<'formula' | 'filter-up'>>['tryConvertToDate'] = (field) => {
  const configProps = (field as FieldDTO<'filter-up' | 'formula'>)?.config?.renderFieldConfig?.props;

  // 如果标明了计算结果字段类型
  if (configProps?.resultFieldType) {
    // 保持和【更新时间】一致，不支持
    if (configProps?.resultFieldType === 'updatedTime') {
      return { format: undefined };
    }
  }

  const resultType = getFormulaLookupResultType(field);
  if (resultType === 'date') {
    const fieldProps = field.config?.renderFieldConfig?.props ?? {};
    const { formatter } = fieldProps as FormulaLookupFieldResultFormat;

    return {
      format: (formatter?.config as TemporalFormatConfig)?.format || TEMPORAL_FORMAT.HYPHEN_DATE,
    };
  }

  return { format: undefined };
};

export const getCalcFieldFilterOperators = (
  sheetId: SheetId,
  field: FieldDTO,
  getField: GetFieldFunc,
  getPlugin: (type: FieldTypes) => GenericFieldPlugin | null,
  filterConditionVersion: number | null,
): FilterOperator[] => {
  const configProps = (field as FieldDTO<'filter-up' | 'lookup' | 'formula'>)?.config?.renderFieldConfig?.props;
  if (configProps?.resultFieldType) {
    const { valuesField } = getCalcValuesField(sheetId, configProps, getField) || {};
    if (valuesField) {
      const plugin = getPlugin(valuesField.type);
      const calcFilterOperators = plugin?.getFilterOperators(sheetId, valuesField, getField, getPlugin, filterConditionVersion);
      if (calcFilterOperators) return calcFilterOperators;
    }
  }
  const resultType = getFormulaLookupResultType(field) || 'others';
  return filterOpsMap[resultType];
};

export const getCalcFieldSortOperators = (
  sheetId: SheetId,
  field: FieldDTO,
  getField: GetFieldFunc,
  getPlugin: (type: FieldTypes) => GenericFieldPlugin | null,
): SortOperator[] | undefined => {
  const configProps = (field as FieldDTO<'filter-up' | 'lookup' | 'formula'>)?.config?.renderFieldConfig?.props;
  if (configProps?.resultFieldType) {
    const { valuesField } = getCalcValuesField(sheetId, configProps, getField) || {};
    if (valuesField) {
      const plugin = getPlugin(valuesField.type);
      const calcSortOperators = plugin?.getSortOperators(sheetId, valuesField, getField, getPlugin);
      if (calcSortOperators) return calcSortOperators;
    }
  }
  const resultType = getFormulaLookupResultType(field);
  if (resultType === 'date' || resultType === 'number') {
    return NumberSortOperator;
  }
  return TextSortOperator;
};

export const getCalcValueFieldConfig = (
  valuesField: FieldDTO | null,
  resultFieldType?: FieldTypes,
  config?: IFConfig<FormulaFieldConfig | LookupFieldData | FilterUpFieldData>,
) => {
  const configProps = config?.renderFieldConfig?.props;
  if (!configProps) return config;
  if (valuesField && resultFieldType) {
    const currentConfig = {
      renderFieldConfig: {
        props: configProps?.formatter?.config,
      },
      extendInfo: valuesField?.config,
    } as FieldDTO['config'];
    if (resultFieldType === valuesField.type
      && (valuesField.type === 'date' || valuesField.type === 'createdTime' || valuesField.type === 'updatedTime')) {
      return currentConfig;
    }
    if (resultFieldType === valuesField.type && valuesField.type === 'number') {
      return currentConfig;
    }
    return valuesField?.config;
  }
  return config;
};

type GetFxFieldSchemaFunc<T extends FieldTypes> = NonNullable<AutomationFieldConfig<T>['getOutputSchema']>;

const getLookupFieldOutputSchema: GetFxFieldSchemaFunc<'formula' | 'lookup' | 'filter-up'> = (field, ctx) => {
  const resultFieldType = getFormulaLookupResultFieldType(field);
  if (resultFieldType) {
    if (!field || !field.config?.renderFieldConfig?.props) return;
    const valuesFieldInfo = getCalcValuesFieldInfo(ctx.sheetId, field.config?.renderFieldConfig?.props, ctx.getField);
    if (!valuesFieldInfo) return;
    const plugin = ctx.getPlugin(resultFieldType);
    if (!plugin) return;
    const { automation: automationConfig } = plugin;
    if (!automationConfig?.getOutputSchema) return;
    const { field: valuesField, sheetId: sId } = valuesFieldInfo;
    const context = { ...ctx, sheetId: sId };
    const schema = (automationConfig.getOutputSchema as GetFxFieldSchemaFunc<FieldTypes>)(valuesField, context);
    if (!schema) return;
    // 本身就支持返回多值的字段，原样返回
    if (schema.type === 'array') return schema;
    return {
      type: 'array',
      items: schema,
    };
  }
};

const isValidValuesField = (
  targetSheetId: string,
  valuesField: string,
  getField: (sheetId: string, fieldId: string) => FieldDTO | null,
) => {
  if (!valuesField) {
    return false;
  }

  if (!targetSheetId) {
    return false;
  }

  const field = getField(targetSheetId, valuesField);

  if (!field) {
    return false;
  }

  return isSupportedResultFieldType(field.type);
};

const judgeCompatibleFollowSource = (
  params: {
    resultFieldType?: FieldTypes;
    sheetId: string;
    field: FieldDTO<'formula' | 'lookup' | 'filter-up'>;
    getField: (sheetId: string, fieldId: string) => FieldDTO | null;
  },
) => {
  const { resultFieldType, sheetId, field, getField } = params;

  if (!resultFieldType) {
    return false;
  }

  if (field.type === 'formula') {
    return false;
  }

  const agg = field.config.renderFieldConfig?.props?.aggregator;
  if (agg !== 'VALUES' && agg !== 'UNIQUE') {
    return false;
  }

  if (field.type === 'filter-up') {
    return isValidValuesField(
      field.config.renderFieldConfig?.props?.targetSheet || '',
      field.config.renderFieldConfig?.props?.valuesField || '',
      getField,
    );
  }

  if (field.type === 'lookup') {
    const res = getLookupValuesFieldByConfig(
      sheetId,
      field.config.renderFieldConfig?.props || null,
      getField,
    );
    return isValidValuesField(
      res?.sheetId || '',
      res?.field?.id || '',
      getField,
    );
  }

  return false;
};

export const getFxFieldOutputSchema: AutomationFieldConfig<'formula' | 'lookup' | 'filter-up'>['getOutputSchema'] = (field, ctx) => {
  // 二期（公式服务端化支持引用字段）灰度开启 output 输出当前计算字段
  // 一期灰度开启 仅输出不带引用的计算字段
  // 一期二期均未开启，自动化不会出现计算字段，理论上以下代码不会调用
  if (ctx.automationGrays.enableCalcField || ctx.getFxEngine()?.isFieldFormulaAndNotReferingLookup(ctx.sheetId, field.id)) {
    const resultFieldType = getFormulaLookupResultFieldType(field);
    const isCompatibleFollowSource = judgeCompatibleFollowSource({
      resultFieldType,
      sheetId: ctx.sheetId,
      field,
      getField: ctx.getField,
    });

    // 在公式原值引用放量前，先控制仅查找引用-原值引用走此逻辑
    if (isCompatibleFollowSource) {
      return getLookupFieldOutputSchema(field, ctx);
    }

    const resultType = getFormulaLookupResultType(field);
    if (resultType === 'date') {
      const formatter = get(field, 'config.renderFieldConfig.props.formatter') as TemporalFormat;
      if (formatter?.type === 'date') {
        const f = getAutomationDateFieldFormatType(formatter.value, 'java');
        return getAutomationDateFieldOutputSchema(f);
      }
      return getAutomationDateFieldOutputSchema();
    }
    if (field.type === 'filter-up' || field.type === 'lookup') {
      // 不支持原值的场景返回showValue
      return { type: 'string' as const };
    }
    if (resultType === 'number') {
      const { flags } = ctx;
      if (isFlagOn<AutomationFlag>(flags, AutomationFlag.NUMBER_DRILL_DOWN)) {
        return AutomationNumberDrillDownOutoutSchema;
      }
      return { type: 'number' };
    }
    // 公式自动化在非原值场景下返回showValue
    return { type: 'string' as const };
  }
};

type GetFxFieldOutputValueFunc<T extends FieldTypes> = NonNullable<AutomationFieldConfig<T>['getOutputValue']>;
type GetFxFieldConditionBranchVarFunc<T extends FieldTypes> =
  NonNullable<NonNullable<AutomationFieldConfig<T>['customConfig']>['getConditionBranchVarValue']>;
type FromFxFieldConditionCompareValueFunc<T extends FieldTypes> =
NonNullable<NonNullable<AutomationFieldConfig<T>['customConfig']>['fromConditionCompareValue']>;

export const getFxFieldOutputValue: GetFxFieldOutputValueFunc<'formula' | 'lookup' | 'filter-up'> = async (field, cellValue, ctx) => {
  const resultFieldType = getFormulaLookupResultFieldType(field);
  const isCompatibleFollowSource = judgeCompatibleFollowSource({
    resultFieldType,
    sheetId: ctx.sheetId,
    field,
    getField: ctx.getField,
  });

  const calcFormulaShowValue = () => {
    if (isNil(cellValue)) return null;
    return getFormulaShowValue(cellValue, field.config?.renderFieldConfig?.props);
  };

  if (!isCompatibleFollowSource || !isObjectCellValue(cellValue)) {
    // 不支持原值的场景返回showValue
    return calcFormulaShowValue();
  }
  try {
    if (!isFxOriginObjectValue(cellValue)) {
      return calcFormulaShowValue();
    }
    // resultFieldType 在 isCompatibleFollowSource 中已经判断过了
    const plugin = ctx.getPlugin(resultFieldType || '');
    if (!plugin) return null;
    const { automation: automationConfig } = plugin;
    if (!automationConfig) return null;
    if (field && field.config?.renderFieldConfig?.props) {
      const valuesFieldInfo = getCalcValuesFieldInfo(ctx.sheetId, field.config?.renderFieldConfig?.props, ctx.getField);
      if (!valuesFieldInfo) return null;
      const { field: valuesField, sheetId: sId } = valuesFieldInfo;
      let outputValues: IFieldOutputValue[] = [];
      if (automationConfig.getOutputValue) {
        const context = { ...ctx, sheetId: sId };
        outputValues = await Promise.all(cellValue.refData.value.map(async (it) => {
          const outputValue = await (automationConfig.getOutputValue as GetFxFieldOutputValueFunc<FieldTypes>)(valuesField, it, context);
          return outputValue;
        }));
        // 如果返回引用结果为一组多个数字，则只返回文本格式的结果，不返回数字格式结果
        if (outputValues.length > 1) {
          outputValues = outputValues.map((value) => {
            if (typeof value === 'object' && value && 'number' in value) {
              delete value.number;
            }
            return value;
          });
        }
      } else if (cellValue) {
        outputValues = cellValue.refData.value.map((it) => {
          const convertToString = getConvertToStringParams(valuesField, it);
          const outputValue = (plugin as HeadlessFieldPlugin<FieldTypes>).toString(convertToString);
          return outputValue;
        }).filter((v) => !isNil(v)) as string[];
      }
      if (outputValues.length > 0) {
        const finalValues: IFieldOutputValue[] = [];
        outputValues.forEach((v) => {
          if (isNil(v)) return;
          // 本身就支持返回多值的字段，拍平返回
          ArrayUtils.safePush(finalValues, Array.isArray(v) ? v as string[] : [v]);
        });
        if (finalValues.length > 0) return finalValues;
        return null;
      }
      return null;
    } else {
      return calcFormulaShowValue();
    }
  } catch {
    return calcFormulaShowValue();
  }
};

export const getFieldFxInfo: NonNullable<NonNullable<AutomationFieldConfig['customConfig']>['getFxInfo']> = (
  sId: string,
  fId: string,
  getField: (sheetId: SheetId, fieldId: FieldId) => FieldDTO | null,
  getFxEngine,
) => {
  const field = getField(sId, fId) as FieldDTO<'filter-up' | 'lookup' | 'formula'> | null;
  const fxEngine = getFxEngine();
  let includeFormulaVolatileDate: IIncludeFormulaVolatileDate | undefined;
  if (fxEngine && field && ['filter-up', 'lookup', 'formula'].includes(field?.type)) {
    includeFormulaVolatileDate = {
      hasNow: fxEngine.isCalcFieldFinallyCallingFunction({ sheetId: sId, fieldId: fId }, ['NOW']),
      hasToday: fxEngine.isCalcFieldFinallyCallingFunction({ sheetId: sId, fieldId: fId }, ['TODAY']),
    };
  }

  if (field?.type === 'formula') {
    return { sheetId: sId, fieldId: fId, includeFormulaVolatileDate };
  }

  const configProps = field?.config?.renderFieldConfig?.props;
  const resultFieldType = configProps?.resultFieldType;
  if (!resultFieldType) return { sheetId: sId, fieldId: fId, includeFormulaVolatileDate };
  const fieldInfo = getCalcValuesFieldInfo(sId, configProps, getField);
  if (!fieldInfo) return { sheetId: sId, fieldId: fId, includeFormulaVolatileDate };
  return {
    sheetId: fieldInfo.sheetId,
    fieldId: fieldInfo.fieldId,
    includeFormulaVolatileDate,
  };
};

const getLookupFieldConditionBranchVarValue: GetFxFieldConditionBranchVarFunc<'formula' | 'lookup' | 'filter-up'> = (jsonPathVar, ctx) => {
  const { field, sheetId, getField, getPlugin } = ctx;
  const resultFieldType = getFormulaLookupResultType(field);
  if (resultFieldType) {
    if (!field || !field.config?.renderFieldConfig?.props) return { string: jsonPathVar };
    const valuesFieldInfo = getCalcValuesFieldInfo(sheetId, field.config?.renderFieldConfig?.props, getField);
    if (!valuesFieldInfo) return { string: jsonPathVar };
    const plugin = getPlugin(resultFieldType);
    const { automation: automationConfig } = plugin || {};
    const isSearchRecordsOutput = ctx.renderCode === NOTABLE_CODE.CODE_DOCS_NOTABLE_ACTION_SEARCH_RECORDS;
    if (!automationConfig?.customConfig?.getConditionBranchVarValue) {
      return isSearchRecordsOutput ?
        { array: { string: jsonPathVar as unknown as string[] } } :
        { string: jsonPathVar };
    }
    const { field: valuesField, sheetId: sId } = valuesFieldInfo;
    const varValue = (automationConfig.customConfig.getConditionBranchVarValue as GetFxFieldConditionBranchVarFunc<FieldTypes>)(
      jsonPathVar, { ...ctx, field: valuesField, sheetId: sId },
    );
    return varValue;
  }
  return { string: jsonPathVar };
};

export const getFxFieldConditionBranchVarValue: GetFxFieldConditionBranchVarFunc<'formula' | 'lookup' | 'filter-up'> = (jsonPathVar, ctx) => {
  const resultFieldType = getFormulaLookupResultFieldType(ctx.field);
  if (resultFieldType) {
    return getLookupFieldConditionBranchVarValue(jsonPathVar, ctx);
  }
  const isSearchRecordsOutput = ctx.renderCode === NOTABLE_CODE.CODE_DOCS_NOTABLE_ACTION_SEARCH_RECORDS;
  const resultType = getFormulaLookupResultType(ctx.field);
  if (resultType === 'number') {
    return isSearchRecordsOutput ?
      { array: { number: jsonPathVar as unknown as number[] } } :
      { number: jsonPathVar as unknown as number };
  } else {
    return isSearchRecordsOutput ? { array: { string: jsonPathVar as unknown as string[] } } : { string: jsonPathVar };
  }
};

export const fromFxFieldConditionCompareValue: FromFxFieldConditionCompareValueFunc<'formula'> = (inputValue, field) => {
  const resultType = getFormulaLookupResultType(field);
  if (resultType === 'date') {
    return fromDateConditionCompareValue(inputValue, field);
  }
  if (resultType === 'number') {
    return fromNumberConditionCompareValue(inputValue, field);
  }
  return fromTextConditionCompareValue(inputValue, field);
};
