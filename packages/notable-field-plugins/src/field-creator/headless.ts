import $i18n from '@ali/notable-i18n';
import {
  CellDataType,
  FieldType,
  FilterOperator_Equal,
  HeadlessFieldPlugin,
  FilterOperator,
  AutomationFieldConfig,
  isFlagOn,
  AutomationFlag,
} from '@ali/notable-common';
import { ObjectCellValue } from '@ali/notable-model';
import { Api, NotableCore } from '@ali/notable-core';
import { TextSortOperator } from '../common/sort';
import { FieldHeadlessPluginCreator } from '../common/types';
import { parseData } from '../utils';
import { getPersonOutputSchema } from '../common/automation';
import { consumeRefDataToPersonValue, toPersonSyncField } from '../common/sync';
import { toString, toCellValue, createField, getDefaultFieldConfig } from './converter';

export interface CreatorData {
  avatarUrl: string;
  uid: number;
  nick: string;
}

export const createCreatorFieldHeadlessPlugin: FieldHeadlessPluginCreator<'creator'> = (_: Api) =>
  new CreatorFieldHeadlessPlugin();

export class CreatorFieldHeadlessPlugin implements Partial<HeadlessFieldPlugin<'creator'>> {
  type = 'creator' as const;
  dataType = CellDataType.OBJECT;
  fieldType = FieldType.ADVANCED;
  get typeName() {
    return $i18n.t('notable_notableFields_Creator');
  }

  toString = toString;
  toCellValue = toCellValue;
  createField = createField;
  getDefaultFieldConfig = getDefaultFieldConfig;

  sync = creatorSync;
  automation = AutomationConfig;
  // 支持按字段拉取行记录之后上
  // openApi = SystemUserOpenApiConfig as OpenApiFieldConfig<'creator', ICore>;
  getFilterOperators = () => filterOperators;
  getSortOperators = () => TextSortOperator;
  isCellReadonly = true;
  autoFill = {
    canAutoFill: () => false,
  };
}

const filterOperators: FilterOperator[] = [...FilterOperator_Equal, 'ANY_OF', 'NONE_OF'];

const creatorSync: HeadlessFieldPlugin<'creator'>['sync'] = {
  toSyncField(context) {
    return toPersonSyncField(context.field, context.core as NotableCore);
  },
  consumeRefDataValue: consumeRefDataToPersonValue,
};

const AutomationConfig: AutomationFieldConfig<'creator'> = {
  getOutputSchema: getPersonOutputSchema,
  getOutputValue: async (_1, cellValue, ctx) => {
    if (cellValue === null) {
      return [];
    }

    const dataStr = (cellValue as ObjectCellValue).data;
    const data = parseData(dataStr, {}) as CreatorData;
    if (!data.uid) return [];
    const result = await ctx.hooks.getUserInfo(String(data.uid));
    if (!result) return [];
    return [result.id];
  },
  getFilterOperators: () => filterOperators,
  customConfig: {
    getConditionBranchVarValue: (jsonPathVar) => {
      return { array: { user: [jsonPathVar] } };
    },
  },
};
