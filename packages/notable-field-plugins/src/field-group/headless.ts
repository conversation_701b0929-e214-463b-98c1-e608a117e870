import { set, uniq } from 'lodash-es';
import $i18n from '@ali/notable-i18n';
import {
  AutomationConversationFieldOutputSchema,
  AutomationFieldConfig,
  CellDataType,
  FieldDTO,
  FieldType,
  FilterOperator,
  HeadlessFieldPlugin,
  ObjectCellValue,
  isNil,
  limitations,
  serializeGroup,
  GroupInfo,
  deserializeGroup,
  OpenApiFieldConfig,
  OpenApi,
  ICore,
  isDateOperator,
  GetDefaultValueContext,
} from '@ali/notable-common';
import { APIException, type NotableCore } from '@ali/notable-core';
import { TextSortOperator } from '../common';
import { LIST_FILTER_OPERATORS } from '../common/filter';
import { toString, toCellValue, createField, getDefaultFieldConfig } from './converter';

export class GroupFieldHeadlessPlugin implements Partial<HeadlessFieldPlugin<'group'>> {
  type = 'group' as const;
  dataType = CellDataType.OBJECT;
  fieldType = FieldType.BASIC;
  canToPrimary = false;
  get typeName() {
    return $i18n.t('notable_notableFields_group');
  }

  toString = toString;
  toCellValue = toCellValue;
  createField = createField;
  getDefaultFieldConfig = getDefaultFieldConfig;

  automation = AutomationConfig;
  openApi = OpenApiConfig as OpenApiFieldConfig<'group', ICore>;

  getDefaultValue = (context: GetDefaultValueContext) => {
    const config = context.targetField?.config as FieldDTO<'group'>['config'] | undefined;
    if (config?.decorator) return;
    return config?.renderFieldConfig?.props?.defaultValue;
  };
  validateAndCompleteConfig = (config: FieldDTO<'group'>['config']) => {
    if (isNil(config.renderFieldConfig?.props?.allowMultiSelect)) {
      set(config, ['renderFieldConfig', 'props', 'allowMultiSelect'], true);
      return config;
    }
  };

  getFilterOperators = (): FilterOperator[] => LIST_FILTER_OPERATORS;
  getSortOperators = () => TextSortOperator;

  sync: HeadlessFieldPlugin<'group'>['sync'] = {
    consumeRefDataValue(values) {
      const groups: GroupInfo[] = [];
      values.forEach((value) => {
        try {
          const list = deserializeGroup(value as ObjectCellValue);
          groups.push(...list);
        } catch {
          // do nothing
        }
      });
      if (groups.length === 0) return null;
      return serializeGroup(groups);
    },
  };
}

export const createGroupFieldHeadlessPlugin = () => new GroupFieldHeadlessPlugin();

const AutomationConfig: AutomationFieldConfig<'group'> = {
  getOutputSchema: () => ({ type: 'array', items: AutomationConversationFieldOutputSchema }),
  getOutputValue: async (_, cellValue, ctx) => {
    if (cellValue === null) {
      return [];
    }

    const groups = deserializeGroup(cellValue as ObjectCellValue);
    const cidList = groups.map(({ cid }) => cid);
    const openCids = await Promise.all(cidList.map((cid) => ctx.hooks.getOpenConversationId(cid)));
    return openCids.filter((openCid): openCid is string => !!openCid);
  },
  fromInputValue: async (inputValue, field, ctx) => {
    const value = inputValue.array?.conversation;
    if (isNil(value)) {
      return null;
    }

    const openCids: string[] = [];
    if (field.config.renderFieldConfig?.props?.allowMultiSelect) {
      openCids.push(...Array.from(new Set(value)));
    } else if (value.length > 0) {
      // 不支持多选的话，只取第一个值
      openCids.push(value[0]);
    }

    if (openCids.length > limitations.cellGroupMaxCount) {
      throw new Error(`The input value is too many for group field. The count limit is ${limitations.cellGroupMaxCount}`);
    }

    const cids = await Promise.all(openCids.map((openCid) => ctx.hooks.getCid(openCid)));
    const groups = await Promise.all(cids.filter((cid): cid is string => !!cid).map((cid) => ctx.hooks.getConversation(cid)));
    return {
      dataType: CellDataType.OBJECT,
      value: serializeGroup(groups.filter((group): group is GroupInfo => !!group)),
    };
  },
  getFilterOperators: () => LIST_FILTER_OPERATORS,
  customConfig: {
    getConditionBranchVarValue: (jsonPathVar) => {
      return { array: { conversation: [jsonPathVar] } };
    },
    fromConditionCompareValue: (inputValue) => {
      const value = inputValue.conversation || inputValue.array?.conversation;
      if (isNil(value) || (Array.isArray(value) && !value.filter((v) => !isNil(v)).length)) {
        return null;
      }
      return {
        dataType: CellDataType.OBJECT,
        value: serializeGroup((Array.isArray(value) ? value : [value]).filter((v) => !isNil(v)).map((v) => ({ cid: v, name: v }))),
      };
    },
  },
};

const OpenApiConfig: OpenApiFieldConfig<'group', NotableCore> = {
  convertApiFieldOptionToConfig: (fieldProperty, field) => {
    const { multiple } = fieldProperty;
    if (typeof multiple !== 'boolean' && multiple !== undefined) {
      throw new APIException('invalid_argument', `user config multiple: ${multiple} is invalid`);
    }
    const { renderFieldConfig } = field?.config || {};
    const { props } = renderFieldConfig || {};
    const allowMultiSelect = fieldProperty.multiple ?? props?.allowMultiSelect ?? true;
    return { ...renderFieldConfig, props: { ...props, allowMultiSelect } };
  },
  convertConfigToApiFieldOption: (field) => {
    const { allowMultiSelect = true } = field.config.renderFieldConfig?.props || {};
    return { multiple: allowMultiSelect };
  },
  convertToApiCellValue: (cell, _, ctx) => {
    if (cell.dataType === CellDataType.OBJECT) {
      const groups = deserializeGroup(cell.value);
      const cids = groups.map(({ cid }) => cid);
      const conversationIds = cids.map((cid) => {
        const conversationId = ctx.core.addonController?.rowsProcessor?.getConversationId?.(cid);
        return conversationId;
      }).filter((conversation) => !!conversation) as OpenApi.GroupsCellValue;
      return conversationIds || null;
    }
    return null;
  },
  convertApiCellValueToCell: async (value, field, ctx) => {
    if (!value) return undefined;
    const { core, fieldIdentifier } = ctx;
    if (!Array.isArray(value)) {
      throw new APIException('invalid_argument', `the value '${JSON.stringify(value)}' is invalid for field '${fieldIdentifier}'`);
    }
    if (!value.length) {
      return null;
    }
    const multiple = !(field.config.renderFieldConfig?.props?.allowMultiSelect === false);

    if (!OpenApi.isGroupsCellValue(value)) {
      throw new APIException('invalid_argument', `the value '${JSON.stringify(value)}' is invalid for field '${fieldIdentifier}'`);
    }
    if (!core.addonController?.serviceHooks.getConversationInfo) throw new APIException('invalid_argument', 'unsupported field type: group');
    // 这里输入的人员不会重复，所以并行查询
    const cids = await Promise.all(((multiple ? value : [value[0]])).map(async (item) => {
      if (typeof item !== 'object') {
        throw new APIException('invalid_argument', `the value '${JSON.stringify(value)}' is invalid for field '${fieldIdentifier}'`);
      }
      const { cid, openConversationId } = item;
      if (cid) {
        return cid;
      }
      if (openConversationId) {
        // 通过非open api的途径写入unionId时，可能无法获取调用方信息，此时不支持通过传入unionId写入人员字段
        if (!core.addonController?.serviceHooks?.getCidByOpenCid) {
          throw new APIException('not_found', 'unable to identify API caller');
        }
        const result = await core.addonController.serviceHooks.getCidByOpenCid(openConversationId);
        if (result?.success) {
          if (!result.response) {
            throw new APIException('not_found', `the value item '${JSON.stringify(item)}' is invalid for field '${fieldIdentifier}'`);
          }
          return result.response;
        } else {
          throw new APIException('internal_error', `the value '${JSON.stringify(value)}' is failed to convert to cids'`);
        }
      }
    }));
    const result = await Promise.all(uniq(cids)
      .filter((cid): cid is string => !!cid)
      .map(async (cid) => {
        const conversationInfoResult = await core.addonController?.serviceHooks?.getConversationInfo?.(cid);
        if (conversationInfoResult?.success) {
          return conversationInfoResult.response;
        }
        return null;
      }));
    const conversationInfos = result.filter((conversationInfo): conversationInfo is GroupInfo => !!conversationInfo);
    if (conversationInfos.length > limitations.cellGroupMaxCount) {
      throw new APIException(
        'invalid_argument',
        `the value is too many for field '${fieldIdentifier}'. The count limit is ${limitations.cellGroupMaxCount}`,
      );
    }

    return {
      dataType: CellDataType.OBJECT,
      value: serializeGroup(conversationInfos),
    };
  },
  convertToCoreFilterCondition: async (_, operator, value, ctx) => {
    if (isDateOperator(operator)) {
      throw new APIException(
        'invalid_argument',
        `operator '${operator}' is invalid condition for field '${ctx.fieldIdentifier}'`,
      );
    }

    const cids = new Set<string>();
    if (!OpenApi.isGroupsConditionValue(value)) {
      throw new APIException(
        'invalid_argument',
        `the value item '${JSON.stringify(value)}' is invalid condition for field '${ctx.fieldIdentifier}'`,
      );
    }
    for (const val of value) {
      const { cid, openConversationId } = val;
      if (cid) {
        cids.add(cid);
      } else if (openConversationId) {
        const res = await ctx.core.addonController?.serviceHooks.getCidByOpenCid?.(openConversationId);
        if (!res?.success) {
          throw new APIException(
            'not_found',
            `the value '${JSON.stringify(value)}' is invalid condition for field '${ctx.fieldIdentifier}'`,
          );
        }
        if (!res.response) {
          throw new APIException(
            'not_found',
            `the value item '${JSON.stringify(val)}' is invalid condition for field '${ctx.fieldIdentifier}'`,
          );
        }

        res.response && cids.add(res.response);
      } else {
        throw new APIException(
          'not_found',
          `the value item '${JSON.stringify(val)}' is invalid condition for field '${ctx.fieldIdentifier}'`,
        );
      }
    }

    return {
      symbol: operator,
      value: Array.from(cids),
    };
  },
  convertToCoreFilterOperator: (operator) => {
    switch (operator) {
      case 'equal':
      case 'notEqual':
      case 'empty':
      case 'notEmpty':
        return OpenApi.commonConvertToCoreFilterOperator(operator);
      case 'contain':
        return 'ANY_OF';
      case 'notContain':
        return 'NONE_OF';
      default:
        return null;
    }
  },
};
