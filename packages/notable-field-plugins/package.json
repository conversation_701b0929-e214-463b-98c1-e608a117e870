{"name": "@ali/notable-field-plugins", "version": "0.36.0", "main": "src/index.ts", "sideEffects": ["./src/locale/*.ts", "./locale/*.js"], "publishConfig": {"registry": "https://registry.anpm.alibaba-inc.com"}, "author": "alidocs panda", "scripts": {"reinstall": "rm -rf node_modules pnpm-lock.yaml && pnpm i", "clean": "rm -rf pkg build", "start": "we start-storybook -p 9000", "build-storybook": "we build-storybook", "build": "we build-component  && cp -r assets pkg/assets", "test": "echo test", "deploy": "we deploy-pages -d build/ -b demo -m 'deloy. to #xxx'", "site": "pnpm build-storybook && pnpm deploy", "typechecking": "we typechecking", "pub": "pnpm build && tnpm publish pkg", "coverage": "we jest --coverage --passWithNoTests", "update-i18n": "update-i18n ./localesKeys.js ./src/i18nResources -w 'zh_CN|en_US|zh_TW|zh_HK|ja_JP' --moreLangs ko_KR,tr_TR,pt_BR,th_TH,id_ID,ms_MY -l ts -n notable-sdk", "lint": "we eslint --quiet && we stylelint"}, "dependencies": {"@ali/dd-ipaas-field-render": "*", "@ali/dd-ipaas-utils": "*", "@ali/notable-common": "0.36.0", "@ali/notable-components": "0.36.0", "@ali/notable-core": "0.36.0", "@ali/notable-design": "0.36.0", "@ali/notable-i18n": "0.36.0", "@ali/notable-model": "0.36.0", "@ali/notable-utils": "0.36.0", "@ali/we-assets": "0.0.6-beta.aba4d0.202506171637", "@ali/we-icons": "0.0.6-beta.aba4d0.202506171637", "@ali/we-icons-3": "0.0.6-beta.aba4d0.202506171637", "@ali/we-notable-cangjie-serializer": "0.0.4", "@ali/zongheng-common": "2.22.250312-beta.6251617", "@amap/amap-jsapi-loader": "1.0.1", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "classnames": "^2.2.1", "dayjs": "1.10.6", "dingtalk-design-mobile": "2.6.4", "mobx-react-lite": "3.4.3", "react-infinite-scroll-component": "^6.1.0", "react-textarea-autosize": "^8.3.3", "react-virtualized-auto-sizer": "^1.0.7", "react-window": "^1.8.8", "regression": "2.0.1", "resize-observer-polyfill": "^1.5.1"}, "devDependencies": {"@ali/dingtalk-medusa": "^10.1.0", "@ali/we-cli": "1.1.78-beta.31", "@types/lodash-es": "4.17.7", "@types/react-virtualized-auto-sizer": "^1.0.1", "@types/react-window": "^1.8.5", "@types/styled-components": "^5.1.25"}, "peerDependencies": {"@ali/we-design-next": "0.51.1", "@ali/we-design-token": "0.7.0", "@babel/runtime": "7.x", "ahooks": "3.7.6", "lodash-es": "*", "react": "17.x", "react-dom": "17.x", "styled-components": "^5.2.1"}}