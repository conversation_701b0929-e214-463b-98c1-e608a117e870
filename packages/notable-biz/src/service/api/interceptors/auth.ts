import axios from 'axios';
import { isPre, globalVar } from '../../../common';

/**
 * 协同引擎轻量的 Token 刷新接口
 */
export const getAccessToken = async () => {
  const url = `https://${isPre ? 'pre-' : ''}collab.dingtalk.com/api/collab/v2/token/refresh`;
  const response = await axios({
    method: 'POST',
    url,
    headers: { 'A-TOKEN': globalVar.get('token') },
    timeout: 10 * 1000,
    withCredentials: true,
  });
  return response?.data?.data;
};
