import { globalVar } from '../../../common';
import { getAccessToken } from './auth';

export type TuplesElement<T extends unknown[], I extends number> = T[I];

export const A_TOKEN_FIELD = 'A-TOKEN';

export const TRACE_ID_TRAIT = 'eagleeye-traceid';

let cachedToken: string | null = null;
let refreshPromise: Promise<string | null> | null = null;

export const getAuthToken = async (): Promise<string | null> => {
  if (cachedToken) return cachedToken;

  // If already refreshing, wait for the existing refresh to complete
  if (refreshPromise) {
    try {
      const timeout = new Promise<string | null>((_, reject) => {
        setTimeout(() => reject(new Error('Timeout after 5s')), 5000);
      });
      const token = await Promise.race([refreshPromise, timeout]);
      return token;
    } catch {
      return null;
    }
  }

  return renewAuthToken();
};

export const renewAuthToken = async (): Promise<string | null> => {
  if (refreshPromise) {
    return refreshPromise;
  }

  refreshPromise = (async () => {
    try {
      cachedToken = null;
      const token = await getAccessToken();
      if (token) {
        cachedToken = token;
        return token;
      } else {
        throw new Error('Failed to get access token');
      }
    } catch (error) {
      cachedToken = null;
      throw error;
    } finally {
      refreshPromise = null;
    }
  })();

  try {
    return await refreshPromise;
  } catch {
    return null;
  }
};

export const setAuthToken = (token: string) => {
  globalVar.set('token', token);
  cachedToken = token;
  refreshPromise = null;
};
